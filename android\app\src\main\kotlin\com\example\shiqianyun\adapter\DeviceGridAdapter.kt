package com.example.shiqianyun.adapter

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.util.Base64
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.example.shiqianyun.R
import com.example.shiqianyun.network.model.DeviceInfo
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

/**
 * 设备网格适配器
 */
class DeviceGridAdapter(
    private val devices: List<DeviceInfo>,
    private val onItemClick: (DeviceInfo) -> Unit,
    private val onSettingsClick: (DeviceInfo) -> Unit,
    private val onLoadScreenshot: ((DeviceInfo, (String?) -> Unit) -> Unit)? = null
) : RecyclerView.Adapter<DeviceGridAdapter.ViewHolder>() {

    // 存储设备截图的缓存
    private val screenshotCache = mutableMapOf<String, String>()
    // 存储已处理图片的缓存，避免重复处理
    private val processedImageCache = mutableMapOf<String, String>()

    /**
     * 清空截图缓存
     */
    fun clearScreenshotCache() {
        screenshotCache.clear()
        processedImageCache.clear()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_device_grid, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val device = devices[position]
        holder.bind(device)
        
        // 卡片主体点击连接设备
        holder.cardView.setOnClickListener {
            onItemClick(device)
        }
        
        // 设置按钮点击显示详情弹窗
        holder.btnSettings.setOnClickListener {
            // 阻止事件传递给卡片
            it.isClickable = true
            onSettingsClick(device)
        }
        
        // 预览图点击也直接连接设备
        holder.imgPreview.setOnClickListener {
            onItemClick(device)
        }
        
        // 加载设备截图
        loadDeviceScreenshot(device, holder.imgPreview)
    }

    override fun getItemCount(): Int = devices.size
    
    /**
     * 加载设备截图
     */
    private fun loadDeviceScreenshot(device: DeviceInfo, imageView: ImageView) {
        // 设备唯一标识
        val deviceKey = "${device.phone_identify}_${device.consul_id}"
        
        // 检查缓存中是否有截图
        if (screenshotCache.containsKey(deviceKey)) {
            // 从缓存中加载
            val base64Image = screenshotCache[deviceKey]
            if (base64Image != null) {
                loadImageFromBase64(base64Image, imageView)
            }
            return
        }
        
        // 如果没有提供加载截图的回调，则使用默认图片
        if (onLoadScreenshot == null) {
            imageView.setImageResource(R.drawable.ic_device_preview)
            return
        }
        
        // 请求加载截图
        onLoadScreenshot.invoke(device) { base64Image ->
            if (base64Image != null) {
                // 缓存截图
                screenshotCache[deviceKey] = base64Image
                
                // 在UI线程中更新ImageView
                imageView.post {
                    loadImageFromBase64(base64Image, imageView)
                }
            } else {
                // 加载失败，设置默认图片
                imageView.post {
                    imageView.setImageResource(R.drawable.ic_device_preview)
                }
            }
        }
    }
    
    /**
     * 从Base64加载图片
     */
    private fun loadImageFromBase64(base64Image: String, imageView: ImageView) {
        try {
            // 检查是否已经处理过这个图片
            val imageHash = base64Image.hashCode().toString()
            val cachedProcessedImage = processedImageCache[imageHash]
            
            if (cachedProcessedImage != null) {
                // 使用缓存的已处理图片，直接加载
                Glide.with(imageView.context)
                    .load(cachedProcessedImage)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .skipMemoryCache(false)
                    .dontAnimate()
                    .into(imageView)
                return
            }
            
            // 在后台线程处理图片，避免主线程阻塞
            Thread {
                try {
                    val processedImage = processImageOrientation(base64Image)
                    
                    // 缓存处理后的图片
                    processedImageCache[imageHash] = processedImage
                    
                    // 回到主线程更新UI
                    imageView.post {
                        // 使用Glide加载处理后的图片，优化配置减少闪烁
                        Glide.with(imageView.context)
                            .load(processedImage)
                            .diskCacheStrategy(DiskCacheStrategy.ALL) // 启用磁盘缓存
                            .skipMemoryCache(false) // 启用内存缓存
                            .dontAnimate() // 禁用动画避免闪烁
                            .into(imageView)
                    }
                } catch (e: Exception) {
                    // 处理失败，在主线程设置默认图片
                    imageView.post {
                        imageView.setImageResource(R.drawable.ic_device_preview)
                    }
                }
            }.start()
        } catch (e: Exception) {
            // 加载失败，设置默认图片
            imageView.setImageResource(R.drawable.ic_device_preview)
        }
    }
    
    /**
     * 处理图片方向，如果是横屏则顺时针旋转90度
     */
    private fun processImageOrientation(base64Image: String): String {
        try {
            // 移除data:image前缀（如果存在）
            val base64Data = if (base64Image.startsWith("data:image")) {
                base64Image.substring(base64Image.indexOf(",") + 1)
            } else {
                base64Image
            }
            
            // 解码Base64为Bitmap
            val decodedBytes = Base64.decode(base64Data, Base64.DEFAULT)
            val originalBitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
                ?: return base64Image // 解码失败，返回原图
            
            // 检查是否为横屏（宽度大于高度）
            val isLandscape = originalBitmap.width > originalBitmap.height
            
            if (isLandscape) {
                // 创建旋转矩阵，顺时针旋转90度
                val matrix = Matrix()
                matrix.postRotate(90f)
                
                // 应用旋转
                val rotatedBitmap = Bitmap.createBitmap(
                    originalBitmap, 0, 0, 
                    originalBitmap.width, originalBitmap.height, 
                    matrix, true
                )
                
                // 将旋转后的Bitmap转换回Base64
                val outputStream = java.io.ByteArrayOutputStream()
                rotatedBitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream)
                val rotatedBytes = outputStream.toByteArray()
                val rotatedBase64 = Base64.encodeToString(rotatedBytes, Base64.DEFAULT)
                
                // 清理资源
                if (rotatedBitmap != originalBitmap) {
                    rotatedBitmap.recycle()
                }
                originalBitmap.recycle()
                
                return "data:image/jpeg;base64,$rotatedBase64"
            } else {
                // 竖屏图片，不需要旋转
                originalBitmap.recycle()
                return base64Image
            }
        } catch (e: Exception) {
            // 处理失败，返回原图
            return base64Image
        }
    }

    inner class ViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val cardView: CardView = itemView.findViewById(R.id.card_device)
        val imgPreview: ImageView = itemView.findViewById(R.id.img_device_preview)
        private val tvTime: TextView = itemView.findViewById(R.id.tv_device_time)
        private val tvName: TextView = itemView.findViewById(R.id.tv_device_name)
        val btnSettings: ImageView = itemView.findViewById(R.id.btn_device_settings)
        private val offlineOverlay: View = itemView.findViewById(R.id.offline_overlay)

        fun bind(device: DeviceInfo) {
            // 设置设备名称
            tvName.text = device.nick_name
            
            // 计算剩余时间并显示
            try {
                val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                val overTime = dateFormat.parse(device.over_time)
                val currentTime = Date()
                
                if (overTime != null && overTime.after(currentTime)) {
                    val diffMillis = overTime.time - currentTime.time
                    val diffHours = TimeUnit.MILLISECONDS.toHours(diffMillis)
                    
                    tvTime.text = when {
                        diffHours >= 24 -> "${diffHours / 24}天"
                        else -> "${diffHours}h"
                    }
                } else {
                    tvTime.text = "已过期"
                }
            } catch (e: Exception) {
                tvTime.text = "未知"
            }
            
            // 根据online_status显示或隐藏离线蒙版
            offlineOverlay.visibility = if (device.online_status) View.GONE else View.VISIBLE
        }
    }
} 