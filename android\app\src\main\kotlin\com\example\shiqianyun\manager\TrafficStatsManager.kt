package com.example.shiqianyun.manager

import android.os.Handler
import android.os.Looper
import android.util.Log
import com.example.shiqianyun.network.ApiService
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.TrafficStatsRequest
import com.example.shiqianyun.network.service.RetrofitClient

class TrafficStatsManager(
    private val userId: Int,
    private val deviceId: Int,
    private val deviceOnlyCode: String,
    private val onComplete: () -> Unit
) {
    companion object {
        private const val TAG = "TrafficStatsManager"
        private const val SAVE_TIMEOUT_MS = 3000L
    }

    /**
     * 保存流量统计
     */
    fun saveTrafficStats(totalTimeSeconds: Long, totalTrafficKB: Long, totalReceivedBytes: Long) {
        try {
            // 如果连接时间太短或参数无效，直接完成
            if (totalTimeSeconds <= 0 || userId == 0 || deviceId == 0 || deviceOnlyCode.isEmpty()) {
                Log.w(TAG, "统计参数无效，跳过保存")
                onComplete()
                return
            }
            
            // 计算平均流量（KB/s）
            val averageTrafficKBps = if (totalTimeSeconds > 0) {
                (totalTrafficKB.toDouble() / totalTimeSeconds.toDouble())
            } else {
                0.0
            }
            
            Log.d(TAG, "保存流量统计: 总时长=${totalTimeSeconds}秒, 总流量=${totalTrafficKB}KB, 平均=${averageTrafficKBps}KB/s")
            Log.d(TAG, "参数详情: userId=${userId}, deviceId=${deviceId}, deviceOnlyCode=${deviceOnlyCode}")
            
            // 创建请求对象
            val request = TrafficStatsRequest(
                userId = userId,
                deviceId = deviceId,
                deviceOnlyCode = deviceOnlyCode,
                totalTraffic = totalTrafficKB,
                averageTraffic = averageTrafficKBps,
                totalTime = totalTimeSeconds
            )
            
            // 打印完整请求体内容
            Log.d(TAG, "完整请求体: userId=${request.userId}, deviceId=${request.deviceId}, " +
                   "deviceOnlyCode=${request.deviceOnlyCode}, totalTraffic=${request.totalTraffic}, " +
                   "averageTraffic=${request.averageTraffic}, totalTime=${request.totalTime}")
            
            // 创建Retrofit API服务
            val apiService = RetrofitClient.getInstance().create(ApiService::class.java)
            Log.d(TAG, "RetrofitClient baseUrl: ${RetrofitClient.getInstance().baseUrl()}")
            
            // 发送请求
            apiService.saveTrafficStats(request).enqueue(object : retrofit2.Callback<BaseResponse<Any>> {
                override fun onResponse(call: retrofit2.Call<BaseResponse<Any>>, response: retrofit2.Response<BaseResponse<Any>>) {
                    if (response.isSuccessful) {
                        val baseResponse = response.body()
                        Log.d(TAG, "流量统计保存成功: code=${baseResponse?.code}, msg=${baseResponse?.msg}")
                    } else {
                        try {
                            val errorBody = response.errorBody()?.string() ?: "未知错误"
                            Log.e(TAG, "流量统计保存失败: HTTP ${response.code()}, 错误信息: $errorBody")
                        } catch (e: Exception) {
                            Log.e(TAG, "解析错误响应失败", e)
                        }
                    }
                    // 无论成功与否，都完成
                    onComplete()
                }
                
                override fun onFailure(call: retrofit2.Call<BaseResponse<Any>>, t: Throwable) {
                    Log.e(TAG, "流量统计保存失败", t)
                    Log.e(TAG, "错误详情: ${t.message}")
                    // 发送失败，仍然完成
                    onComplete()
                }
            })
            
            // 设置超时，防止请求一直不返回
            Handler(Looper.getMainLooper()).postDelayed({
                Log.w(TAG, "流量统计保存请求超时")
                onComplete()
            }, SAVE_TIMEOUT_MS)
            
        } catch (e: Exception) {
            Log.e(TAG, "保存流量统计失败", e)
            // 出现异常，直接完成
            onComplete()
        }
    }
}
