                        -HD:\idedManger\FlutterSdk\flutter_windows_3.29.0-stable\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=arm64-v8a
-DCMAKE_ANDROID_ARCH_ABI=arm64-v8a
-DANDROID_NDK=D:\idedManger\AndroidSDK\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\idedManger\AndroidSDK\ndk\27.0.12077973
-DC<PERSON>KE_TOOLCHAIN_FILE=D:\idedManger\AndroidSDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\idedManger\AndroidSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\CODE\futter3x\flutter04\build\app\intermediates\cxx\Debug\d646k4u6\obj\arm64-v8a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\CODE\futter3x\flutter04\build\app\intermediates\cxx\Debug\d646k4u6\obj\arm64-v8a
-DCMAKE_BUILD_TYPE=Debug
-BD:\CODE\futter3x\flutter04\android\app\.cxx\Debug\d646k4u6\arm64-v8a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2