package com.example.shiqianyun

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.adapter.FilePushSelectAdapter
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.DeviceInfo
import com.example.shiqianyun.network.model.DeviceListResponse
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.GroupInfo
import com.google.android.material.tabs.TabLayout
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class FilePushSelectActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "FilePushSelectActivity"
        const val EXTRA_FILE_ID = "file_id"
        const val EXTRA_FILE_NAME = "file_name"
        const val EXTRA_FILE_PATH = "file_path"
    }

    private lateinit var ivBack: ImageView
    private lateinit var tabLayout: TabLayout
    private lateinit var recyclerView: RecyclerView
    private lateinit var tvSelectedCount: TextView
    private lateinit var btnConfirm: Button
    private lateinit var progressBar: ProgressBar

    private lateinit var adapter: FilePushSelectAdapter
    private val allDevices = mutableListOf<DeviceInfo>()
    private val currentGroupDevices = mutableListOf<DeviceInfo>()
    private val groupList = mutableListOf<GroupInfo>()

    private var fileId: Int = 0
    private var fileName: String = ""
    private var filePath: String = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(com.example.shiqianyun.R.layout.activity_batch_restart)

        // 获取传递的参数
        fileId = intent.getIntExtra(EXTRA_FILE_ID, 0)
        fileName = intent.getStringExtra(EXTRA_FILE_NAME) ?: ""
        filePath = intent.getStringExtra(EXTRA_FILE_PATH) ?: ""

        if (fileId == 0) {
            Toast.makeText(this, "文件信息无效", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        initViews()
        setupListeners()
        loadDevices()
    }

    private fun initViews() {
        ivBack = findViewById(com.example.shiqianyun.R.id.iv_back)
        tabLayout = findViewById(com.example.shiqianyun.R.id.tab_layout)
        recyclerView = findViewById(com.example.shiqianyun.R.id.recycler_view)
        tvSelectedCount = findViewById(com.example.shiqianyun.R.id.tv_selected_count)
        btnConfirm = findViewById(com.example.shiqianyun.R.id.btn_confirm)
        progressBar = findViewById(com.example.shiqianyun.R.id.progress_bar)

        // 设置按钮文字
        btnConfirm.text = "确认推送"

        // 初始化适配器
        adapter = FilePushSelectAdapter(currentGroupDevices) { updateSelectedCount() }
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter
    }

    private fun setupListeners() {
        ivBack.setOnClickListener {
            finish()
        }

        btnConfirm.setOnClickListener {
            pushFileToDevices()
        }

        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let { filterDevicesByGroup(it.position) }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }

    private fun getUserId(): Int {
        val sharedPreferences = getSharedPreferences("app_prefs", MODE_PRIVATE)
        return sharedPreferences.getInt("user_id", 0)
    }

    private fun loadDevices() {
        progressBar.visibility = View.VISIBLE
        
        val userId = getUserId()
        RetrofitManager.apiService.getAllPhones(userId).enqueue(object : Callback<BaseResponse<DeviceListResponse>> {
            override fun onResponse(
                call: Call<BaseResponse<DeviceListResponse>>,
                response: Response<BaseResponse<DeviceListResponse>>
            ) {
                progressBar.visibility = View.GONE
                
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        handleDeviceData(baseResponse.data)
                        Log.d(TAG, "加载到 ${allDevices.size} 台设备，${groupList.size} 个分组")
                    } else {
                        Toast.makeText(this@FilePushSelectActivity, 
                            baseResponse?.msg ?: "获取设备列表失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@FilePushSelectActivity, 
                        "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<BaseResponse<DeviceListResponse>>, t: Throwable) {
                progressBar.visibility = View.GONE
                Log.e(TAG, "获取设备列表失败", t)
                Toast.makeText(this@FilePushSelectActivity, 
                    "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    /**
     * 处理设备数据
     */
    private fun handleDeviceData(data: DeviceListResponse) {
        // 更新分组列表
        groupList.clear()
        groupList.addAll(data.group ?: emptyList())
        
        // 更新设备列表
        allDevices.clear()
        data.nonExpiredIDsData?.let { allDevices.addAll(it) }
        
        // 设置分组选项卡
        setupTabs()
        
        // 默认显示全部设备
        filterDevicesByGroup(0)
    }

    /**
     * 设置分组选项卡
     */
    private fun setupTabs() {
        tabLayout.removeAllTabs()
        
        // 添加"全部"选项卡
        tabLayout.addTab(tabLayout.newTab().setText("全部"))
        
        // 添加分组选项卡
        groupList.forEach { group ->
            tabLayout.addTab(tabLayout.newTab().setText(group.group_name))
        }
    }

    /**
     * 根据分组筛选设备
     */
    private fun filterDevicesByGroup(tabPosition: Int) {
        currentGroupDevices.clear()
        
        if (tabPosition == 0) {
            // 全部设备
            currentGroupDevices.addAll(allDevices)
        } else {
            // 特定分组的设备（需要调用API获取）
            val group = groupList[tabPosition - 1]
            loadGroupDevices(group.id)
            return
        }
        
        adapter.notifyDataSetChanged()
        updateSelectedCount()
    }

    /**
     * 加载分组设备
     */
    private fun loadGroupDevices(groupId: Int) {
        progressBar.visibility = View.VISIBLE
        
        RetrofitManager.apiService.getGroupDevices(
            groupId = groupId,
            limit = 100,
            sort = "desc"
        ).enqueue(object : Callback<BaseResponse<List<DeviceInfo>>> {
            override fun onResponse(call: Call<BaseResponse<List<DeviceInfo>>>, response: Response<BaseResponse<List<DeviceInfo>>>) {
                progressBar.visibility = View.GONE
                
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        currentGroupDevices.clear()
                        currentGroupDevices.addAll(baseResponse.data ?: emptyList())
                        adapter.notifyDataSetChanged()
                        updateSelectedCount()
                    } else {
                        Toast.makeText(this@FilePushSelectActivity, baseResponse?.msg ?: "获取分组设备失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@FilePushSelectActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<List<DeviceInfo>>>, t: Throwable) {
                progressBar.visibility = View.GONE
                Log.e(TAG, "获取分组设备失败", t)
                Toast.makeText(this@FilePushSelectActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun updateSelectedCount() {
        val selectedCount = adapter.getSelectedDevices().size
        val totalCount = currentGroupDevices.size
        
        tvSelectedCount.text = "已选中 $selectedCount/$totalCount 台设备"
        btnConfirm.isEnabled = selectedCount > 0
    }

    private fun pushFileToDevices() {
        val selectedDevices = adapter.getSelectedDevices()
        if (selectedDevices.isEmpty()) {
            Toast.makeText(this, "请先选择要推送的设备", Toast.LENGTH_SHORT).show()
            return
        }

        val userId = getUserId()
        if (userId == 0) {
            Toast.makeText(this, "用户ID无效", Toast.LENGTH_SHORT).show()
            return
        }

        // 提取设备ID列表
        val deviceIds = selectedDevices.map { it.phone_id }
        
        // 从文件路径中提取基础URL
        val baseUrl = extractBaseUrl(filePath)
        if (baseUrl.isEmpty()) {
            Toast.makeText(this, "无法解析文件路径", Toast.LENGTH_SHORT).show()
            return
        }

        Log.d(TAG, "开始推送文件: fileId=$fileId, deviceIds=$deviceIds, baseUrl=$baseUrl")

        // 显示加载状态
        progressBar.visibility = View.VISIBLE
        btnConfirm.isEnabled = false

        // 构建请求体
        val requestBody = mapOf(
            "user_id" to userId,
            "file_id" to fileId,
            "device_id" to deviceIds
        )

        RetrofitManager.apiService.pushFileToDevices("$baseUrl/api/app/file/translate", requestBody)
            .enqueue(object : Callback<BaseResponse<Any>> {
                override fun onResponse(
                    call: Call<BaseResponse<Any>>,
                    response: Response<BaseResponse<Any>>
                ) {
                    progressBar.visibility = View.GONE
                    btnConfirm.isEnabled = true

                    if (response.isSuccessful) {
                        val baseResponse = response.body()
                        if (baseResponse != null && baseResponse.code == 200) {
                            Toast.makeText(this@FilePushSelectActivity, 
                                "推送成功，正在安装到${selectedDevices.size}台设备", Toast.LENGTH_LONG).show()
                            
                            // 返回上一页
                            finish()
                        } else {
                            Toast.makeText(this@FilePushSelectActivity, 
                                baseResponse?.msg ?: "推送失败", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        Toast.makeText(this@FilePushSelectActivity, 
                            "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                    }
                }

                override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                    progressBar.visibility = View.GONE
                    btnConfirm.isEnabled = true
                    
                    Log.e(TAG, "推送文件失败", t)
                    Toast.makeText(this@FilePushSelectActivity, 
                        "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            })
    }

    /**
     * 从文件路径中提取基础URL
     * 例如: http://106.36.197.138:9090/upload/cloud/17780583839/apk/包名查看
     * 提取: http://106.36.197.138:9090
     */
    private fun extractBaseUrl(filePath: String): String {
        return try {
            val uploadIndex = filePath.indexOf("/upload")
            if (uploadIndex > 0) {
                filePath.substring(0, uploadIndex)
            } else {
                ""
            }
        } catch (e: Exception) {
            Log.e(TAG, "提取基础URL失败: $filePath", e)
            ""
        }
    }
}