package com.example.shiqianyun

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.CheckBox
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.CloudFile
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import com.bumptech.glide.Glide
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
// 添加R类的导入
import com.example.shiqianyun.R

class CloudDiskActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "CloudDiskActivity"
        private const val TYPE_APP = "apk"
        private const val TYPE_IMAGE = "img"
        private const val TYPE_DOC = "doc"
    }

    private lateinit var rvFiles: RecyclerView
    private lateinit var progressStorage: ProgressBar
    private lateinit var tvStorageUsage: TextView
    private lateinit var tvStoragePercentage: TextView
    private lateinit var btnDelete: View
    
    private val cloudFiles = mutableListOf<CloudFile>()
    private lateinit var adapter: CloudFileAdapter
    
    // 当前选中的文件类型
    private var currentFileType = TYPE_APP
    
    // 是否处于选择模式
    private var isSelectionMode = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_cloud_disk)
        
        // 初始化视图
        initViews()
        
        // 设置监听器
        setupListeners()
        
        // 加载云盘文件
        loadCloudFiles(currentFileType)
    }
    
    /**
     * 初始化视图
     */
    private fun initViews() {
        // 文件列表
        rvFiles = findViewById(R.id.rv_files)
        rvFiles.layoutManager = LinearLayoutManager(this)
        adapter = CloudFileAdapter(cloudFiles)
        rvFiles.adapter = adapter
        
        // 存储进度
        progressStorage = findViewById(R.id.progress_storage)
        tvStorageUsage = findViewById(R.id.tv_storage_usage)
        tvStoragePercentage = findViewById(R.id.tv_storage_percentage)
        
        // 删除按钮
        btnDelete = findViewById(R.id.btn_delete)
        btnDelete.visibility = View.GONE
        
        // 设置存储使用情况（模拟数据）
        tvStorageUsage.text = "已用: 0.03G/8G"
        tvStoragePercentage.text = "0.37%"
        progressStorage.progress = 1 // 假设最大值是100
        
        // 更新标签文字
        findViewById<TextView>(R.id.tab_all).text = "应用"
        findViewById<TextView>(R.id.tab_documents).text = "图片"
        findViewById<TextView>(R.id.tab_images).text = "其他"
        
        // 隐藏第四个标签
        findViewById<TextView>(R.id.tab_apps).visibility = View.GONE
    }
    
    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // 返回按钮
        findViewById<View>(R.id.btn_back).setOnClickListener {
            finish()
        }
        
        // 顶部上传进度按钮
        findViewById<View>(R.id.btn_upload_progress).setOnClickListener {
            showUploadProgressList()
        }
        
        // 上传文件按钮
        findViewById<View>(R.id.btn_upload_file).setOnClickListener {
            openFilePicker()
        }
        
        // 删除按钮
        btnDelete.setOnClickListener {
            deleteSelectedFiles()
        }
        
        // 文件类型选项卡
        findViewById<View>(R.id.tab_all).setOnClickListener { 
            switchTab(it as TextView)
            currentFileType = TYPE_APP
            loadCloudFiles(currentFileType)
        }
        findViewById<View>(R.id.tab_documents).setOnClickListener { 
            switchTab(it as TextView)
            currentFileType = TYPE_IMAGE
            loadCloudFiles(currentFileType)
        }
        findViewById<View>(R.id.tab_images).setOnClickListener { 
            switchTab(it as TextView)
            currentFileType = TYPE_DOC
            loadCloudFiles(currentFileType)
        }
    }
    
    /**
     * 切换标签
     */
    private fun switchTab(selectedTab: TextView) {
        // 获取所有标签
        val tabs = listOf(
            findViewById<TextView>(R.id.tab_all),
            findViewById<TextView>(R.id.tab_documents),
            findViewById<TextView>(R.id.tab_images)
        )
        
        // 更新所有标签的选中状态
        for (tab in tabs) {
            tab.setTextColor(resources.getColor(
                if (tab == selectedTab) android.R.color.holo_blue_light
                else android.R.color.darker_gray, 
                null
            ))
        }
    }
    
    /**
     * 打开文件选择器
     */
    private fun openFilePicker() {
        // 跳转到本地文件浏览页面
        val intent = Intent(this, LocalFilesActivity::class.java)
        startActivity(intent)
    }
    
    /**
     * 获取用户ID
     */
    private fun getUserId(): Int {
        val sharedPreferences = getSharedPreferences("app_prefs", MODE_PRIVATE)
        return sharedPreferences.getInt("user_id", 0)
    }
    
    /**
     * 加载云盘文件
     */
    private fun loadCloudFiles(typePath: String) {
        val userId = getUserId()
        if (userId == 0) {
            Toast.makeText(this, "用户ID无效", Toast.LENGTH_SHORT).show()
            return
        }
        
        RetrofitManager.apiService.getCloudFiles(userId, typePath).enqueue(object : Callback<BaseResponse<List<CloudFile>>> {
            override fun onResponse(
                call: Call<BaseResponse<List<CloudFile>>>,
                response: Response<BaseResponse<List<CloudFile>>>
            ) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 更新文件列表
                        updateFileList(baseResponse.data ?: emptyList())
                    } else {
                        Toast.makeText(this@CloudDiskActivity, baseResponse?.msg ?: "获取云盘文件失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@CloudDiskActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<List<CloudFile>>>, t: Throwable) {
                Log.e(TAG, "获取云盘文件失败", t)
                Toast.makeText(this@CloudDiskActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 更新文件列表
     */
    private fun updateFileList(files: List<CloudFile>) {
        cloudFiles.clear()
        cloudFiles.addAll(files)
        adapter.notifyDataSetChanged()
    }
    
    /**
     * 删除选中的文件
     */
    private fun deleteSelectedFiles() {
        val selectedFileIds = adapter.getSelectedFileIds()
        if (selectedFileIds.isEmpty()) {
            Toast.makeText(this, "请先选择要删除的文件", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 获取用户ID
        val userId = getUserId()
        if (userId == 0) {
            Toast.makeText(this, "用户ID无效", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 显示确认对话框
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("删除文件")
            .setMessage("确定要删除选中的${selectedFileIds.size}个文件吗？")
            .setPositiveButton("确定") { _, _ ->
                // 在后台线程中删除文件
                Thread {
                    var successCount = 0
                    var failCount = 0
                    
                    for (fileId in selectedFileIds) {
                        try {
                            val response = com.example.shiqianyun.network.CloudStoreApi.deleteFile(userId, fileId)
                            if (response.optInt("code") == 200) {
                                successCount++
                            } else {
                                failCount++
                            }
                        } catch (e: Exception) {
                            failCount++
                        }
                    }
                    
                    // 在主线程中更新UI
                    runOnUiThread {
                        if (successCount > 0) {
                            Toast.makeText(this, "成功删除${successCount}个文件", Toast.LENGTH_SHORT).show()
                            // 重新加载文件列表
                            loadCloudFiles(currentFileType)
                        }
                        
                        if (failCount > 0) {
                            Toast.makeText(this, "删除失败${failCount}个文件", Toast.LENGTH_SHORT).show()
                        }
                        
                        // 退出选择模式
                        toggleSelectionMode()
                    }
                }.start()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 删除单个文件
     */
    private fun deleteFile(fileId: Int, fileName: String) {
        // 获取用户ID
        val userId = getUserId()
        if (userId == 0) {
            Toast.makeText(this, "用户ID无效", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 显示确认对话框
        androidx.appcompat.app.AlertDialog.Builder(this)
            .setTitle("删除文件")
            .setMessage("确定要删除文件\"${fileName}\"吗？")
            .setPositiveButton("确定") { _, _ ->
                // 在后台线程中删除文件
                Thread {
                    try {
                        val response = com.example.shiqianyun.network.CloudStoreApi.deleteFile(userId, fileId)
                        
                        // 在主线程中更新UI
                        runOnUiThread {
                            if (response.optInt("code") == 200) {
                                Toast.makeText(this, "删除成功", Toast.LENGTH_SHORT).show()
                                // 重新加载文件列表
                                loadCloudFiles(currentFileType)
                            } else {
                                Toast.makeText(this, 
                                    response.optString("msg", "删除失败"), 
                                    Toast.LENGTH_SHORT
                                ).show()
                            }
                        }
                    } catch (e: Exception) {
                        runOnUiThread {
                            Toast.makeText(this, "删除失败: ${e.message}", Toast.LENGTH_SHORT).show()
                        }
                    }
                }.start()
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 切换选择模式
     */
    private fun toggleSelectionMode() {
        isSelectionMode = !isSelectionMode
        
        // 更新UI
        btnDelete.visibility = if (isSelectionMode) View.VISIBLE else View.GONE
        adapter.setSelectionMode(isSelectionMode)
    }
    
    /**
     * 显示上传进度列表
     */
    private fun showUploadProgressList() {
        // TODO: 实现上传进度列表的显示
        Toast.makeText(this, "上传进度列表功能待实现", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 格式化时间
     */
    private fun formatDate(dateString: String?): String {
        if (dateString.isNullOrEmpty()) return ""
        
        try {
            val inputFormat = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX", Locale.getDefault())
            val outputFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
            val date = inputFormat.parse(dateString)
            return outputFormat.format(date ?: Date())
        } catch (e: Exception) {
            Log.e(TAG, "日期格式化失败", e)
            return dateString
        }
    }
    
    /**
     * 文件Adapter
     */
    inner class CloudFileAdapter(private val files: List<CloudFile>) : 
        RecyclerView.Adapter<RecyclerView.ViewHolder>() {
        
        private val VIEW_TYPE_APP = 1
        private val VIEW_TYPE_IMAGE = 2
        private val VIEW_TYPE_DOC = 3
        
        private var selectionMode = false
        private val selectedFiles = mutableSetOf<Int>()
        
        /**
         * 设置选择模式
         */
        fun setSelectionMode(mode: Boolean) {
            selectionMode = mode
            if (!mode) {
                selectedFiles.clear()
            }
            notifyDataSetChanged()
        }
        
        /**
         * 获取选中的文件ID列表
         */
        fun getSelectedFileIds(): List<Int> {
            return selectedFiles.toList()
        }
        
        override fun getItemViewType(position: Int): Int {
            return when (currentFileType) {
                TYPE_APP -> VIEW_TYPE_APP
                TYPE_IMAGE -> VIEW_TYPE_IMAGE
                else -> VIEW_TYPE_DOC
            }
        }
        
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return when (viewType) {
                VIEW_TYPE_APP -> {
                    val view = LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_cloud_app, parent, false)
                    AppViewHolder(view)
                }
                VIEW_TYPE_IMAGE -> {
                    val view = LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_cloud_image, parent, false)
                    ImageViewHolder(view)
                }
                else -> {
                    val view = LayoutInflater.from(parent.context)
                        .inflate(R.layout.item_cloud_doc, parent, false)
                    DocViewHolder(view)
                }
            }
        }
        
        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            val file = files[position]
            
            when (holder) {
                is AppViewHolder -> bindAppViewHolder(holder, file)
                is ImageViewHolder -> bindImageViewHolder(holder, file)
                is DocViewHolder -> bindDocViewHolder(holder, file)
            }
        }
        
        private fun bindAppViewHolder(holder: AppViewHolder, file: CloudFile) {
            // 设置应用信息
            holder.tvAppName.text = file.file_name
            
            // 设置文件大小和版本
            val sizeText = formatFileSize(file.file_size ?: 0)
            val versionText = if (!file.version_name.isNullOrEmpty()) "V${file.version_name}" else ""
            holder.tvAppInfo.text = if (versionText.isNotEmpty()) "$sizeText | $versionText" else sizeText
            
            // 设置创建时间
            holder.tvAppDate.text = formatDate(file.created_at)
            
            // 加载应用图标
            if (!file.app_icon_url.isNullOrEmpty()) {
                Glide.with(this@CloudDiskActivity)
                    .load(file.app_icon_url)
                    .placeholder(R.drawable.ic_app_placeholder)
                    .error(R.drawable.ic_app_placeholder)
                    .into(holder.ivAppIcon)
            } else {
                holder.ivAppIcon.setImageResource(R.drawable.ic_app_placeholder)
            }
            
            // 设置安装按钮
            holder.btnInstall.visibility = if (selectionMode) View.GONE else View.VISIBLE
            
            // 设置删除按钮
            holder.btnDelete.visibility = if (selectionMode) View.GONE else View.VISIBLE
            
            // 设置选择框
            holder.cbSelectFile.visibility = if (selectionMode) View.VISIBLE else View.GONE
            holder.cbSelectFile.isChecked = selectedFiles.contains(file.id)
            
            // 设置点击事件
            setupItemClickListeners(holder.itemView, holder.cbSelectFile, file.id)
            
            // 安装按钮点击事件
            holder.btnInstall.setOnClickListener {
                installApk(file)
            }

            // 删除按钮点击事件
            holder.btnDelete.setOnClickListener {
                deleteFile(file.id, file.file_name ?: "未知文件")
            }
        }
        
        private fun bindImageViewHolder(holder: ImageViewHolder, file: CloudFile) {
            // 设置图片信息
            holder.tvImageName.text = file.file_name
            holder.tvImageSize.text = formatFileSize(file.file_size ?: 0)
            holder.tvImageDate.text = formatDate(file.created_at)
            
            // 加载图片缩略图
            if (!file.file_path.isNullOrEmpty()) {
                Glide.with(this@CloudDiskActivity)
                    .load(file.file_path)
                    .placeholder(R.drawable.ic_image_placeholder)
                    .error(R.drawable.ic_image_placeholder)
                    .centerCrop()
                    .into(holder.ivImageThumb)
            } else {
                holder.ivImageThumb.setImageResource(R.drawable.ic_image_placeholder)
            }
            
            // 设置删除按钮
            holder.btnDelete.visibility = if (selectionMode) View.GONE else View.VISIBLE
            
            // 设置选择框
            holder.cbSelectFile.visibility = if (selectionMode) View.VISIBLE else View.GONE
            holder.cbSelectFile.isChecked = selectedFiles.contains(file.id)
            
            // 设置点击事件
            setupItemClickListeners(holder.itemView, holder.cbSelectFile, file.id)
            
            // 删除按钮点击事件
            holder.btnDelete.setOnClickListener {
                deleteFile(file.id, file.file_name ?: "未知文件")
            }
        }
        
        private fun bindDocViewHolder(holder: DocViewHolder, file: CloudFile) {
            // 设置文档信息
            holder.tvDocName.text = file.file_name
            holder.tvDocSize.text = formatFileSize(file.file_size ?: 0)
            holder.tvDocDate.text = formatDate(file.created_at)
            
            // 根据文件类型设置图标
            val fileExtension = file.file_name?.substringAfterLast('.', "")?.toLowerCase(Locale.ROOT) ?: ""
            val iconRes = when (fileExtension) {
                "txt" -> R.drawable.ic_file_txt
                "pdf" -> R.drawable.ic_file_pdf
                "doc", "docx" -> R.drawable.ic_file_doc
                "xls", "xlsx" -> R.drawable.ic_file_xls
                "ppt", "pptx" -> R.drawable.ic_file_ppt
                else -> R.drawable.ic_file_generic
            }
            holder.ivDocIcon.setImageResource(iconRes)
            
            // 设置删除按钮
            holder.btnDelete.visibility = if (selectionMode) View.GONE else View.VISIBLE
            
            // 设置选择框
            holder.cbSelectFile.visibility = if (selectionMode) View.VISIBLE else View.GONE
            holder.cbSelectFile.isChecked = selectedFiles.contains(file.id)
            
            // 设置点击事件
            setupItemClickListeners(holder.itemView, holder.cbSelectFile, file.id)
            
            // 删除按钮点击事件
            holder.btnDelete.setOnClickListener {
                deleteFile(file.id, file.file_name ?: "未知文件")
            }
        }
        
        /**
         * 设置项目点击事件
         */
        private fun setupItemClickListeners(itemView: View, checkBox: CheckBox, fileId: Int) {
            itemView.setOnClickListener {
                if (selectionMode) {
                    // 选择模式下，点击切换选中状态
                    toggleFileSelection(checkBox, fileId)
                } else {
                    // 普通模式下，点击预览文件
                    previewFile(fileId)
                }
            }
            
            itemView.setOnLongClickListener {
                if (!selectionMode) {
                    toggleSelectionMode()
                    toggleFileSelection(checkBox, fileId)
                }
                true
            }
        }
        
        /**
         * 预览文件
         */
        private fun previewFile(fileId: Int) {
            val file = files.find { it.id == fileId } ?: return
            
            when (currentFileType) {
                TYPE_APP -> {
                    Toast.makeText(this@CloudDiskActivity, "查看应用: ${file.file_name}", Toast.LENGTH_SHORT).show()
                }
                TYPE_IMAGE -> {
                    // 打开图片预览
                    if (!file.file_path.isNullOrEmpty()) {
                        // TODO: 实现图片预览
                        Toast.makeText(this@CloudDiskActivity, "预览图片: ${file.file_name}", Toast.LENGTH_SHORT).show()
                    }
                }
                else -> {
                    // 打开文档预览
                    Toast.makeText(this@CloudDiskActivity, "预览文档: ${file.file_name}", Toast.LENGTH_SHORT).show()
                }
            }
        }
        
        /**
         * 切换文件选择状态
         */
        private fun toggleFileSelection(checkBox: CheckBox, fileId: Int) {
            if (selectedFiles.contains(fileId)) {
                selectedFiles.remove(fileId)
                checkBox.isChecked = false
            } else {
                selectedFiles.add(fileId)
                checkBox.isChecked = true
            }
        }
        
        /**
         * 安装APK - 跳转到设备选择页面
         */
        private fun installApk(file: CloudFile) {
            if (file.file_path.isNullOrEmpty()) {
                Toast.makeText(this@CloudDiskActivity, "APK文件路径无效", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 跳转到文件推送设备选择页面
            val intent = Intent(this@CloudDiskActivity, FilePushSelectActivity::class.java).apply {
                putExtra(FilePushSelectActivity.EXTRA_FILE_ID, file.id)
                putExtra(FilePushSelectActivity.EXTRA_FILE_NAME, file.file_name)
                putExtra(FilePushSelectActivity.EXTRA_FILE_PATH, file.file_path)
            }
            startActivity(intent)
        }
        
        /**
         * 格式化文件大小
         */
        private fun formatFileSize(size: Long): String {
            if (size <= 0) return "0B"
            
            val units = arrayOf("B", "KB", "MB", "GB", "TB")
            val digitGroups = (Math.log10(size.toDouble()) / Math.log10(1024.0)).toInt()
            
            return String.format("%.2f%s", size / Math.pow(1024.0, digitGroups.toDouble()), units[digitGroups])
        }
        
        override fun getItemCount(): Int = files.size
        
        // 应用ViewHolder
        inner class AppViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val ivAppIcon: ImageView = view.findViewById(R.id.iv_app_icon)
            val tvAppName: TextView = view.findViewById(R.id.tv_app_name)
            val tvAppInfo: TextView = view.findViewById(R.id.tv_app_info)
            val tvAppDate: TextView = view.findViewById(R.id.tv_app_date)
            val btnInstall: Button = view.findViewById(R.id.btn_install)
            val btnDelete: Button = view.findViewById(R.id.btn_delete)
            val cbSelectFile: CheckBox = view.findViewById(R.id.cb_select_file)
        }
        
        // 图片ViewHolder
        inner class ImageViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val ivImageThumb: ImageView = view.findViewById(R.id.iv_image_thumb)
            val tvImageName: TextView = view.findViewById(R.id.tv_image_name)
            val tvImageSize: TextView = view.findViewById(R.id.tv_image_size)
            val tvImageDate: TextView = view.findViewById(R.id.tv_image_date)
            val btnDelete: Button = view.findViewById(R.id.btn_delete)
            val cbSelectFile: CheckBox = view.findViewById(R.id.cb_select_file)
        }
        
        // 文档ViewHolder
        inner class DocViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val ivDocIcon: ImageView = view.findViewById(R.id.iv_doc_icon)
            val tvDocName: TextView = view.findViewById(R.id.tv_doc_name)
            val tvDocSize: TextView = view.findViewById(R.id.tv_doc_size)
            val tvDocDate: TextView = view.findViewById(R.id.tv_doc_date)
            val btnDelete: Button = view.findViewById(R.id.btn_delete)
            val cbSelectFile: CheckBox = view.findViewById(R.id.cb_select_file)
        }
    }
} 