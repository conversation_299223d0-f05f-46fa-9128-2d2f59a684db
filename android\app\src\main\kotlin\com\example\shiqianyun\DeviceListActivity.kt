package com.example.shiqianyun

import android.app.AlertDialog
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.InputType
import android.util.Base64
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Gravity
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ListView
import android.widget.PopupWindow
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import android.widget.HorizontalScrollView
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.resource.drawable.DrawableTransitionOptions
import com.example.shiqianyun.adapter.DeviceGridAdapter
import com.example.shiqianyun.adapter.DeviceListAdapter
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.DeviceInfo
import com.example.shiqianyun.network.model.DeviceListResponse
import com.example.shiqianyun.network.model.GroupDevicesRequest
import com.example.shiqianyun.network.model.GroupInfo
import com.example.shiqianyun.network.model.SessionResponse
import com.example.shiqianyun.network.model.SessionRequest
import com.example.shiqianyun.network.model.BitrateConfig
import com.example.shiqianyun.network.model.AnnouncementConfigResponse
import com.example.shiqianyun.network.model.AnnouncementItem
import com.example.shiqianyun.network.model.NodeService
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*
import java.net.URLEncoder
import org.json.JSONObject
import org.json.JSONArray
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken

/**
 * 设备列表界面
 */
class DeviceListActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "DeviceListActivity"
        private const val WS_BASE_URL = "ws://"
        private const val WS_PATH = "/ws/"
        // private const val WS_PATH = "/ws/only/"
        private const val DEFAULT_WIDTH = 720
        private const val DEFAULT_HEIGHT = 1280
        private const val SCREENSHOT_REFRESH_INTERVAL = 10000L // 10秒刷新一次
        private const val LOGIN_CHECK_INTERVAL = 5000L // 5秒检查一次登录状态
    }

    private lateinit var tvDisplayMode: TextView // 显示模式（预览/列表）
    private lateinit var tvColumnMode: TextView // 列数模式（2列图/3列图/4列图）
    private lateinit var tvGroupMode: TextView // 分组模式（全部分组/...）
    private lateinit var recyclerView: RecyclerView
    private lateinit var btnRefresh: ImageButton
    private lateinit var btnGroupMenu: ImageButton // 分组菜单按钮
    private lateinit var progressBar: ProgressBar
    private lateinit var announcementLayout: LinearLayout
    private lateinit var tvAnnouncement: TextView
    private lateinit var btnCloseAnnouncement: ImageView
    private lateinit var announcementScrollView: HorizontalScrollView
    
    // 公告滚动相关
    private var announcementScrollHandler: Handler? = null
    private var announcementScrollRunnable: Runnable? = null
    
    // 当前选择的模式
    private var currentDisplayMode = DisplayMode.PREVIEW
    private var currentColumnCount = 2
    private var currentGroupId = -1  // -1 表示全部分组
    
    // 设备数据
    private var deviceList = mutableListOf<DeviceInfo>()
    private var allDeviceList = mutableListOf<DeviceInfo>() // 保存全部设备，用于筛选
    private var groupList = mutableListOf<GroupInfo>()
    
    // 码率配置列表
    private var bitrateConfigList = mutableListOf<BitrateConfig>()
    // 默认使用标准画质配置（索引1）
    private var defaultBitrateConfig: BitrateConfig? = null
    
    // 适配器
    private lateinit var gridAdapter: DeviceGridAdapter
    private lateinit var listAdapter: DeviceListAdapter

    /**
     * 定时刷新任务
     */
    private var screenshotRefreshHandler: Handler? = null
    private val screenshotRefreshRunnable = object : Runnable {
        override fun run() {
            // 刷新可见设备的截图
            refreshVisibleDevicesScreenshots()
            
            // 继续执行下一次刷新
            screenshotRefreshHandler?.postDelayed(this, SCREENSHOT_REFRESH_INTERVAL)
        }
    }
    
    /**
     * 单点登录检查任务
     */
    private var loginCheckHandler: Handler? = null
    private val loginCheckRunnable = object : Runnable {
        override fun run() {
            // 检查用户单点登录状态
            checkUserSingleLogin()
            
            // 继续执行下一次检查
            loginCheckHandler?.postDelayed(this, LOGIN_CHECK_INTERVAL)
        }
    }

    private var lastBackPressTime: Long = 0
    
    override fun onBackPressed() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastBackPressTime > 2000) { // 2秒内点击两次
            Toast.makeText(this, "再按一次退出应用", Toast.LENGTH_SHORT).show()
            lastBackPressTime = currentTime
        } else {
            super.onBackPressed()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_list)

        // 初始化视图
        initViews()
        
        // 设置点击事件
        setupListeners()
        
        // 加载数据
        loadDeviceData()

        // 初始化刷新按钮
        findViewById<ImageButton>(R.id.btn_refresh).setOnClickListener {
            refreshAllData()
        }

        // 初始化批量操作按钮
        findViewById<ImageButton>(R.id.btn_batch_operation).setOnClickListener {
            showBatchOperationDialog()
        }
        
        // 开始定时刷新设备截图
        startPeriodicScreenshotRefresh()
        
        // 开始定时检查单点登录状态
        startPeriodicLoginCheck()
    }
    
    /**
     * 页面恢复时刷新数据
     */
    override fun onResume() {
        super.onResume()
        
        // 页面恢复时重新加载数据
        loadDeviceData()
        
        Log.d(TAG, "页面恢复，重新加载数据")
    }
    
    /**
     * 页面暂停时停止刷新
     */
    override fun onPause() {
        super.onPause()
        
        // 停止定时刷新
        stopPeriodicScreenshotRefresh()
    }
    
    /**
     * 页面销毁时停止所有定时任务
     */
    override fun onDestroy() {
        super.onDestroy()
        
        // 停止定时刷新
        stopPeriodicScreenshotRefresh()
        
        // 停止单点登录检查
        stopPeriodicLoginCheck()
        
        // 停止公告滚动
        stopAnnouncementAutoScroll()
    }
    
    /**
     * 初始化视图
     */
    private fun initViews() {
        tvDisplayMode = findViewById(R.id.tv_display_mode)
        tvColumnMode = findViewById(R.id.tv_column_mode)
        tvGroupMode = findViewById(R.id.tv_group_mode)
        recyclerView = findViewById(R.id.recycler_view)
        btnRefresh = findViewById(R.id.btn_refresh)
        btnGroupMenu = findViewById(R.id.btn_group_menu)
        progressBar = findViewById(R.id.progress_bar)
        announcementLayout = findViewById(R.id.announcement_layout)
        tvAnnouncement = findViewById(R.id.tv_announcement)
        btnCloseAnnouncement = findViewById(R.id.btn_close_announcement)
        announcementScrollView = findViewById(R.id.scroll_view_announcement)
        
        // 设置底部导航栏点击事件
        findViewById<android.view.View>(R.id.nav_discovery).setOnClickListener {
            // 跳转到购买页面，不销毁当前页面
            val intent = Intent(this, PurchaseActivity::class.java)
            startActivity(intent)
        }
        
        findViewById<android.view.View>(R.id.nav_profile).setOnClickListener {
            // 跳转到个人信息页面
            val intent = Intent(this, ProfileActivity::class.java)
            // 清除返回栈，防止用户按返回键回到设备列表页面
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            // 添加无动画标志
            intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
            startActivity(intent)
            // 关闭当前页面时也不要动画
            overridePendingTransition(0, 0)
            finish()
        }
        
        // 初始化RecyclerView
        recyclerView.layoutManager = GridLayoutManager(this, currentColumnCount)
        
        // 初始化适配器
        gridAdapter = DeviceGridAdapter(
            devices = deviceList,
            // 点击卡片直接连接设备
            onItemClick = { deviceInfo -> 
                connectToDevice(deviceInfo)
            },
            // 点击设置按钮显示设备详情弹窗
            onSettingsClick = { deviceInfo ->
                showDeviceDetailDialog(deviceInfo)
            },
            // 加载设备截图回调
            onLoadScreenshot = { deviceInfo, callback ->
                fetchDeviceScreenshot(deviceInfo, callback)
            }
        )
        
        listAdapter = DeviceListAdapter(
            devices = deviceList,
            // 点击卡片直接连接设备
            onItemClick = { deviceInfo -> 
                connectToDevice(deviceInfo)
            },
            // 点击设置按钮显示设备详情弹窗
            onSettingsClick = { deviceInfo ->
                showDeviceDetailDialog(deviceInfo)
            },
            // 点击续费按钮
            onRenewClick = { deviceInfo ->
                // 创建包含单个设备的列表
                val selectedDevices = ArrayList<DeviceInfo>().apply {
                    add(deviceInfo)
                }
                
                // 跳转到续费套餐页面
                val intent = Intent(this, RenewPackageActivity::class.java).apply {
                    putExtra("selectedDevices", selectedDevices)
                    putExtra("selectedDeviceIds", intArrayOf(deviceInfo.phone_id))
                    putExtra("vipId", deviceInfo.vip_id)
                }
                startActivity(intent)
            }
        )
        
        // 设置默认适配器
        recyclerView.adapter = gridAdapter
        
        // 更新UI状态
        updateUIState()
    }
    
    /**
     * 设置点击事件
     */
    private fun setupListeners() {
        // 刷新按钮
        btnRefresh.setOnClickListener {
            loadDeviceData()
        }
        
        // 显示模式选择
        tvDisplayMode.setOnClickListener {
            showDisplayModePopup(it)
        }
        
        // 列数模式选择
        tvColumnMode.setOnClickListener {
            showColumnModePopup(it)
        }
        
        // 分组模式选择
        tvGroupMode.setOnClickListener {
            showGroupModePopup(it)
        }
        
        // 分组菜单按钮
        btnGroupMenu.setOnClickListener {
            showGroupMenuPopup(it)
        }
        
        // 关闭公告按钮
        btnCloseAnnouncement.setOnClickListener {
            stopAnnouncementAutoScroll()
            announcementLayout.visibility = View.GONE
        }
    }
    
    /**
     * 加载设备数据
     */
    private fun loadDeviceData() {
        // 显示加载中
        showLoading()
        
        // 获取用户ID
        val userId = getUserId()
        
        // 同时获取码率配置和公告
        loadBitrateConfig()
        loadAnnouncement()
        
        // 发起网络请求
        RetrofitManager.apiService.getAllPhones(userId).enqueue(object : Callback<BaseResponse<DeviceListResponse>> {
            override fun onResponse(call: Call<BaseResponse<DeviceListResponse>>, response: Response<BaseResponse<DeviceListResponse>>) {
                hideLoading()
                
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 处理设备数据
                        handleDeviceData(baseResponse.data)
                    } else {
                        Toast.makeText(this@DeviceListActivity, baseResponse?.msg ?: "获取设备列表失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@DeviceListActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<BaseResponse<DeviceListResponse>>, t: Throwable) {
                hideLoading()
                Log.e(TAG, "获取设备列表失败", t)
                Toast.makeText(this@DeviceListActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 处理设备数据
     */
    private fun handleDeviceData(data: DeviceListResponse) {
        // 更新分组列表
        groupList.clear()
        groupList.addAll(data.group ?: emptyList())
        
        // 打印分组信息
        Log.d(TAG, "分组列表数量: ${groupList.size}")
        groupList.forEachIndexed { index, group ->
            Log.d(TAG, "分组 ${index + 1}:")
            Log.d(TAG, "  分组ID: ${group.id}")
            Log.d(TAG, "  分组名称: ${group.group_name}")
            Log.d(TAG, "----------------------------------------")
        }
        
        // 更新设备列表
        allDeviceList.clear()
        // 添加非空检查，避免data.nonExpiredIDsData为null时崩溃
        if (data.nonExpiredIDsData != null) {
            allDeviceList.addAll(data.nonExpiredIDsData)
        }
        
        
        // 重置当前设备列表
        deviceList.clear()
        deviceList.addAll(allDeviceList)
        
        // 重置分组显示
        currentGroupId = -1
        tvGroupMode.text = "全部分组"
        
        // 更新适配器
        gridAdapter.notifyDataSetChanged()
        listAdapter.notifyDataSetChanged()
    }
    
    /**
     * 加载分组设备
     */
    private fun loadGroupDevices(groupId: Int) {
        // 显示加载中
        showLoading()
        
        // 发起网络请求
        RetrofitManager.apiService.getGroupDevices(
            groupId = groupId,
            limit = 16,
            sort = "desc"
        ).enqueue(object : Callback<BaseResponse<List<DeviceInfo>>> {
            override fun onResponse(call: Call<BaseResponse<List<DeviceInfo>>>, response: Response<BaseResponse<List<DeviceInfo>>>) {
                hideLoading()
                
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 处理分组设备数据
                        handleGroupDevices(baseResponse.data)
                    } else {
                        // 显示错误消息
                        Toast.makeText(this@DeviceListActivity, baseResponse?.msg ?: "获取分组设备失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@DeviceListActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<List<DeviceInfo>>>, t: Throwable) {
                hideLoading()
                Log.e(TAG, "获取分组设备失败", t)
                Toast.makeText(this@DeviceListActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 处理分组设备数据
     */
    private fun handleGroupDevices(data: List<DeviceInfo>?) {
        deviceList.clear()
        
        if (data != null && data.isNotEmpty()) {
            deviceList.addAll(data)
        } else {
            // 分组内无设备
            Toast.makeText(this, "该分组内暂无设备", Toast.LENGTH_SHORT).show()
        }
        
        // 更新适配器
        gridAdapter.notifyDataSetChanged()
        listAdapter.notifyDataSetChanged()
    }
    
    /**
     * 添加分组
     */
    private fun addGroup(groupName: String) {
        if (groupName.isBlank()) {
            Toast.makeText(this, "分组名称不能为空", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 显示加载中
        showLoading()
        
        // 获取用户ID
        val userId = getUserId().toString()
        
        // 发起网络请求
        RetrofitManager.apiService.addGroup(groupName, userId).enqueue(object : Callback<BaseResponse<Any>> {
            override fun onResponse(call: Call<BaseResponse<Any>>, response: Response<BaseResponse<Any>>) {
                hideLoading()
                
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 添加分组成功
                        Toast.makeText(this@DeviceListActivity, "添加分组成功", Toast.LENGTH_SHORT).show()
                        // 重新加载设备列表
                        loadDeviceData()
                    } else {
                        // 显示错误消息
                        Toast.makeText(this@DeviceListActivity, baseResponse?.msg ?: "添加分组失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@DeviceListActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                hideLoading()
                Log.e(TAG, "添加分组失败", t)
                Toast.makeText(this@DeviceListActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 显示/隐藏加载中
     */
    private fun showLoading() {
        progressBar.visibility = View.VISIBLE
    }
    
    /**
     * 获取用户ID
     */
    private fun getUserId(): Int {
        val sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        return sharedPreferences.getInt("user_id", 0)
    }
    
    /**
     * 显示添加分组对话框
     */
    private fun showAddGroupDialog() {
        val input = EditText(this).apply {
            hint = "请输入分组名称"
            inputType = InputType.TYPE_CLASS_TEXT
            setPadding(50, 30, 50, 30)
        }
        
        AlertDialog.Builder(this)
            .setTitle("添加分组")
            .setView(input)
            .setPositiveButton("确定") { _, _ ->
                val groupName = input.text.toString().trim()
                addGroup(groupName)
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 显示模式弹窗
     */
    private fun showDisplayModePopup(anchorView: View) {
        val items = listOf("预览模式", "列表模式")
        val adapter = ArrayAdapter(this, R.layout.item_popup_menu, items)
        val listView = ListView(this).apply {
            this.adapter = adapter
            setBackgroundResource(R.drawable.popup_background)
        }
        
        val popupWindow = PopupWindow(
            listView,
            anchorView.width,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            true
        )
        
        listView.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            currentDisplayMode = if (position == 0) DisplayMode.PREVIEW else DisplayMode.LIST
            updateDisplayMode()
            popupWindow.dismiss()
        }
        
        popupWindow.showAsDropDown(anchorView)
    }
    
    /**
     * 列数模式弹窗
     */
    private fun showColumnModePopup(anchorView: View) {
        val items = listOf("2列图", "3列图", "4列图")
        val adapter = ArrayAdapter(this, R.layout.item_popup_menu, items)
        val listView = ListView(this).apply {
            this.adapter = adapter
            setBackgroundResource(R.drawable.popup_background)
        }
        
        val popupWindow = PopupWindow(
            listView,
            anchorView.width,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            true
        )
        
        listView.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            currentColumnCount = position + 2 // 2, 3, 4列
            updateColumnMode()
            popupWindow.dismiss()
        }
        
        popupWindow.showAsDropDown(anchorView)
    }
    
    /**
     * 分组模式弹窗
     */
    private fun showGroupModePopup(anchorView: View) {
        val items = mutableListOf("全部分组")
        groupList.forEach { items.add(it.group_name) }
        
        val adapter = ArrayAdapter(this, R.layout.item_popup_menu, items)
        val listView = ListView(this).apply {
            this.adapter = adapter
            setBackgroundResource(R.drawable.popup_background)
        }
        
        val popupWindow = PopupWindow(
            listView,
            anchorView.width,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            true
        )
        
        listView.onItemClickListener = AdapterView.OnItemClickListener { _, _, position, _ ->
            if (position == 0) {
                // 全部分组
                currentGroupId = -1
                tvGroupMode.text = "全部分组"
                
                // 使用所有设备
                deviceList.clear()
                deviceList.addAll(allDeviceList)
                
                // 刷新适配器
                gridAdapter.notifyDataSetChanged()
                listAdapter.notifyDataSetChanged()
            } else {
                // 选择特定分组
                val group = groupList[position - 1]
                currentGroupId = group.id
                tvGroupMode.text = group.group_name
                
                // 获取分组设备
                loadGroupDevices(currentGroupId)
            }
            
            popupWindow.dismiss()
        }
        
        popupWindow.showAsDropDown(anchorView)
    }
    
    /**
     * 显示分组菜单弹窗
     */
    private fun showGroupMenuPopup(anchorView: View) {
        // 加载弹窗布局
        val popupView = LayoutInflater.from(this).inflate(R.layout.popup_group_menu, null)
        
        // 创建PopupWindow
        val popupWindow = PopupWindow(
            popupView,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT,
            true
        )
        
        // 设置背景和阴影
        popupWindow.setBackgroundDrawable(null)
        popupWindow.elevation = 10f
        
        // 设置点击事件
        popupView.findViewById<LinearLayout>(R.id.layout_new_group).setOnClickListener {
            // 显示添加分组对话框
            showAddGroupDialog()
            popupWindow.dismiss()
        }
        
        popupView.findViewById<LinearLayout>(R.id.layout_manage_group).setOnClickListener {
            // 跳转到分组管理界面
            val intent = Intent(this, GroupManageActivity::class.java)
            startActivity(intent)
            popupWindow.dismiss()
        }
        
        // 测量视图大小
        popupView.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED)
        val popupWidth = popupView.measuredWidth
        
        // 显示弹窗，与按钮右侧对齐
        popupWindow.showAsDropDown(
            anchorView,
            anchorView.width - popupWidth,
            0
        )
    }
    
    /**
     * 更新显示模式
     */
    private fun updateDisplayMode() {
        tvDisplayMode.text = if (currentDisplayMode == DisplayMode.PREVIEW) "预览模式" else "列表模式"
        
        if (currentDisplayMode == DisplayMode.PREVIEW) {
            // 预览模式下使用Grid
            recyclerView.layoutManager = GridLayoutManager(this, currentColumnCount)
            recyclerView.adapter = gridAdapter
            tvColumnMode.visibility = View.VISIBLE
        } else {
            // 列表模式下使用Linear
            recyclerView.layoutManager = LinearLayoutManager(this)
            recyclerView.adapter = listAdapter
            tvColumnMode.visibility = View.GONE
        }
    }
    
    /**
     * 更新列数模式
     */
    private fun updateColumnMode() {
        tvColumnMode.text = "${currentColumnCount}列图"
        
        if (currentDisplayMode == DisplayMode.PREVIEW) {
            (recyclerView.layoutManager as GridLayoutManager).spanCount = currentColumnCount
            recyclerView.adapter?.notifyDataSetChanged()
        }
    }
    
    /**
     * 更新UI状态
     */
    private fun updateUIState() {
        updateDisplayMode()
        updateColumnMode()
        tvGroupMode.text = "全部分组"
    }
    
    /**
     * 显示设备详情对话框
     */
    private fun showDeviceDetailDialog(deviceInfo: DeviceInfo) {
        // 创建对话框
        val dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_device_detail, null)
        val dialog = AlertDialog.Builder(this, R.style.FullScreenDialog)
            .setView(dialogView)
            .create()
            
        // 设置底部显示
        val window = dialog.window
        window?.setGravity(android.view.Gravity.BOTTOM)
        window?.setLayout(android.view.ViewGroup.LayoutParams.MATCH_PARENT, 
                          android.view.ViewGroup.LayoutParams.WRAP_CONTENT)
        
        // 禁用所有动画
        window?.setWindowAnimations(0)
        
        // 设置设备信息
        val tvDeviceName = dialogView.findViewById<TextView>(R.id.tv_device_name)
        val tvDeviceLevel = dialogView.findViewById<TextView>(R.id.tv_device_level)
        val tvDeviceId = dialogView.findViewById<TextView>(R.id.tv_device_id)
        val tvRemainingTime = dialogView.findViewById<TextView>(R.id.tv_remaining_time)
        val layoutExpireWarning = dialogView.findViewById<LinearLayout>(R.id.layout_expire_warning)
        val layoutNodeInfo = dialogView.findViewById<LinearLayout>(R.id.layout_node_info)
        val tvNodeInfo = dialogView.findViewById<TextView>(R.id.tv_node_info)
        
        // 填充设备信息
        tvDeviceName.text = deviceInfo.nick_name
        // 显示设备编号而不是phone_id
        tvDeviceId.text = deviceInfo.device_designation ?: deviceInfo.phone_id.toString()
        
        // 设置VIP等级和Android版本
        val vipLevelText = "${deviceInfo.vip_level} ${deviceInfo.android_version}"
        tvDeviceLevel.text = vipLevelText
        
        // 根据VIP等级设置不同的颜色
        when (deviceInfo.vip_level.toLowerCase(Locale.getDefault())) {
            "vip" -> tvDeviceLevel.setTextColor(resources.getColor(android.R.color.holo_orange_light))
            "svip" -> tvDeviceLevel.setTextColor(resources.getColor(android.R.color.holo_purple))
            else -> tvDeviceLevel.setTextColor(resources.getColor(android.R.color.holo_blue_light))
        }
        
        // 计算剩余时间
        try {
            val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            val overTime = dateFormat.parse(deviceInfo.over_time)
            val currentTime = Date()
            
            if (overTime != null && overTime.after(currentTime)) {
                val diffMillis = overTime.time - currentTime.time
                val diffHours = diffMillis / (60 * 60 * 1000)
                val diffMinutes = (diffMillis % (60 * 60 * 1000)) / (60 * 1000)
                
                tvRemainingTime.text = "${diffHours}时${diffMinutes}分"
                
                // 如果剩余时间小于24小时，显示警告
                if (diffHours < 24) {
                    layoutExpireWarning.visibility = View.VISIBLE
                }
            } else {
                tvRemainingTime.text = "已过期"
                layoutExpireWarning.visibility = View.VISIBLE
            }
        } catch (e: Exception) {
            tvRemainingTime.text = "未知"
        }
        
        // 加载节点信息
        loadNodeInfo(deviceInfo, layoutNodeInfo, tvNodeInfo)
        
        // 设置按钮点击事件
        dialogView.findViewById<View>(R.id.btn_close).setOnClickListener {
            dialog.dismiss()
        }
        
        // 续费按钮
        dialogView.findViewById<View>(R.id.btn_renew_warning).setOnClickListener {
            // 创建包含单个设备的列表
            val selectedDevices = ArrayList<DeviceInfo>().apply {
                add(deviceInfo)
            }
            
            // 跳转到续费套餐页面
            val intent = Intent(this, RenewPackageActivity::class.java).apply {
                putExtra("selectedDevices", selectedDevices)
                putExtra("selectedDeviceIds", intArrayOf(deviceInfo.phone_id))
                putExtra("vipId", deviceInfo.vip_id)
            }
            startActivity(intent)
        }
        
        dialogView.findViewById<View>(R.id.btn_renew_now).setOnClickListener {
            // 创建包含单个设备的列表
            val selectedDevices = ArrayList<DeviceInfo>().apply {
                add(deviceInfo)
            }
            
            // 跳转到续费套餐页面
            val intent = Intent(this, RenewPackageActivity::class.java).apply {
                putExtra("selectedDevices", selectedDevices)
                putExtra("selectedDeviceIds", intArrayOf(deviceInfo.phone_id))
                putExtra("vipId", deviceInfo.vip_id)
            }
            startActivity(intent)
        }
        
        // 复制ID按钮
        dialogView.findViewById<View>(R.id.btn_copy_id).setOnClickListener {
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clip = android.content.ClipData.newPlainText("设备ID", deviceInfo.phone_id.toString())
            clipboard.setPrimaryClip(clip)
            Toast.makeText(this, "已复制设备ID", Toast.LENGTH_SHORT).show()
        }
        
        // 上传文件
        dialogView.findViewById<View>(R.id.btn_upload).setOnClickListener {
            Toast.makeText(this, "上传文件功能开发中", Toast.LENGTH_SHORT).show()
            dialog.dismiss()
        }
        
        // 修改名称按钮
        dialogView.findViewById<View>(R.id.btn_rename).setOnClickListener {
            dialog.dismiss()
            showRenameDeviceDialog(deviceInfo)
        }
        
        // 重启设备按钮
        dialogView.findViewById<View>(R.id.btn_restart).setOnClickListener {
            Toast.makeText(this, "重启设备功能开发中", Toast.LENGTH_SHORT).show()
            dialog.dismiss()
        }
        
        // 恢复出厂按钮
        dialogView.findViewById<View>(R.id.btn_restore).setOnClickListener {
            Toast.makeText(this, "恢复出厂功能开发中", Toast.LENGTH_SHORT).show()
            dialog.dismiss()
        }
        
        // 显示对话框
        dialog.show()
    }
    
    /**
     * 显示重命名设备对话框
     */
    private fun showRenameDeviceDialog(deviceInfo: DeviceInfo) {
        val input = EditText(this).apply {
            hint = "请输入新的设备名称"
            setText(deviceInfo.nick_name)
            inputType = InputType.TYPE_CLASS_TEXT
            setPadding(50, 30, 50, 30)
        }
        
        AlertDialog.Builder(this)
            .setTitle("修改设备名称")
            .setView(input)
            .setPositiveButton("确定") { _, _ ->
                val newName = input.text.toString().trim()
                if (newName.isNotEmpty()) {
                    // 发起修改设备名称的网络请求
                    updateDeviceName(deviceInfo.id, newName)
                } else {
                    Toast.makeText(this, "设备名称不能为空", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }
    
    /**
     * 修改设备名称
     */
    private fun updateDeviceName(rentId: Int, nickName: String) {
        // 显示加载中
        showLoading()
        
        // 发起网络请求
        RetrofitManager.apiService.updateDeviceName(rentId, nickName)
            .enqueue(object : Callback<BaseResponse<Any>> {
                override fun onResponse(call: Call<BaseResponse<Any>>, response: Response<BaseResponse<Any>>) {
                    hideLoading()
                    
                    if (response.isSuccessful) {
                        val baseResponse = response.body()
                        if (baseResponse != null && baseResponse.code == 200) {
                            // 修改成功
                            Toast.makeText(this@DeviceListActivity, "修改设备名称成功", Toast.LENGTH_SHORT).show()
                            // 重新加载设备列表
                            loadDeviceData()
                        } else {
                            // 显示错误消息
                            Toast.makeText(this@DeviceListActivity, baseResponse?.msg ?: "修改设备名称失败", Toast.LENGTH_SHORT).show()
                        }
                    } else {
                        Toast.makeText(this@DeviceListActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                    }
                }
                
                override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                    hideLoading()
                    Log.e(TAG, "修改设备名称失败", t)
                    Toast.makeText(this@DeviceListActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            })
    }
    
    /**
     * 连接到设备
     */
    private fun connectToDevice(deviceInfo: DeviceInfo) {
        try {
            Log.d(TAG, "连接到设备: ${deviceInfo.nick_name} (${deviceInfo.phone_identify})")
            
            if (deviceInfo.adb_type) {
                // 无adb设备，需要先创建session
                createSessionAndConnect(deviceInfo)
            } else {
                // adb设备，使用原有的连接方式
                connectWithAdb(deviceInfo)
            }
        } catch (e: Exception) {
            Log.e(TAG, "连接设备失败", e)
            Toast.makeText(this, "连接设备失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 获取设备截图 - 无adb设备 (adb_type=true)
     */
    private fun getDeviceTaskScreen(phoneIdentify: String, consulId: String, callback: (String?) -> Unit) {
        try {
            // 构建请求URL
            val url = "http://$consulId/api/devices/task_screen"
            
            // 构建请求体 - 使用Map而不是JSONObject
            val requestBody = mapOf("host" to phoneIdentify)
            
            // 打印请求URL（保留URL打印，但移除请求体打印）
            Log.d(TAG, "获取无adb设备截图请求: $url")
            
            // 发起网络请求
            RetrofitManager.apiService.getDeviceTaskScreen(url, requestBody)
                .enqueue(object : Callback<BaseResponse<Map<String, Any>>> {
                    override fun onResponse(
                        call: Call<BaseResponse<Map<String, Any>>>,
                        response: Response<BaseResponse<Map<String, Any>>>
                    ) {
                        if (response.isSuccessful) {
                            val baseResponse = response.body()
                            // 修改判断逻辑，不仅检查code=0，还要检查msg中是否包含"成功"
                            if (baseResponse != null && (baseResponse.code == 0 || 
                                    (baseResponse.msg != null && baseResponse.msg.contains("成功")))) {
                                // 获取截图成功
                                val data = baseResponse.data
                                val screenBase64 = data?.get("screen") as? String
                                
                                if (screenBase64 != null && screenBase64.isNotEmpty()) {
                                    // 返回Base64图片数据
                                    Log.d(TAG, "成功获取无adb设备截图: ${phoneIdentify}")
                                    callback("data:image/jpeg;base64,$screenBase64")
                                    return
                                } else {
                                    Log.e(TAG, "获取无adb设备截图失败: 返回数据中没有screen字段或为空")
                                }
                            } else {
                                Log.e(TAG, "获取无adb设备截图失败: ${baseResponse?.msg}")
                            }
                        } else {
                            try {
                                val errorBody = response.errorBody()?.string() ?: "未知错误"
                                Log.e(TAG, "获取无adb设备截图请求失败: $errorBody")
                            } catch (e: Exception) {
                                Log.e(TAG, "解析错误响应失败", e)
                            }
                        }
                        callback(null)
                    }

                    override fun onFailure(call: Call<BaseResponse<Map<String, Any>>>, t: Throwable) {
                        Log.e(TAG, "获取无adb设备截图网络请求失败", t)
                        callback(null)
                    }
                })
        } catch (e: Exception) {
            Log.e(TAG, "获取无adb设备截图异常", e)
            callback(null)
        }
    }

    /**
     * 获取设备截图 - adb设备 (adb_type=false)
     */
    private fun getDeviceFrame(phoneIdentify: String, consulId: String, callback: (String?) -> Unit) {
        try {
            // 构建请求URL
            val url = "http://$consulId/api/adb/getFrame?device=$phoneIdentify"
            
            // 打印请求内容
            Log.d(TAG, "获取adb设备截图请求: $url")
            
            // 发起网络请求
            RetrofitManager.apiService.getDeviceFrame(url)
                .enqueue(object : Callback<BaseResponse<Map<String, Any>>> {
                    override fun onResponse(
                        call: Call<BaseResponse<Map<String, Any>>>,
                        response: Response<BaseResponse<Map<String, Any>>>
                    ) {
                        if (response.isSuccessful) {
                            val baseResponse = response.body()
                            // 修改判断逻辑，不仅检查code=0，还要检查msg中是否包含"成功"
                            if (baseResponse != null && (baseResponse.code == 0 || 
                                    (baseResponse.msg != null && baseResponse.msg.contains("成功")))) {
                                // 获取截图成功
                                val data = baseResponse.data
                                val imageBase64 = data?.get("image") as? String
                                
                                if (imageBase64 != null && imageBase64.isNotEmpty()) {
                                    // 返回Base64图片数据
                                    Log.d(TAG, "成功获取adb设备截图: ${phoneIdentify}")
                                    callback("data:image/jpeg;base64,$imageBase64")
                                    return
                                } else {
                                    Log.e(TAG, "获取adb设备截图失败: 返回数据中没有image字段或为空")
                                }
                            } else {
                                Log.e(TAG, "获取adb设备截图失败: ${baseResponse?.msg}")
                            }
                        } else {
                            try {
                                val errorBody = response.errorBody()?.string() ?: "未知错误"
                                Log.e(TAG, "获取adb设备截图请求失败: $errorBody")
                            } catch (e: Exception) {
                                Log.e(TAG, "解析错误响应失败", e)
                            }
                        }
                        callback(null)
                    }

                    override fun onFailure(call: Call<BaseResponse<Map<String, Any>>>, t: Throwable) {
                        Log.e(TAG, "获取adb设备截图网络请求失败", t)
                        callback(null)
                    }
                })
        } catch (e: Exception) {
            Log.e(TAG, "获取adb设备截图异常", e)
            callback(null)
        }
    }

    /**
     * 获取设备截图（根据设备类型选择不同的API）
     */
    private fun fetchDeviceScreenshot(deviceInfo: DeviceInfo, callback: (String?) -> Unit) {
        try {
            // 如果设备不在线，直接返回null
            if (!deviceInfo.online_status) {
                Log.d(TAG, "设备离线，不获取截图: ${deviceInfo.nick_name}")
                callback(null)
                return
            }
            
            // 确保设备有必要的参数
            if (deviceInfo.consul_id.isNullOrEmpty() || deviceInfo.phone_identify.isNullOrEmpty()) {
                Log.e(TAG, "获取设备截图失败: 缺少必要参数")
                callback(null)
                return
            }
            
            // 根据adb_type选择不同的API
            if (deviceInfo.adb_type) {
                // adb_type=true，使用task_screen API
                getDeviceTaskScreen(deviceInfo.phone_identify, deviceInfo.consul_id) { base64Image ->
                    callback(base64Image)
                }
            } else {
                // adb_type=false，使用getFrame API
                getDeviceFrame(deviceInfo.phone_identify, deviceInfo.consul_id) { base64Image ->
                    callback(base64Image)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取设备截图异常", e)
            callback(null)
        }
    }

    /**
     * 创建session并连接无adb设备
     */
    private fun createSessionAndConnect(deviceInfo: DeviceInfo) {
        // 显示加载中
        showLoading()
        
        // 构建请求URL
        val url = "http://${deviceInfo.consul_id}/api/session/create"
        
        // 获取当前的码率配置
        val bitrateConfig = defaultBitrateConfig
        
        // 构建请求体
        val sessionRequest = SessionRequest(
            deviceId = deviceInfo.phone_identify,
            fps = bitrateConfig?.fps ?: 20,
            bitrate = bitrateConfig?.videoBitRate ?: 500000,
            resolution = bitrateConfig?.maxSize ?: 1080,
            audioBitrate = bitrateConfig?.audioBitRate ?: 128000
        )
        
        // 打印请求内容
        Log.d(TAG, "创建Session请求: $url")
        Log.d(TAG, "请求体: $sessionRequest")
        
        // 发起网络请求
        RetrofitManager.apiService.createSession(url, sessionRequest)
            .enqueue(object : Callback<BaseResponse<SessionResponse>> {
                override fun onResponse(
                    call: Call<BaseResponse<SessionResponse>>,
                    response: Response<BaseResponse<SessionResponse>>
                ) {
                    hideLoading()
                    
                    if (response.isSuccessful) {
                        val baseResponse = response.body()
                        // 打印完整响应
                        Log.d(TAG, "Session API响应: ${response.body()}")
                        
                        if (baseResponse?.code == 0) {
                            // 获取session id成功
                            val sessionId = baseResponse.data.id
                            
                            // 构建WebSocket URL - 不添加audio=1参数
                            val wsUrl = "ws://${deviceInfo.consul_id}/session/$sessionId"
                            
                            Log.d(TAG, "无adb设备WebSocket URL: $wsUrl")
                            
                            // 将bitrate配置列表转为ArrayList以便传递
                            val bitrateConfigArrayList = ArrayList<BitrateConfig>().apply {
                                addAll(bitrateConfigList)
                            }
                            
                            // 获取用户ID
                            val userId = getUserId().toString()
                            
                            // 启动ScrcpyActivityRefactored，传递比特率配置列表和流量统计所需参数
                            ScrcpyActivityRefactored.startActivity(
                                this@DeviceListActivity,
                                wsUrl,
                                DEFAULT_WIDTH,
                                DEFAULT_HEIGHT,
                                bitrateConfigArrayList,
                                userId,
                                deviceInfo.id.toString(),
                                deviceInfo.phone_identify,
                                deviceInfo.nick_name
                            )
                        } else {
                            Toast.makeText(
                                this@DeviceListActivity,
                                "创建会话失败: ${baseResponse?.msg}",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    } else {
                        try {
                            val errorBody = response.errorBody()?.string() ?: "未知错误"
                            Log.e(TAG, "Session API请求失败: $errorBody")
                            Toast.makeText(
                                this@DeviceListActivity,
                                "网络请求失败: ${response.code()}, $errorBody",
                                Toast.LENGTH_SHORT
                            ).show()
                        } catch (e: Exception) {
                            Log.e(TAG, "解析错误响应失败", e)
                            Toast.makeText(
                                this@DeviceListActivity,
                                "网络请求失败: ${response.code()}",
                                Toast.LENGTH_SHORT
                            ).show()
                        }
                    }
                }

                override fun onFailure(call: Call<BaseResponse<SessionResponse>>, t: Throwable) {
                    hideLoading()
                    Log.e(TAG, "创建会话失败", t)
                    Toast.makeText(
                        this@DeviceListActivity,
                        "网络连接失败: ${t.message}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            })
    }

    /**
     * 使用adb方式连接设备
     */
    private fun connectWithAdb(deviceInfo: DeviceInfo) {
        // 获取当前的码率配置
        val bitrateConfig = defaultBitrateConfig
        
        // 根据Android版本决定是否启用音频
        val enableAudio = deviceInfo.android_version?.toDoubleOrNull()?.let {
            it >= 11.0
        } ?: false
        
        // 创建配置参数对象
        val config = JSONObject().apply {
            put("audio", enableAudio)
            
            // 添加视频编码参数
            if (bitrateConfig != null) {
                put("video_bit_rate", bitrateConfig.videoBitRate)
                put("audio_bit_rate", bitrateConfig.audioBitRate)
                put("max_fps", bitrateConfig.fps)
                put("max_size", bitrateConfig.maxSize)
            }
        }
        
        // 将配置对象转换为JSON字符串，然后进行URL编码
        val configParam = URLEncoder.encode(config.toString(), "UTF-8")
        
        // 拼接WebSocket URL: ws://consul_id/ws/phone_identify?config={"audio":true/false}
        val wsUrl = "${WS_BASE_URL}${deviceInfo.consul_id}${WS_PATH}${deviceInfo.phone_identify}?config=${configParam}"
        
        Log.d(TAG, "adb设备WebSocket URL: $wsUrl")
        Log.d(TAG, "音频状态: ${if (enableAudio) "启用" else "禁用"}")
        Log.d(TAG, "编码参数: $config")
        
        // 将bitrate配置列表转为ArrayList以便传递
        val bitrateConfigArrayList = ArrayList<BitrateConfig>().apply {
            addAll(bitrateConfigList)
        }
        
        // 获取用户ID
        val userId = getUserId().toString()
        
        // 启动ScrcpyActivityRefactored，传递比特率配置列表和流量统计所需参数
        ScrcpyActivityRefactored.startActivity(
            this, 
            wsUrl, 
            DEFAULT_WIDTH, 
            DEFAULT_HEIGHT,
            bitrateConfigArrayList,
            userId,
            deviceInfo.phone_id.toString(),
            deviceInfo.phone_identify,
            deviceInfo.nick_name
        )
    }

    /**
     * 刷新所有数据
     */
    private fun refreshAllData() {
        // 显示加载动画
        showLoading()

        // 重新加载设备列表数据
        loadDeviceData()
        
        // 清空适配器中的截图缓存，强制重新加载截图
        gridAdapter.clearScreenshotCache()
    }

    /**
     * 显示批量操作弹窗
     */
    private fun showBatchOperationDialog() {
        // 创建底部弹出的Dialog
        val dialog = Dialog(this, R.style.BottomDialogStyle)
        val dialogView = layoutInflater.inflate(R.layout.dialog_batch_operation, null)
        dialog.setContentView(dialogView)
        
        // 设置Dialog宽度为屏幕宽度
        val window = dialog.window
        window?.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        window?.setGravity(Gravity.BOTTOM)
        
        // 关闭按钮
        dialogView.findViewById<View>(R.id.btn_close).setOnClickListener {
            dialog.dismiss()
        }
        
        // 批量购买
        dialogView.findViewById<View>(R.id.btn_batch_buy).setOnClickListener {
            // 跳转到购买页面
            val intent = Intent(this, PurchaseActivity::class.java)
            startActivity(intent)
            dialog.dismiss()
        }
        
        // 批量续费
        dialogView.findViewById<View>(R.id.btn_batch_spend).setOnClickListener {
            // 跳转到批量续费页面
            val intent = Intent(this, BatchRenewActivity::class.java)
            startActivity(intent)
            dialog.dismiss()
        }
        
        // 上传文件
        dialogView.findViewById<View>(R.id.btn_upload_file).setOnClickListener {
            // 跳转到云盘页面
            val intent = Intent(this, CloudDiskActivity::class.java)
            startActivity(intent)
            dialog.dismiss()
        }
        
        // 重启设备
        dialogView.findViewById<View>(R.id.btn_restart_device).setOnClickListener {
            // 跳转到批量重启页面
            val intent = Intent(this, BatchRestartActivity::class.java)
            startActivity(intent)
            dialog.dismiss()
        }
        
        // 分组管理
        dialogView.findViewById<View>(R.id.btn_batch_manage).setOnClickListener {
            // 跳转到分组管理页面
            val intent = Intent(this, GroupManageActivity::class.java)
            startActivity(intent)
            dialog.dismiss()
        }
        
        // 恢复出厂
        dialogView.findViewById<View>(R.id.btn_restore_factory).setOnClickListener {
            Toast.makeText(this, "恢复出厂", Toast.LENGTH_SHORT).show()
            dialog.dismiss()
        }
        
        dialog.show()
    }

    // 隐藏加载动画
    private fun hideLoading() {
        progressBar.visibility = View.GONE
    }
    
    /**
     * 显示模式枚举
     */
    enum class DisplayMode {
        PREVIEW, // 预览模式
        LIST     // 列表模式
    }

    /**
     * 获取码率配置
     */
    private fun loadBitrateConfig() {
        RetrofitManager.apiService.getBitrateConfig().enqueue(object : Callback<BaseResponse<List<BitrateConfig>>> {
            override fun onResponse(call: Call<BaseResponse<List<BitrateConfig>>>, response: Response<BaseResponse<List<BitrateConfig>>>) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 处理码率配置数据
                        handleBitrateConfig(baseResponse.data)
                    } else {
                        Log.e(TAG, "获取码率配置失败: ${baseResponse?.msg}")
                    }
                } else {
                    Log.e(TAG, "获取码率配置网络请求失败: ${response.code()}")
                }
            }

            override fun onFailure(call: Call<BaseResponse<List<BitrateConfig>>>, t: Throwable) {
                Log.e(TAG, "获取码率配置网络连接失败", t)
            }
        })
    }
    
    /**
     * 处理码率配置数据
     */
    private fun handleBitrateConfig(configs: List<BitrateConfig>?) {
        if (configs == null || configs.isEmpty()) {
            Log.e(TAG, "码率配置为空")
            return
        }
        
        // 清空并重新填充码率配置列表
        bitrateConfigList.clear()
        bitrateConfigList.addAll(configs)
        
        // 打印码率配置信息
        Log.d(TAG, "码率配置数量: ${bitrateConfigList.size}")
        bitrateConfigList.forEachIndexed { index, config ->
            Log.d(TAG, "配置 ${index + 1}:")
            Log.d(TAG, "  ID: ${config.id}")
            Log.d(TAG, "  名称: ${config.name}")
            Log.d(TAG, "  最大分辨率: ${config.maxSize}")
            Log.d(TAG, "  视频码率: ${config.videoBitRate}")
            Log.d(TAG, "  音频码率: ${config.audioBitRate}")
            Log.d(TAG, "  帧率: ${config.fps}")
            Log.d(TAG, "----------------------------------------")
        }
        
        // 设置默认使用标准画质配置（通常是索引1）
        if (bitrateConfigList.size > 1) {
            defaultBitrateConfig = bitrateConfigList[1] // 使用第二个配置（标准画质）
            Log.d(TAG, "设置默认码率配置: ${defaultBitrateConfig?.name}")
        } else if (bitrateConfigList.isNotEmpty()) {
            defaultBitrateConfig = bitrateConfigList[0] // 只有一个配置时使用该配置
            Log.d(TAG, "设置默认码率配置: ${defaultBitrateConfig?.name}")
        }
    }

    /**
     * 开始定时刷新设备截图
     */
    private fun startPeriodicScreenshotRefresh() {
        // 创建Handler
        if (screenshotRefreshHandler == null) {
            screenshotRefreshHandler = Handler(Looper.getMainLooper())
        }
        
        // 停止之前的任务（如果有）
        stopPeriodicScreenshotRefresh()
        
        // 开始新的定时任务
        screenshotRefreshHandler?.postDelayed(screenshotRefreshRunnable, SCREENSHOT_REFRESH_INTERVAL)
        
        Log.d(TAG, "开始定时刷新设备截图")
    }
    
    /**
     * 停止定时刷新设备截图
     */
    private fun stopPeriodicScreenshotRefresh() {
        screenshotRefreshHandler?.removeCallbacks(screenshotRefreshRunnable)
        Log.d(TAG, "停止定时刷新设备截图")
    }
    
    /**
     * 刷新可见设备的截图
     */
    private fun refreshVisibleDevicesScreenshots() {
        try {
            // 获取当前可见的设备
            val layoutManager = recyclerView.layoutManager
            val firstVisiblePos: Int
            val lastVisiblePos: Int
            
            if (layoutManager is GridLayoutManager) {
                firstVisiblePos = layoutManager.findFirstVisibleItemPosition()
                lastVisiblePos = layoutManager.findLastVisibleItemPosition()
            } else if (layoutManager is LinearLayoutManager) {
                firstVisiblePos = layoutManager.findFirstVisibleItemPosition()
                lastVisiblePos = layoutManager.findLastVisibleItemPosition()
            } else {
                return
            }
            
            // 检查是否有可见项
            if (firstVisiblePos < 0 || lastVisiblePos < 0 || firstVisiblePos > lastVisiblePos) {
                return
            }
            
            Log.d(TAG, "刷新可见设备截图: $firstVisiblePos-$lastVisiblePos")
            
            // 遍历可见的设备，刷新截图
            for (i in firstVisiblePos..lastVisiblePos) {
                if (i >= 0 && i < deviceList.size) {
                    val device = deviceList[i]
                    
                    // 打印设备信息
                    Log.d(TAG, "刷新设备截图: ${device.nick_name} (${device.phone_identify}), adb_type=${device.adb_type}")
                    
                    // 获取设备截图
                    if (currentDisplayMode == DisplayMode.PREVIEW) {
                        val viewHolder = recyclerView.findViewHolderForAdapterPosition(i) as? DeviceGridAdapter.ViewHolder
                        if (viewHolder != null) {
                                                                // 直接使用设备类型对应的API获取截图
                                    if (device.adb_type) {
                                        getDeviceTaskScreen(device.phone_identify, device.consul_id) { base64Image ->
                                            if (base64Image != null) {
                                                // 在后台线程处理图片，避免主线程阻塞
                                                Thread {
                                                    try {
                                                        val processedImage = processImageOrientation(base64Image)
                                                        
                                                        // 在UI线程更新图片
                                                        runOnUiThread {
                                                            try {
                                                                // 使用Glide加载处理后的图片，优化配置减少闪烁
                                                                Glide.with(this)
                                                                    .load(processedImage)
                                                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                                    .skipMemoryCache(false)
                                                                    .dontAnimate() // 禁用动画避免闪烁
                                                                    .into(viewHolder.imgPreview)
                                                            } catch (e: Exception) {
                                                                Log.e(TAG, "加载设备截图失败", e)
                                                            }
                                                        }
                                                    } catch (e: Exception) {
                                                        Log.e(TAG, "处理设备截图失败", e)
                                                    }
                                                }.start()
                                            }
                                        }
                                    } else {
                                        getDeviceFrame(device.phone_identify, device.consul_id) { base64Image ->
                                            if (base64Image != null) {
                                                // 在后台线程处理图片，避免主线程阻塞
                                                Thread {
                                                    try {
                                                        val processedImage = processImageOrientation(base64Image)
                                                        
                                                        // 在UI线程更新图片
                                                        runOnUiThread {
                                                            try {
                                                                // 使用Glide加载处理后的图片，优化配置减少闪烁
                                                                Glide.with(this)
                                                                    .load(processedImage)
                                                                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                                                                    .skipMemoryCache(false)
                                                                    .dontAnimate() // 禁用动画避免闪烁
                                                                    .into(viewHolder.imgPreview)
                                                            } catch (e: Exception) {
                                                                Log.e(TAG, "加载设备截图失败", e)
                                                            }
                                                        }
                                                    } catch (e: Exception) {
                                                        Log.e(TAG, "处理设备截图失败", e)
                                                    }
                                                }.start()
                                            }
                                        }
                                    }
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "刷新设备截图失败", e)
        }
    }

    /**
     * 检查用户单点登录状态
     */
    private fun checkUserSingleLogin() {
        try {
            Log.d(TAG, "检查用户单点登录状态")
            
            // 发起网络请求
            RetrofitManager.apiService.checkUserSingleLogin()
                .enqueue(object : Callback<BaseResponse<Any>> {
                    override fun onResponse(
                        call: Call<BaseResponse<Any>>,
                        response: Response<BaseResponse<Any>>
                    ) {
                        // 响应拦截器会处理特殊状态码（如309、310等）
                        // 这里只处理正常响应
                        if (response.isSuccessful) {
                            Log.d(TAG, "单点登录检查成功")
                        }
                    }

                    override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                        Log.e(TAG, "单点登录检查失败", t)
                    }
                })
        } catch (e: Exception) {
            Log.e(TAG, "单点登录检查异常", e)
        }
    }

    /**
     * 开始定时检查单点登录状态
     */
    private fun startPeriodicLoginCheck() {
        // 创建Handler
        if (loginCheckHandler == null) {
            loginCheckHandler = Handler(Looper.getMainLooper())
        }
        
        // 停止之前的任务（如果有）
        stopPeriodicLoginCheck()
        
        // 开始新的定时任务
        loginCheckHandler?.postDelayed(loginCheckRunnable, LOGIN_CHECK_INTERVAL)
        
        Log.d(TAG, "开始定时检查单点登录状态")
    }
    
    /**
     * 停止定时检查单点登录状态
     */
    private fun stopPeriodicLoginCheck() {
        loginCheckHandler?.removeCallbacks(loginCheckRunnable)
        Log.d(TAG, "停止定时检查单点登录状态")
    }
    
    /**
     * 加载公告配置
     */
    private fun loadAnnouncement() {
        RetrofitManager.apiService.getAnnouncementConfig().enqueue(object : Callback<BaseResponse<AnnouncementConfigResponse>> {
            override fun onResponse(
                call: Call<BaseResponse<AnnouncementConfigResponse>>,
                response: Response<BaseResponse<AnnouncementConfigResponse>>
            ) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 处理公告数据
                        handleAnnouncementData(baseResponse.data)
                    } else {
                        Log.e(TAG, "获取公告配置失败: ${baseResponse?.msg}")
                    }
                } else {
                    Log.e(TAG, "获取公告配置网络请求失败: ${response.code()}")
                }
            }

            override fun onFailure(call: Call<BaseResponse<AnnouncementConfigResponse>>, t: Throwable) {
                Log.e(TAG, "获取公告配置网络连接失败", t)
            }
        })
    }
    
    /**
     * 处理公告数据
     */
    private fun handleAnnouncementData(data: AnnouncementConfigResponse?) {
        if (data == null || data.configInfo.isNullOrEmpty()) {
            Log.d(TAG, "公告配置为空")
            return
        }
        
        try {
            // 解析JSON数组
            val gson = Gson()
            val listType = object : TypeToken<List<AnnouncementItem>>() {}.type
            val announcements: List<AnnouncementItem> = gson.fromJson(data.configInfo, listType)
            
            Log.d(TAG, "解析到公告数量: ${announcements.size}")
            
            if (announcements.isNotEmpty()) {
                // 显示第一个公告
                val firstAnnouncement = announcements.first()
                Log.d(TAG, "显示公告: ${firstAnnouncement.announcementContent}")
                
                runOnUiThread {
                    // 在公告内容前后添加占位符，确保滚动时首尾字符可见
                    val paddedContent = "    ${firstAnnouncement.announcementContent}    "
                    tvAnnouncement.text = paddedContent
                    announcementLayout.visibility = View.VISIBLE
                    
                    // 如果内容较长，启动自动滚动
                    tvAnnouncement.post {
                        if (tvAnnouncement.width > announcementScrollView.width) {
                            // 先停止之前的滚动，再开始新的滚动
                            stopAnnouncementAutoScroll()
                            startAnnouncementAutoScroll()
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "解析公告数据失败", e)
        }
    }
    
    /**
     * 启动公告自动滚动（从右向左）
     */
    private fun startAnnouncementAutoScroll() {
        // 先停止之前的滚动任务
        stopAnnouncementAutoScroll()
        
        // 创建新的Handler和Runnable
        announcementScrollHandler = Handler(Looper.getMainLooper())
        announcementScrollRunnable = object : Runnable {
            override fun run() {
                if (announcementLayout.visibility == View.VISIBLE) {
                    // 检查是否需要滚动
                    val maxScrollX = tvAnnouncement.width - announcementScrollView.width
                    
                    if (maxScrollX > 0) {
                        val currentScrollX = announcementScrollView.scrollX
                        
                        if (currentScrollX >= maxScrollX) {
                            // 滚动到末尾，重新开始
                            announcementScrollView.smoothScrollTo(0, 0)
                            // 停顿2秒后重新开始滚动
                            announcementScrollHandler?.postDelayed(this, 500)
                        } else {
                            // 继续向右滚动（文字从右向左移动）
                            announcementScrollView.smoothScrollBy(2, 0)
                            // 每50毫秒滚动2像素，实现平滑滚动
                            announcementScrollHandler?.postDelayed(this, 50)
                        }
                    }
                }
            }
        }
        
        // 延迟3秒后开始自动滚动
        announcementScrollRunnable?.let { runnable ->
            announcementScrollHandler?.postDelayed(runnable, 3000)
        }
    }
    
    /**
     * 停止公告自动滚动
     */
    private fun stopAnnouncementAutoScroll() {
        announcementScrollRunnable?.let { runnable ->
            announcementScrollHandler?.removeCallbacks(runnable)
        }
        announcementScrollHandler = null
        announcementScrollRunnable = null
        Log.d(TAG, "停止公告自动滚动")
    }
    
    /**
     * 加载节点信息
     */
    private fun loadNodeInfo(deviceInfo: DeviceInfo, layoutNodeInfo: LinearLayout, tvNodeInfo: TextView) {
        RetrofitManager.apiService.getNodeServices().enqueue(object : Callback<BaseResponse<Map<String, NodeService>>> {
            override fun onResponse(
                call: Call<BaseResponse<Map<String, NodeService>>>,
                response: Response<BaseResponse<Map<String, NodeService>>>
            ) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        Log.d(TAG, "节点服务API调用成功，开始查找匹配的节点")
                        // 处理节点服务数据
                        findMatchingNodeService(deviceInfo, baseResponse.data, layoutNodeInfo, tvNodeInfo)
                    } else {
                        Log.e(TAG, "获取节点服务信息失败: ${baseResponse?.msg}")
                        showNodeInfoError(layoutNodeInfo, tvNodeInfo)
                    }
                } else {
                    Log.e(TAG, "获取节点服务信息网络请求失败: ${response.code()}")
                    showNodeInfoError(layoutNodeInfo, tvNodeInfo)
                }
            }

            override fun onFailure(call: Call<BaseResponse<Map<String, NodeService>>>, t: Throwable) {
                Log.e(TAG, "获取节点服务信息网络连接失败", t)
                showNodeInfoError(layoutNodeInfo, tvNodeInfo)
            }
        })
    }
    
    /**
     * 查找匹配的节点服务
     */
    private fun findMatchingNodeService(
        deviceInfo: DeviceInfo, 
        nodeServices: Map<String, NodeService>, 
        layoutNodeInfo: LinearLayout, 
        tvNodeInfo: TextView
    ) {
        try {
            // 解析设备的consul_id，格式应该是 IP:PORT
            val consulId = deviceInfo.consul_id
            Log.d(TAG, "设备 ${deviceInfo.nick_name} 的 consul_id: '$consulId'")
            Log.d(TAG, "节点服务总数: ${nodeServices.size}")
            
            // 遍历所有节点服务，查找匹配的
            for ((serviceKey, nodeService) in nodeServices) {
                val serviceAddress = "${nodeService.serviceAddr}:${nodeService.servicePort}"
                Log.d(TAG, "比较节点服务 $serviceKey: '$serviceAddress' vs '$consulId'")
                Log.d(TAG, "  - service_addr: '${nodeService.serviceAddr}'")
                Log.d(TAG, "  - service_port: ${nodeService.servicePort}")
                Log.d(TAG, "  - tags: ${nodeService.tags}")
                
                if (consulId == serviceAddress) {
                    // 找到匹配的节点服务
                    val tags = nodeService.tags.joinToString(", ")
                    Log.d(TAG, "✅ 找到匹配的节点服务: $serviceKey, 标签: $tags")
                    
                    runOnUiThread {
                        tvNodeInfo.text = tags.ifEmpty { serviceKey }
                        layoutNodeInfo.visibility = View.VISIBLE
                    }
                    return
                }
            }
            
            // 没有找到匹配的节点服务
            Log.d(TAG, "❌ 未找到匹配的节点服务")
            runOnUiThread {
                tvNodeInfo.text = "未知机房"
                layoutNodeInfo.visibility = View.VISIBLE
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "查找节点服务失败", e)
            showNodeInfoError(layoutNodeInfo, tvNodeInfo)
        }
    }
    
    /**
     * 显示节点信息错误
     */
    private fun showNodeInfoError(layoutNodeInfo: LinearLayout, tvNodeInfo: TextView) {
        runOnUiThread {
            tvNodeInfo.text = "获取失败"
            layoutNodeInfo.visibility = View.VISIBLE
        }
    }
    
    /**
     * 处理图片方向，如果是横屏则顺时针旋转90度
     */
    private fun processImageOrientation(base64Image: String): String {
        try {
            // 移除data:image前缀（如果存在）
            val base64Data = if (base64Image.startsWith("data:image")) {
                base64Image.substring(base64Image.indexOf(",") + 1)
            } else {
                base64Image
            }
            
            // 解码Base64为Bitmap
            val decodedBytes = Base64.decode(base64Data, Base64.DEFAULT)
            val originalBitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.size)
                ?: return base64Image // 解码失败，返回原图
            
            // 检查是否为横屏（宽度大于高度）
            val isLandscape = originalBitmap.width > originalBitmap.height
            
            if (isLandscape) {
                // 创建旋转矩阵，顺时针旋转90度
                val matrix = Matrix()
                matrix.postRotate(90f)
                
                // 应用旋转
                val rotatedBitmap = Bitmap.createBitmap(
                    originalBitmap, 0, 0, 
                    originalBitmap.width, originalBitmap.height, 
                    matrix, true
                )
                
                // 将旋转后的Bitmap转换回Base64
                val outputStream = java.io.ByteArrayOutputStream()
                rotatedBitmap.compress(Bitmap.CompressFormat.JPEG, 85, outputStream)
                val rotatedBytes = outputStream.toByteArray()
                val rotatedBase64 = Base64.encodeToString(rotatedBytes, Base64.DEFAULT)
                
                // 清理资源
                if (rotatedBitmap != originalBitmap) {
                    rotatedBitmap.recycle()
                }
                originalBitmap.recycle()
                
                return "data:image/jpeg;base64,$rotatedBase64"
            } else {
                // 竖屏图片，不需要旋转
                originalBitmap.recycle()
                return base64Image
            }
        } catch (e: Exception) {
            // 处理失败，返回原图
            Log.e(TAG, "处理图片方向失败", e)
            return base64Image
        }
    }
} 