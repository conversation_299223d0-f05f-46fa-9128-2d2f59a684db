package com.example.shiqianyun

import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.DeviceInfo
import com.example.shiqianyun.network.model.BaseResponse
import com.google.gson.Gson
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 设备转移页面
 */
class DeviceTransferActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "DeviceTransferActivity"
        private const val REQUEST_SELECT_DEVICES = 1001
    }

    // UI组件
    private lateinit var ivBack: ImageView
    private lateinit var llSelectDevice: LinearLayout
    private lateinit var tvSelectedDevices: TextView
    private lateinit var tvSelectedCount: TextView
    private lateinit var etTargetPhone: EditText
    private lateinit var btnConfirmTransfer: Button
    private lateinit var progressBar: ProgressBar

    // 数据
    private var selectedDevices = mutableListOf<DeviceInfo>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_transfer)

        initViews()
        setupListeners()
    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        ivBack = findViewById(R.id.iv_back)
        llSelectDevice = findViewById(R.id.ll_select_device)
        tvSelectedDevices = findViewById(R.id.tv_selected_devices)
        tvSelectedCount = findViewById(R.id.tv_selected_count)
        etTargetPhone = findViewById(R.id.et_target_phone)
        btnConfirmTransfer = findViewById(R.id.btn_confirm_transfer)
        progressBar = findViewById(R.id.progress_bar)

        updateUI()
    }

    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // 返回按钮
        ivBack.setOnClickListener {
            finish()
        }

        // 选择设备区域点击
        llSelectDevice.setOnClickListener {
            val intent = Intent(this, DeviceTransferSelectActivity::class.java)
            startActivityForResult(intent, REQUEST_SELECT_DEVICES)
        }

        // 确认转移按钮
        btnConfirmTransfer.setOnClickListener {
            confirmTransfer()
        }
    }

    /**
     * 处理页面返回结果
     */
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == REQUEST_SELECT_DEVICES && resultCode == Activity.RESULT_OK) {
            data?.let { intent ->
                val devices = intent.getParcelableArrayListExtra<DeviceInfo>("selected_devices")
                devices?.let { deviceList ->
                    selectedDevices.clear()
                    selectedDevices.addAll(deviceList)
                    updateUI()
                }
            }
        }
    }

    /**
     * 更新UI显示
     */
    private fun updateUI() {
        if (selectedDevices.isEmpty()) {
            tvSelectedDevices.text = "请选择要转移的设备"
            tvSelectedDevices.setTextColor(resources.getColor(android.R.color.darker_gray, null))
            tvSelectedCount.text = "已选中 0/0 台设备"
        } else {
            val deviceNames = selectedDevices.joinToString(", ") { device ->
                device.device_designation?.takeIf { it.isNotBlank() } 
                    ?: device.phone_id.toString()
            }
            tvSelectedDevices.text = if (deviceNames.length > 30) {
                "${deviceNames.substring(0, 30)}..."
            } else {
                deviceNames
            }
            tvSelectedDevices.setTextColor(resources.getColor(android.R.color.black, null))
            tvSelectedCount.text = "已选中 ${selectedDevices.size}/${selectedDevices.size} 台设备"
        }

        // 更新确认按钮状态
        updateConfirmButtonState()
        
        // 监听输入框变化
        etTargetPhone.addTextChangedListener(object : android.text.TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: android.text.Editable?) {
                updateConfirmButtonState()
            }
        })
    }

    /**
     * 更新确认转移按钮状态
     */
    private fun updateConfirmButtonState() {
        val hasSelectedDevices = selectedDevices.isNotEmpty()
        val hasTargetPhone = etTargetPhone.text.toString().trim().isNotEmpty()
        btnConfirmTransfer.isEnabled = hasSelectedDevices && hasTargetPhone
    }

    /**
     * 确认转移
     */
    private fun confirmTransfer() {
        val targetPhone = etTargetPhone.text.toString().trim()
        
        // 验证输入
        if (selectedDevices.isEmpty()) {
            Toast.makeText(this, "请选择要转移的设备", Toast.LENGTH_SHORT).show()
            return
        }
        
        if (targetPhone.isEmpty()) {
            Toast.makeText(this, "请输入接收方手机号", Toast.LENGTH_SHORT).show()
            return
        }
        
        if (!isValidPhoneNumber(targetPhone)) {
            Toast.makeText(this, "请输入正确的手机号码", Toast.LENGTH_SHORT).show()
            return
        }

        // 显示确认对话框
        showTransferConfirmDialog(targetPhone)
    }

    /**
     * 显示转移确认对话框
     */
    private fun showTransferConfirmDialog(targetPhone: String) {
        val deviceNames = selectedDevices.joinToString("\n") { device ->
            "• ${device.device_designation?.takeIf { it.isNotBlank() } ?: device.phone_id.toString()}"
        }
        
        val message = "确认要将以下设备转移给 $targetPhone 吗？\n\n$deviceNames\n\n注意：转移操作不可逆，请确认信息无误。"
        
        AlertDialog.Builder(this)
            .setTitle("确认转移")
            .setMessage(message)
            .setPositiveButton("确认转移") { _, _ ->
                executeTransfer(targetPhone)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 执行转移操作
     */
    private fun executeTransfer(targetPhone: String) {
        showLoading(true)
        
        val sharedPreferences = getSharedPreferences("app_prefs", MODE_PRIVATE)
        val userId = sharedPreferences.getInt("user_id", 0)
        
        val phoneIds = selectedDevices.map { it.phone_id }
        
        val requestMap = mapOf(
            "phone_id" to phoneIds,
            "target_user_Phone" to targetPhone,
            "user_id" to userId,
            "tag_side" to 4
        )
        
        val gson = Gson()
        val requestBody = gson.toJson(requestMap).toRequestBody("application/json".toMediaTypeOrNull())
        
        Log.d(TAG, "转移请求参数: $requestMap")
        
        val call = RetrofitManager.apiService.transferDevices(requestBody)
        call.enqueue(object : Callback<BaseResponse<Any>> {
            override fun onResponse(call: Call<BaseResponse<Any>>, response: Response<BaseResponse<Any>>) {
                showLoading(false)
                
                if (response.isSuccessful) {
                    val result = response.body()
                    if (result?.code == 200) {
                        Toast.makeText(this@DeviceTransferActivity, "设备转移成功", Toast.LENGTH_SHORT).show()
                        finish()
                    } else {
                        val errorMsg = result?.msg ?: "转移失败"
                        Toast.makeText(this@DeviceTransferActivity, errorMsg, Toast.LENGTH_SHORT).show()
                        Log.e(TAG, "转移失败: $errorMsg")
                    }
                } else {
                    Toast.makeText(this@DeviceTransferActivity, "网络请求失败", Toast.LENGTH_SHORT).show()
                    Log.e(TAG, "网络请求失败: ${response.message()}")
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                showLoading(false)
                Toast.makeText(this@DeviceTransferActivity, "网络连接失败", Toast.LENGTH_SHORT).show()
                Log.e(TAG, "网络连接失败", t)
            }
        })
    }

    /**
     * 验证手机号格式
     */
    private fun isValidPhoneNumber(phone: String): Boolean {
        return phone.matches(Regex("^1[3-9]\\d{9}$"))
    }

    /**
     * 显示/隐藏加载状态
     */
    private fun showLoading(show: Boolean) {
        progressBar.visibility = if (show) View.VISIBLE else View.GONE
        btnConfirmTransfer.isEnabled = !show && selectedDevices.isNotEmpty() && 
                etTargetPhone.text.toString().trim().isNotEmpty()
    }
}