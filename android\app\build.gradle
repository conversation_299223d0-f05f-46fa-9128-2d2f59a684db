plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new RuntimeException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}




def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

// 获取签名配置的属性
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace "com.example.shiqianyun"
    compileSdkVersion 35

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = '11'
    }
    
    buildFeatures {
        compose true
    }
    
    composeOptions {
        kotlinCompilerExtensionVersion "1.4.7"
    }
    
    tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
        kotlinOptions {
            freeCompilerArgs += [
                "-Xsuppress-version-warnings",
                "-P",
                "plugin:androidx.compose.compiler.plugins.kotlin:suppressKotlinVersionCompatibilityCheck=true"
            ]
        }
    }
    tasks.whenTaskAdded { task ->
        if (task.name == 'generateDebugUnitTestConfig') {
            task.enabled = false
        }
    }
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    // 添加签名配置
    signingConfigs {
        release {
            if (keystorePropertiesFile.exists()) {
                keyAlias keystoreProperties['keyAlias']
                keyPassword keystoreProperties['keyPassword']
                storeFile file(keystoreProperties['storeFile'])
                storePassword keystoreProperties['storePassword']
            } else {
                // 默认开发签名配置，可以移除或修改
                keyAlias 'androiddebugkey'
                keyPassword 'android'
                storeFile file('debug.keystore')
                storePassword 'android'
            }
        }
    }

    defaultConfig {
        applicationId "com.example.shiqianyun"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        
        ndk {
            abiFilters 'armeabi-v7a', 'arm64-v8a', 'x86_64'
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            shrinkResources false
            // 使用签名配置
            signingConfig signingConfigs.release
            // 应用混淆文件(可选)
            // proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
}

flutter {
    source '../..' 
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.appcompat:appcompat:1.4.1"
    implementation "org.java-websocket:Java-WebSocket:1.5.3"
    implementation "androidx.constraintlayout:constraintlayout:2.1.4"
    implementation "androidx.cardview:cardview:1.0.0"
    implementation "com.google.android.material:material:1.6.0"
    implementation "com.squareup.retrofit2:retrofit:2.9.0"
    implementation "com.squareup.retrofit2:converter-gson:2.9.0"
    implementation "com.squareup.okhttp3:okhttp:4.9.3"
    implementation "com.squareup.okhttp3:logging-interceptor:4.9.3"
    implementation "com.google.code.gson:gson:2.9.0"
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.4.0'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.4.0'
    implementation 'com.github.bumptech.glide:glide:4.12.0'
    annotationProcessor 'com.github.bumptech.glide:compiler:4.12.0'
    // Kotlin协程
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.6.0'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.0'
    //微信支付SDK
    api 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.34'
    
    // Jetpack Compose 依赖
    def compose_version = "1.4.3"
    implementation "androidx.compose.ui:ui:$compose_version"
    implementation "androidx.compose.ui:ui-tooling-preview:$compose_version"
    implementation "androidx.compose.material3:material3:1.0.1"
    implementation "androidx.compose.foundation:foundation:$compose_version"
    implementation "androidx.activity:activity-compose:1.7.2"
    implementation "androidx.lifecycle:lifecycle-runtime-compose:2.6.1"
    debugImplementation "androidx.compose.ui:ui-tooling:$compose_version"
    
    implementation fileTree(dir: 'libs', include: ['*.aar', '*.jar'], exclude: [])
}