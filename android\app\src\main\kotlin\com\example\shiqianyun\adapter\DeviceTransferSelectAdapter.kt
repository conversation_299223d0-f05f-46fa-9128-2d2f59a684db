package com.example.shiqianyun.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.network.model.DeviceInfo
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * 剩余时间信息
 */
data class RemainingTimeInfo(
    val text: String,
    val days: Int,
    val totalHours: Int
)

class DeviceTransferSelectAdapter(
    private val deviceList: List<DeviceInfo>,
    private val onDeviceSelected: (DeviceInfo, Boolean) -> Unit
) : RecyclerView.Adapter<DeviceTransferSelectAdapter.DeviceViewHolder>() {

    private val selectedDevices = mutableSetOf<Int>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(com.example.shiqianyun.R.layout.item_device_transfer_select, parent, false)
        return DeviceViewHolder(view)
    }

    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        val device = deviceList[position]
        holder.bind(device)
    }

    override fun getItemCount(): Int = deviceList.size

    inner class DeviceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val checkBox: CheckBox = itemView.findViewById(com.example.shiqianyun.R.id.checkbox)
        private val tvDeviceName: TextView = itemView.findViewById(com.example.shiqianyun.R.id.tv_device_name)
        private val tvDeviceInfo: TextView = itemView.findViewById(com.example.shiqianyun.R.id.tv_device_info)
        private val tvRemainingTime: TextView = itemView.findViewById(com.example.shiqianyun.R.id.tv_remaining_time)
        private val tvStatus: TextView = itemView.findViewById(com.example.shiqianyun.R.id.tv_status)

        fun bind(device: DeviceInfo) {
            // 设备名称
            tvDeviceName.text = device.nick_name

            // 设备信息：VIP等级 | 系统版本 | 设备编号
            val deviceDesignation = device.device_designation ?: device.phone_id.toString()
            tvDeviceInfo.text = "${device.vip_level} | ${device.android_version} | $deviceDesignation"

            // 剩余时间
            val remainingTime = calculateRemainingTime(device.over_time)
            tvRemainingTime.text = remainingTime.text
            tvRemainingTime.setTextColor(
                when {
                    remainingTime.days >= 7 -> 0xFF4CAF50.toInt() // 绿色
                    remainingTime.days >= 3 -> 0xFFFF9800.toInt() // 橙色
                    remainingTime.totalHours >= 24 -> 0xFFFF9800.toInt() // 橙色
                    remainingTime.totalHours > 0 -> 0xFFF44336.toInt() // 红色
                    else -> 0xFF757575.toInt() // 灰色
                }
            )

            // 在线状态
            tvStatus.text = if (device.online_status) "在线" else "离线"
            tvStatus.setTextColor(
                if (device.online_status) {
                    0xFF4CAF50.toInt() // 绿色
                } else {
                    0xFFF44336.toInt() // 红色
                }
            )

            // 复选框状态
            checkBox.isChecked = selectedDevices.contains(device.phone_id)

            // 设置点击事件
            val clickListener = View.OnClickListener {
                val isSelected = !checkBox.isChecked
                checkBox.isChecked = isSelected
                
                if (isSelected) {
                    selectedDevices.add(device.phone_id)
                } else {
                    selectedDevices.remove(device.phone_id)
                }
                
                onDeviceSelected(device, isSelected)
            }

            itemView.setOnClickListener(clickListener)
            checkBox.setOnClickListener(clickListener)
        }

        /**
         * 计算剩余时间（支持天、小时显示）
         */
        private fun calculateRemainingTime(overTime: String): RemainingTimeInfo {
            return try {
                // 处理时间格式
                val timeStr = when {
                    overTime.contains("T") -> {
                        // ISO格式: 2025-08-09T10:16:42+08:00
                        overTime.replace("T", " ").substringBefore("+").substringBefore("Z")
                    }
                    else -> {
                        // 普通格式: 2025-08-09 10:16:42
                        overTime
                    }
                }
                
                val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                val overTimeDate = format.parse(timeStr)
                val currentTime = System.currentTimeMillis()
                val remainingMillis = (overTimeDate?.time ?: 0) - currentTime
                
                if (remainingMillis <= 0) {
                    return RemainingTimeInfo("已到期", 0, 0)
                }
                
                val totalHours = (remainingMillis / (60 * 60 * 1000)).toInt()
                val days = totalHours / 24
                val hours = totalHours % 24
                
                val text = when {
                    days >= 1 -> {
                        if (hours > 0) "剩余 ${days}天${hours}小时" else "剩余 ${days}天"
                    }
                    totalHours > 0 -> "剩余 ${totalHours}小时"
                    else -> "即将到期"
                }
                
                RemainingTimeInfo(text, days, totalHours)
            } catch (e: Exception) {
                // 如果解析失败，默认返回即将到期
                RemainingTimeInfo("解析失败", 0, 0)
            }
        }
    }
}