package com.example.shiqianyun.adapter

import android.graphics.Color
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.network.model.ConsumptionRecord
import com.example.shiqianyun.network.model.VipLevel
import org.json.JSONObject

class ConsumptionRecordAdapter(
    private val records: List<ConsumptionRecord>,
    private val vipLevels: List<VipLevel> = emptyList()
) : RecyclerView.Adapter<ConsumptionRecordAdapter.ViewHolder>() {

    // 硬编码颜色值，避免R类引用问题
    private val COLOR_PRIMARY = "#529cff"
    private val COLOR_ACCENT = "#FF5722"
    private val COLOR_BLACK = "#333333"

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        // 使用findViewById时，使用id的整数值，避免直接引用R.id
        val tvTitle: TextView = view.findViewById(view.resources.getIdentifier("tv_record_title", "id", view.context.packageName))
        val tvTime: TextView = view.findViewById(view.resources.getIdentifier("tv_record_time", "id", view.context.packageName))
        val tvContent: TextView = view.findViewById(view.resources.getIdentifier("tv_record_content", "id", view.context.packageName))
        val tvDuration: TextView = view.findViewById(view.resources.getIdentifier("tv_record_duration", "id", view.context.packageName))
        val layoutDetails: LinearLayout = view.findViewById(view.resources.getIdentifier("layout_details", "id", view.context.packageName))
        val tvDeviceCode: TextView = view.findViewById(view.resources.getIdentifier("tv_device_code", "id", view.context.packageName))
        val ivExpand: ImageView = view.findViewById(view.resources.getIdentifier("iv_expand", "id", view.context.packageName))
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layoutId = parent.resources.getIdentifier("item_consumption_record", "layout", parent.context.packageName)
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        return ViewHolder(view)
    }

    override fun getItemCount() = records.size

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        try {
            val record = records[position]
            
            // 设置时间
            holder.tvTime.text = record.createTime
            
            // 根据支付方式设置不同的内容
            when (record.payWay) {
                "activateCode" -> {
                    // 激活码新增设备
                    holder.tvTitle.text = "激活码新增设备"
                    
                    // 获取激活码
                    val activateCode = record.activateCodes?.firstOrNull()
                    if (activateCode != null) {
                        holder.tvContent.text = activateCode.uuid
                        
                        // 设置时长
                        val timeUnit = if (activateCode.timeType == 0) "小时" else "天"
                        holder.tvDuration.text = "${activateCode.buyDay}${timeUnit}"
                    } else {
                        holder.tvContent.text = "无激活码信息"
                        holder.tvDuration.text = "${record.buyDay}小时"
                    }
                    
                    holder.tvDuration.setTextColor(Color.parseColor(COLOR_ACCENT))
                }
                "activateRenewPay" -> {
                    // 激活码续费设备
                    holder.tvTitle.text = "激活码续费设备"
                    
                    // 获取激活码
                    val activateCode = record.activateCodes?.firstOrNull()
                    if (activateCode != null) {
                        holder.tvContent.text = activateCode.uuid
                        
                        // 设置时长
                        val timeUnit = if (activateCode.timeType == 0) "小时" else "天"
                        holder.tvDuration.text = "${activateCode.buyDay}${timeUnit}"
                    } else {
                        holder.tvContent.text = "无激活码信息"
                        holder.tvDuration.text = "${record.buyDay}小时"
                    }
                    
                    // 续费使用绿色，区别于新增的橙色
                    holder.tvDuration.setTextColor(Color.parseColor("#4CAF50"))
                }
                "translatePhone" -> {
                    // 转移云机
                    holder.tvTitle.text = "转移云机"
                    
                    // 显示设备信息
                    val deviceInfo = if (record.devices?.isNotEmpty() == true) {
                        val device = record.devices.first()
                        "设备: ${device.deviceDesignation ?: device.id.toString()}"
                    } else if (record.deviceIdList?.isNotEmpty() == true) {
                        "设备ID: ${record.deviceIdList.joinToString(", ")}"
                    } else {
                        "设备信息不可用"
                    }
                    
                    holder.tvContent.text = deviceInfo
                    
                    // 显示转移状态和目标用户（如果有的话）
                    val statusText = if (record.targetTranslateId != null) {
                        "已转移给用户${record.targetTranslateId}"
                    } else {
                        "转移完成"
                    }
                    holder.tvDuration.text = statusText
                    
                    // 转移云机使用紫色
                    holder.tvDuration.setTextColor(Color.parseColor("#9C27B0"))
                }
                "wechat", "zhifubao" -> {
                    // 微信或支付宝支付
                    val renewText = if (record.isRenew == true) "续费" else "新增"
                    holder.tvTitle.text = "${renewText}购买分配成功"
                    
                    // 查找对应的VIP等级
                    val vipInfo = vipLevels.find { it.id == record.vip }
                    val vipName = vipInfo?.vip_name ?: "VIP${record.vip}"
                    
                    // 解析设备配置
                    var memoryStorage = ""
                    try {
                        if (vipInfo != null) {
                            val deviceJson = JSONObject(vipInfo.device_json)
                            val memory = deviceJson.optString("memory", "")
                            val storage = deviceJson.optString("storage", "")
                            if (memory.isNotEmpty() && storage.isNotEmpty()) {
                                memoryStorage = "(${memory}+${storage})"
                            }
                        }
                    } catch (e: Exception) {
                        Log.e("ConsumptionAdapter", "解析设备配置失败", e)
                    }
                    
                    // 设置内容
                    holder.tvContent.text = "$vipName$memoryStorage ${record.buyDay}天*${record.buyNum}"
                    
                    // 设置价格
                    holder.tvDuration.text = "￥${record.totalPrice}"
                    
                    // 设置颜色
                    val colorHex = if (record.payWay == "wechat") COLOR_PRIMARY else "#1E88E5"
                    holder.tvDuration.setTextColor(Color.parseColor(colorHex))
                }
                else -> {
                    // 未知类型
                    holder.tvTitle.text = "未知消费记录"
                    holder.tvContent.text = record.content ?: ""
                    holder.tvDuration.text = record.duration ?: ""
                    holder.tvDuration.setTextColor(Color.parseColor(COLOR_BLACK))
                }
            }
            
            // 设置设备编号和订单号
            val deviceDesignation = when {
                // 优先从devices数组获取设备编号
                !record.devices.isNullOrEmpty() -> {
                    val device = record.devices.firstOrNull()
                    if (device != null && !device.deviceDesignation.isNullOrEmpty()) {
                        device.deviceDesignation
                    } else {
                        "设备ID: ${device?.id ?: "未知"}"
                    }
                }
                // 如果是续费记录，从device_id_list获取
                !record.deviceIdList.isNullOrEmpty() -> {
                    "设备ID: ${record.deviceIdList.first()}"
                }
                // 备用方案
                record.deviceDesignation != null -> record.deviceDesignation
                else -> "无设备信息"
            }
            holder.tvDeviceCode.text = "设备编号：$deviceDesignation\n订单号：${record.orderNum}"
            
            // 设置展开/收起详情的点击事件
            val clickListener = View.OnClickListener { toggleDetails(holder) }
            holder.tvTitle.setOnClickListener(clickListener)
            holder.ivExpand.setOnClickListener(clickListener)
            
            // 初始状态为收起
            holder.layoutDetails.visibility = View.GONE
            
            // 设置箭头图标
            val arrowDownId = holder.ivExpand.context.resources.getIdentifier("ic_arrow_down", "drawable", holder.ivExpand.context.packageName)
            holder.ivExpand.setImageResource(arrowDownId)
        } catch (e: Exception) {
            Log.e("ConsumptionAdapter", "绑定数据失败: ${e.message}", e)
        }
    }

    private fun toggleDetails(holder: ViewHolder) {
        try {
            val context = holder.itemView.context
            if (holder.layoutDetails.visibility == View.VISIBLE) {
                holder.layoutDetails.visibility = View.GONE
                val arrowDownId = context.resources.getIdentifier("ic_arrow_down", "drawable", context.packageName)
                holder.ivExpand.setImageResource(arrowDownId)
            } else {
                holder.layoutDetails.visibility = View.VISIBLE
                val arrowUpId = context.resources.getIdentifier("ic_arrow_up", "drawable", context.packageName)
                holder.ivExpand.setImageResource(arrowUpId)
            }
        } catch (e: Exception) {
            Log.e("ConsumptionAdapter", "切换详情失败: ${e.message}", e)
        }
    }
} 