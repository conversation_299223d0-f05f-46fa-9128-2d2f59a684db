package com.example.shiqianyun

import android.annotation.SuppressLint
import android.content.*
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.ImageFormat
import android.graphics.Matrix
import android.graphics.Rect
import android.graphics.RectF
import android.graphics.YuvImage
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.os.*
import android.util.Log
import android.util.SparseArray
import android.view.InputDevice
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.Surface
import android.view.SurfaceHolder
import android.view.SurfaceView
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.nio.ByteBuffer
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit
import org.java_websocket.client.WebSocketClient
import org.java_websocket.handshake.ServerHandshake
import java.net.URI
import android.os.Handler
import android.os.Looper
import org.json.JSONObject
import org.json.JSONArray
import java.io.BufferedInputStream
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.atomic.AtomicBoolean
import javax.net.ssl.SSLSocketFactory
import java.lang.Exception
import android.hardware.SensorManager
import android.hardware.SensorEventListener
import android.hardware.SensorEvent
import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.view.OrientationEventListener
import android.content.ClipData
import android.content.ClipboardManager
import java.nio.charset.StandardCharsets
import com.example.shiqianyun.network.model.BitrateConfig
import android.content.DialogInterface
import com.example.shiqianyun.noAdbAudioDecoder
import android.os.CountDownTimer
import com.example.shiqianyun.network.model.TrafficStatsRequest
import com.example.shiqianyun.network.ApiService
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.service.RetrofitClient
import android.view.Gravity
import android.view.ViewConfiguration
import android.media.MediaScannerConnection
import android.Manifest
import android.content.pm.PackageManager
import android.view.WindowManager
import java.net.URLEncoder

class ScrcpyActivity : AppCompatActivity(), SurfaceHolder.Callback {
    companion object {
        private const val TAG = "ScrcpyActivity"
        
        // WebSocket常量
        private const val WS_URL_KEY = "ws_url"
        private const val DEVICE_WIDTH_KEY = "device_width"
        private const val DEVICE_HEIGHT_KEY = "device_height"
        private const val BITRATE_CONFIGS_KEY = "bitrate_configs"
        private const val DEVICE_NAME_KEY = "device_name" // 添加设备名称常量
        
        // 触摸事件常量
        private const val ACTION_DOWN = 0
        private const val ACTION_UP = 1
        private const val ACTION_MOVE = 2
        
        // 新增：错误处理常量
        private const val MAX_CONSECUTIVE_ERRORS = 10
        
        // 新增：屏幕旋转相关常量
        private const val ORIENTATION_PORTRAIT = 0
        private const val ORIENTATION_LANDSCAPE = 1
        
        // 添加解码器配置状态变量
        
        // 启动Activity的工厂方法
        fun startActivity(
            context: Context, 
            wsUrl: String, 
            width: Int, 
            height: Int, 
            bitrateConfigs: ArrayList<BitrateConfig>? = null,
            userId: Int = 0,
            deviceId: Int = 0,
            deviceOnlyCode: String = "",
            deviceName: String = "" // 添加设备昵称参数
        ) {
            val intent = Intent(context, ScrcpyActivity::class.java).apply {
                putExtra(WS_URL_KEY, wsUrl)
                putExtra(DEVICE_WIDTH_KEY, width)
                putExtra(DEVICE_HEIGHT_KEY, height)
                putParcelableArrayListExtra(BITRATE_CONFIGS_KEY, bitrateConfigs)
                
                // 添加流量统计所需的参数
                putExtra("user_id", userId)
                putExtra("device_id", deviceId)
                putExtra("device_only_code", deviceOnlyCode)
                putExtra(DEVICE_NAME_KEY, deviceName) // 保存设备昵称
            }
            context.startActivity(intent)
        }
        
        // 剪贴板相关常量
        private const val SC_CONTROL_MSG_TYPE_GET_CLIPBOARD = 8
        private const val SC_CONTROL_MSG_TYPE_SET_CLIPBOARD = 9
        
        // 新增：模式常量
        private const val MODE_NORMAL = 0
        private const val MODE_FULLSCREEN = 1
    }
    
    // WebSocket相关
    private var wsClient: WebSocketClient? = null
    private var wsUrl: String = ""
    private var deviceWidth: Int = 0
    private var deviceHeight: Int = 0
    
    // 音频WebSocket客户端（用于免adb设备）
    private var audioWsClient: WebSocketClient? = null
    
    // 比特率配置
    private var bitrateConfigs: List<BitrateConfig> = emptyList()
    private var currentBitrateConfig: BitrateConfig? = null
    
    // 缓存SPS解析的准确分辨率
    private var realWidth: Int = 0
    private var realHeight: Int = 0
    
    // 添加标记，表示是否已经从SPS获取到准确分辨率
    private var gotRealResolution: Boolean = false
    
    // UI组件
    private lateinit var surfaceView: SurfaceView
    private lateinit var controlLayout: LinearLayout
    private lateinit var navigationLayout: LinearLayout
    
    // 解码器相关
    private var mediaCodec: MediaCodec? = null
    private var decoder: MediaCodec? = null
    private var surface: Surface? = null
    
    private var sps: ByteArray? = null
    private var pps: ByteArray? = null
    private var synchronizedSurface: Surface? = null // 添加缺失的变量
    // 解码器状态管理 - 简化版
    private var isDecoderConfigured = false
    private var isConfiguringDecoder = false
        private var cachedWidth = 0
    private var cachedHeight = 0
    private var spsProcessed = false
    private var lastConfigTime = 0L
    
    // 添加日志控制变量
    private var frameCount = 0
    private var logFrameInterval = 100  // 每隔100帧打印一次日志
    private var binaryMessageCount = 0
    private var logMessageInterval = 100  // 每隔100条消息打印一次日志
    private var startTime: Long = System.currentTimeMillis() // 添加缺失的变量
    
    // 线程池
    private val threadPool = ThreadPoolExecutor(
        2, 4, 0L, TimeUnit.MILLISECONDS,
        LinkedBlockingQueue()
    )
    // 帧缓冲队列 - 解决卡顿问题
    private val frameQueue = LinkedBlockingQueue<ByteArray>(200) // 限制队列大小
    private var isProcessingFrames = false
    private var lastProcessTime = 0L
    // 帧处理器 - 使用专用高优先级线程
private val frameProcessor = HandlerThread("FrameProcessor", Process.THREAD_PRIORITY_URGENT_DISPLAY).apply {
    start()
}.let { Handler(it.looper) }
    // 连接状态
    private val isConnected = AtomicBoolean(false)
    private val isReconnecting = AtomicBoolean(false)
    
    // 应用从后台切回前台的检测
    private var wasInBackground = false
    
    // 在类顶部添加成员变量
    private val activePointers = SparseArray<Pair<Float, Float>>() // 存储活动的触摸点（pointerId -> 坐标）
    
    // 用户活动记录
    private var lastUserActivityTime: Long = 0
    
    // 添加音频解码器
    private lateinit var audioDecoder: AudioDecoder
    private var isAudioSupported = true  // 是否支持音频
    
    // 添加免adb设备音频解码器
    private lateinit var noAdbAudioDecoder: noAdbAudioDecoder
    // 标记是否是免adb设备
    private var isNoAdbDevice = false
    
    // 新增错误检测属性
    private var consecutiveErrorCount = 0

    // 以下变量用于屏幕旋转
    private var currentOrientation = ORIENTATION_PORTRAIT
    private var isProcessingOrientationChange = false
    private var lastResolutionChangeTime = 0L
    private var pendingResolutionChange = false
    private var mustRefreshAfterRotation = false

    // WebSocket连接相关变量
    private var connectionAttempts = 0
    private val MAX_CONNECTION_ATTEMPTS = 2 // 最大连接尝试次数
    private var connectionTimeoutHandler: Handler? = null
    private val CONNECTION_TIMEOUT_MS = 10000 // 10秒超时
    
    // 新增重连相关变量
    private var reconnectionAttempts = 0
    private val MAX_RECONNECTION_ATTEMPTS = 3 // 最大重连尝试次数
    
    // **新增：解码器健康监控和花屏检测相关变量**
    private var consecutiveDecodeFailures = 0
    private val MAX_DECODE_FAILURES = 15 // 连续解码失败阈值
    private var lastSuccessfulDecodeTime = 0L
    private val DECODE_TIMEOUT_MS = 5000L // 5秒无成功解码视为超时
    private var corruptionDetected = false
    private var lastCorruptionRecoveryTime = 0L
    private val MIN_RECOVERY_INTERVAL_MS = 10000L // 最小恢复间隔10秒

    // 画质切换相关变量
    private var isChangingQuality = false
    private var qualityChangingStartTime: Long = 0
    private var qualityChangeTimeoutHandler: Handler? = null
    private val QUALITY_CHANGE_TIMEOUT_MS = 5000 // 5秒超时
    
    // 剪贴板管理器
    private lateinit var clipboardManager: ClipboardManager

    // 添加类成员变量
    private var inactivityHandler: Handler? = null
    private var inactivityCountdownTimer: CountDownTimer? = null
    private val INACTIVITY_COUNTDOWN_SECONDS = 5 // 5秒倒计时
    
    // 流量统计相关变量
    private var connectionStartTime: Long = 0 // 连接开始时间
    private var totalReceivedBytes: Long = 0 // 总接收字节数
    private var userId: Int = 0 // 用户ID
    private var deviceId: Int = 0 // 设备ID
    private var deviceOnlyCode: String = "" // 设备唯一码

    // 添加模式相关变量
    private var currentMode = MODE_NORMAL
    
    // 添加变量来记录上一次发送的延迟测量请求时间
    private var lastLatencyRequestTime: Long = 0
    
    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 隐藏状态栏和导航栏
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        // 设置沉浸式标志
        window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
        
        setContentView(R.layout.activity_scrcpy)
        
        // 请求存储权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(
                    arrayOf(
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        Manifest.permission.READ_EXTERNAL_STORAGE
                    ),
                    100
                )
            }
        }
 
        // 获取传递的参数
        wsUrl = intent.getStringExtra(WS_URL_KEY) ?: ""
        deviceWidth = intent.getIntExtra(DEVICE_WIDTH_KEY, 0)
        deviceHeight = intent.getIntExtra(DEVICE_HEIGHT_KEY, 0)
        
        // 获取比特率配置
        @Suppress("DEPRECATION")
        bitrateConfigs = intent.getParcelableArrayListExtra<BitrateConfig>(BITRATE_CONFIGS_KEY) ?: emptyList()
        
        // 获取流量统计所需的参数
        userId = intent.getIntExtra("user_id", 0)
        deviceId = intent.getIntExtra("device_id", 0)
        deviceOnlyCode = intent.getStringExtra("device_only_code") ?: ""
        
        // 初始化当前比特率配置（默认使用标准配置，或第一个配置）
        currentBitrateConfig = if (bitrateConfigs.size > 1) bitrateConfigs[1] else bitrateConfigs.firstOrNull()
        
        if (wsUrl.isEmpty()) {
            Toast.makeText(this, "参数错误，无法启动", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        // 判断是否是免adb设备（根据URL格式）
        isNoAdbDevice = wsUrl.contains("/session/")
        Log.d(TAG, "检测到设备类型: ${if (isNoAdbDevice) "免adb设备" else "adb设备"}")
        
        Log.d(TAG, "启动参数: wsUrl=$wsUrl, 分辨率=${deviceWidth}x${deviceHeight}")
        Log.d(TAG, "比特率配置: ${bitrateConfigs.size}个, 当前使用: ${currentBitrateConfig?.name}")
        
        // 初始化UI
        initViews()
        
        // 初始化Surface
        surfaceView.holder.addCallback(object : SurfaceHolder.Callback {
            override fun surfaceCreated(holder: SurfaceHolder) {
                Log.d(TAG, "Surface已创建")
                surface = holder.surface

                // 如果从后台恢复，总是重新连接WebSocket
                if (wasInBackground) {
                    Log.d(TAG, "从后台恢复，重新连接WebSocket")
                    connectWebSocket()
                }
                // 否则，如果是首次启动且未连接，则连接WebSocket
                else if (wsClient == null || !isConnected.get()) {
                    connectWebSocket()
                }
                // 如果已有SPS和PPS数据，配置解码器
                else if (sps != null && pps != null) {
                }
            }

         // 修复Surface回调 - 确保在Surface准备好后配置解码器
            override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
                Log.d(TAG, "Surface changed: width=$width, height=$height")
                if (holder.surface != null && holder.surface.isValid) {
                    synchronizedSurface = holder.surface
        
        // **Surface准备好后，如果有SPS数据就尝试配置解码器**
        if (sps != null && !isDecoderConfigured && !isConfiguringDecoder) {
            Log.d(TAG, "Surface准备完毕，尝试配置解码器")
            frameProcessor.postDelayed({
                tryConfigureDecoder()
            }, 50)
        }
                    
                    // 只有在获取到真实分辨率后才调整SurfaceView
                    if (gotRealResolution && realWidth > 0 && realHeight > 0) {
            Log.d(TAG, "调整Surface尺寸到: $realWidth x $realHeight")
                        deviceWidth = realWidth
                        deviceHeight = realHeight
                        adjustSurfaceViewSize()
                    } else {
            Log.d(TAG, "等待获取真实分辨率")
                    }
                }
            }

            override fun surfaceDestroyed(holder: SurfaceHolder) {
                Log.d(TAG, "Surface已销毁")
                surface = null
                releaseDecoderSafely()
                
                // 断开WebSocket连接，但不结束Activity
                disconnectWebSocket(false)
            }
        })

        // 设置触摸事件处理
        surfaceView.setOnTouchListener { _, event ->
            handleTouchEvent(event)
            true
        }

        // 初始化导航按钮
        setupNavigationButtons()

        // 设置控制按钮
        setupControlButtons()
        
        // 设置状态栏
        setupStatusBar()

        // 初始化音频解码器
        initAudioDecoder()

        // 初始化剪贴板管理器
        clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
        
        // 初始化模式切换功能
        setupModeToggle()
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            // 当窗口获得焦点时重新应用沉浸式模式
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
        }
    }
    

    
    // 根据屏幕方向更新全屏状态
    private fun updateFullscreenByOrientation(orientation: Int) {
        if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // 横屏时隐藏状态栏和导航栏，实现完全沉浸式体验
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN)
        }
    }

    private fun initViews() {
        surfaceView = findViewById(R.id.surface_view)
        controlLayout = findViewById(R.id.control_layout)
        navigationLayout = findViewById(R.id.navigation_layout)

        // 注意：此处不再调用adjustSurfaceViewSize()
        // 等到获取真实分辨率后再调整
    }
    
    // 添加设置状态栏的方法
    private fun setupStatusBar() {
        // 设置设备名称
        val deviceName = intent.getStringExtra(DEVICE_NAME_KEY) ?: "云手机"
        findViewById<TextView>(R.id.tv_device_name).text = deviceName
        
        // 初始化延迟显示
        updateLatencyDisplay(0)
        
        // 设置画质切换按钮点击事件
        findViewById<LinearLayout>(R.id.status_quality_container).setOnClickListener {
            showResolutionSelector()
        }
        
        // 设置初始画质显示
        val qualityName = currentBitrateConfig?.name ?: "标清"
        findViewById<TextView>(R.id.status_tv_quality).text = qualityName
        
        // 添加画质文本点击事件，与容器点击事件相同
        findViewById<TextView>(R.id.status_tv_quality).setOnClickListener {
            showResolutionSelector()
        }
    }
    
    // 更新延迟显示
    private fun updateLatencyDisplay(latencyMs: Int) {
        runOnUiThread {
            // 更新状态栏的延迟显示
            findViewById<TextView>(R.id.tv_latency).text = "${latencyMs}ms"
            
            // 同时更新悬浮窗工具栏中的延迟显示
            findViewById<TextView>(R.id.overlay_header_latency)?.text = "${latencyMs}ms"
        }
    }

    private fun setupNavigationButtons() {
        // 导航栏按钮
        // 返回按钮
        findViewById<ImageButton>(R.id.btn_back).setOnClickListener {
            sendKeyEvent(4) // KEYCODE_BACK
        }

        // 主页按钮
        findViewById<ImageButton>(R.id.btn_home).setOnClickListener {
            sendKeyEvent(3) // KEYCODE_HOME
        }

        // 最近任务按钮
        findViewById<ImageButton>(R.id.btn_recent).setOnClickListener {
            sendKeyEvent(187) // KEYCODE_APP_SWITCH
        }

        // 左侧导航栏按钮
        // 返回按钮
        findViewById<ImageButton>(R.id.btn_back_left).setOnClickListener {
            sendKeyEvent(4) // KEYCODE_BACK
        }

        // 主页按钮
        findViewById<ImageButton>(R.id.btn_home_left).setOnClickListener {
            sendKeyEvent(3) // KEYCODE_HOME
        }

        // 最近任务按钮
        findViewById<ImageButton>(R.id.btn_recent_left).setOnClickListener {
            sendKeyEvent(187) // KEYCODE_APP_SWITCH
        }
    }

    private fun setupControlButtons() {
        // 音量加按钮
        findViewById<ImageButton>(R.id.btn_volume_up).setOnClickListener {
            increaseVolume()
        }

        // 音量减按钮
        findViewById<ImageButton>(R.id.btn_volume_down).setOnClickListener {
            decreaseVolume()
        }

        // 电源按钮
        findViewById<ImageButton>(R.id.btn_power).setOnClickListener {
            sendKeyEvent(26) // KEYCODE_POWER
        }
        
        // 画质切换按钮已移至顶部状态栏，此处不再需要
        
        // 截图按钮
        findViewById<ImageButton>(R.id.btn_screenshot).setOnClickListener {
            captureScreenshot()
        }

        // 退出按钮
        findViewById<ImageButton>(R.id.btn_exit_right).setOnClickListener {
            saveTrafficStatsAndDisconnect(true, false) // 用户主动退出，不显示加载蒙版
        }

        // 重新连接按钮 - 注意这是ImageButton而不是Button
        findViewById<ImageButton>(R.id.btn_reconnect).setOnClickListener {
            reconnect()
        }

        // 获取剪贴板按钮
        findViewById<ImageButton>(R.id.btn_clipboard_copy).setOnClickListener {
            getRemoteClipboardContent()
        }

        // 设置剪贴板按钮
        findViewById<ImageButton>(R.id.btn_clipboard_paste).setOnClickListener {
            setRemoteClipboardContent()
        }
    }

    // 添加模式切换功能设置
    private fun setupModeToggle() {
        // 切换模式按钮 - 现在此按钮负责在全屏和普通模式之间切换
        findViewById<ImageButton>(R.id.btn_toggle_mode).setOnClickListener {
            toggleDisplayMode()
        }
        
        // 悬浮球点击事件
        val floatingBallContainer = findViewById<FrameLayout>(R.id.floating_ball_container)
        floatingBallContainer.setOnClickListener {
            showToolbarOverlay()
        }
        
        // 添加悬浮球拖动功能
        floatingBallContainer.setOnTouchListener(object : View.OnTouchListener {
            private var lastX: Float = 0f
            private var lastY: Float = 0f
            private var isDragging = false
            private val touchSlop = ViewConfiguration.get(this@ScrcpyActivity).scaledTouchSlop
            private var initialX = 0f
            private var initialY = 0f
            private var initialMarginLeft = 0
            private var initialMarginTop = 0
            private var initialMarginRight = 0
            private var initialMarginBottom = 0
            private var initialGravity = 0

            @SuppressLint("ClickableViewAccessibility")
            override fun onTouch(v: View, event: MotionEvent): Boolean {
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        lastX = event.rawX
                        lastY = event.rawY
                        initialX = event.rawX
                        initialY = event.rawY
                        isDragging = false
                        
                        // 记录初始位置的边距和gravity
                        val layoutParams = v.layoutParams as FrameLayout.LayoutParams
                        initialMarginLeft = layoutParams.leftMargin
                        initialMarginTop = layoutParams.topMargin
                        initialMarginRight = layoutParams.rightMargin
                        initialMarginBottom = layoutParams.bottomMargin
                        initialGravity = layoutParams.gravity
                        return true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        val deltaX = event.rawX - lastX
                        val deltaY = event.rawY - lastY
                        
                        // 判断是否为拖动操作
                        if (!isDragging && (Math.abs(event.rawX - initialX) > touchSlop || 
                                           Math.abs(event.rawY - initialY) > touchSlop)) {
                            isDragging = true
                            
                            // 在开始拖动时，将gravity设置为NO_GRAVITY，并根据当前位置设置margin
                            val layoutParams = v.layoutParams as FrameLayout.LayoutParams
                            
                            // 获取当前位置在屏幕中的坐标
                            val location = IntArray(2)
                            v.getLocationOnScreen(location)
                            
                            // 将屏幕坐标转换为相对于父容器的margin
                            val parent = v.parent as View
                            val parentLocation = IntArray(2)
                            parent.getLocationOnScreen(parentLocation)
                            
                            // 计算相对于父容器的位置
                            val relativeLeft = location[0] - parentLocation[0]
                            val relativeTop = location[1] - parentLocation[1]
                            
                            // 设置新的margin，保持当前位置不变
                            layoutParams.leftMargin = relativeLeft
                            layoutParams.topMargin = relativeTop
                            layoutParams.rightMargin = 0
                            layoutParams.bottomMargin = 0
                            layoutParams.gravity = Gravity.NO_GRAVITY
                            
                            v.layoutParams = layoutParams
                        }
                        
                        if (isDragging) {
                            // 更新悬浮球位置
                            val layoutParams = v.layoutParams as FrameLayout.LayoutParams
                            
                            // 获取父容器尺寸
                            val parentWidth = (v.parent as View).width
                            val parentHeight = (v.parent as View).height
                            
                            // 直接使用margin定位，不依赖gravity
                            layoutParams.leftMargin += deltaX.toInt()
                            layoutParams.topMargin += deltaY.toInt()
                            
                            // 防止拖出屏幕边界
                            layoutParams.leftMargin = layoutParams.leftMargin.coerceIn(0, parentWidth - v.width)
                            layoutParams.topMargin = layoutParams.topMargin.coerceIn(0, parentHeight - v.height)
                            
                            v.layoutParams = layoutParams
                        }
                        
                        lastX = event.rawX
                        lastY = event.rawY
                        return true
                    }
                    MotionEvent.ACTION_UP -> {
                        if (!isDragging) {
                            // 如果不是拖动操作，则当作点击处理
                            v.performClick()
                        } else {
                            // 拖动结束，保存当前位置
                            saveFloatingBallPosition(v)
                        }
                        return true
                    }
                    MotionEvent.ACTION_CANCEL -> {
                        if (isDragging) {
                            // 保存当前位置
                            saveFloatingBallPosition(v)
                        }
                        return true
                    }
                }
                return false
            }
        })
        
        // 工具栏蒙版关闭按钮
        findViewById<LinearLayout>(R.id.overlay_btn_exit).setOnClickListener {
            hideToolbarOverlay()
        }
        
        // 绑定蒙版中的其他按钮
        setupToolbarOverlayButtons()
    }
    
    // 设置工具栏蒙版内按钮事件
    private fun setupToolbarOverlayButtons() {
        // 音量加
        findViewById<LinearLayout>(R.id.overlay_btn_volume_up)?.setOnClickListener {
            increaseVolume()
            // 短暂隐藏工具栏，方便看到效果
            hideToolbarOverlay(true)
        }
        
        // 音量减
        findViewById<LinearLayout>(R.id.overlay_btn_volume_down)?.setOnClickListener {
            decreaseVolume()
            // 短暂隐藏工具栏，方便看到效果
            hideToolbarOverlay(true)
        }
        
        // 剪切按钮 (获取远程剪贴板)
        findViewById<LinearLayout>(R.id.overlay_btn_clipboard_copy)?.setOnClickListener {
            hideToolbarOverlay()
            getRemoteClipboardContent()
        }
        
        // 截图按钮
        findViewById<LinearLayout>(R.id.overlay_btn_screenshot)?.setOnClickListener {
            hideToolbarOverlay()
            captureScreenshot()
        }
        
        // 上传按钮
        findViewById<LinearLayout>(R.id.overlay_btn_upload)?.setOnClickListener {
            hideToolbarOverlay()
            Toast.makeText(this, "上传功能开发中", Toast.LENGTH_SHORT).show()
        }
        
        // 重启按钮
        findViewById<LinearLayout>(R.id.overlay_btn_restart)?.setOnClickListener {
            hideToolbarOverlay()
            reconnect()
        }
        
        // 切换模式按钮
        findViewById<LinearLayout>(R.id.overlay_btn_normal_mode)?.setOnClickListener {
            hideToolbarOverlay()
            toggleDisplayMode()
        }
        
        // 退出按钮
        findViewById<LinearLayout>(R.id.overlay_btn_exit)?.setOnClickListener {
            hideToolbarOverlay()
            saveTrafficStatsAndDisconnect(true, false) // 用户主动退出，不显示加载蒙版
        }
        
        // 导航按钮
        findViewById<ImageButton>(R.id.overlay_btn_back)?.setOnClickListener {
            hideToolbarOverlay()
            sendKeyEvent(4) // KEYCODE_BACK
        }
        
        findViewById<ImageButton>(R.id.overlay_btn_home)?.setOnClickListener {
            hideToolbarOverlay()
            sendKeyEvent(3) // KEYCODE_HOME
        }
        
        findViewById<ImageButton>(R.id.overlay_btn_recent)?.setOnClickListener {
            hideToolbarOverlay()
            sendKeyEvent(187) // KEYCODE_APP_SWITCH
        }
        
        // 添加顶部画质图标点击事件
        findViewById<TextView>(R.id.overlay_header_quality)?.setOnClickListener {
            hideToolbarOverlay()
            showResolutionSelector()
        }
        
        // 移除原有画质按钮的点击事件，或者复用为同样的功能
        findViewById<LinearLayout>(R.id.overlay_btn_quality)?.setOnClickListener {
            hideToolbarOverlay()
            showResolutionSelector()
        }
    }
    
    // 增加音量
    private fun increaseVolume() {
        try {
            // 1. 发送按键事件到设备
            sendKeyEvent(24) // KEYCODE_VOLUME_UP

            // 2. 增加本地音频播放音量
            if (isNoAdbDevice) {
                if (::noAdbAudioDecoder.isInitialized) {
                    noAdbAudioDecoder.increaseVolume()
                }
            } else {
                if (::audioDecoder.isInitialized) {
                    audioDecoder.increaseVolume()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "增加音量失败", e)
        }
    }

    // 减小音量
    private fun decreaseVolume() {
        try {
            // 1. 发送按键事件到设备
            sendKeyEvent(25) // KEYCODE_VOLUME_DOWN

            // 2. 减小本地音频播放音量
            if (isNoAdbDevice) {
                if (::noAdbAudioDecoder.isInitialized) {
                    noAdbAudioDecoder.decreaseVolume()
                }
            } else {
                if (::audioDecoder.isInitialized) {
                    audioDecoder.decreaseVolume()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "减小音量失败", e)
        }
    }


    // 添加KeyEvent处理方法
    private fun sendKeyEvent(keyCode: Int) {
        try {
            // 发送按下事件 (ACTION_DOWN = 0)
            val keyMsg = JSONObject().apply {
                put("msg_type", 0) // SC_CONTROL_MSG_TYPE_INJECT_KEYCODE
                put("keycode", keyCode)
                put("action", 0) // ACTION_DOWN
            }
            wsClient?.send(keyMsg.toString())
            Log.d(TAG, "发送按键DOWN事件: keyCode=$keyCode")

            // 延迟发送ACTION_UP事件 (ACTION_UP = 1)
            Handler(Looper.getMainLooper()).postDelayed({
                val upMsg = JSONObject().apply {
                    put("msg_type", 0) // SC_CONTROL_MSG_TYPE_INJECT_KEYCODE
                    put("keycode", keyCode)
                    put("action", 1) // ACTION_UP
                }
                wsClient?.send(upMsg.toString())
                Log.d(TAG, "发送按键UP事件: keyCode=$keyCode")
            }, 100)

            // 记录按键活动
            lastUserActivityTime = System.currentTimeMillis()
        } catch (e: Exception) {
            Log.e(TAG, "发送按键事件失败", e)
        }
    }

    private fun connectWebSocket() {
        if (isConnected.get() || isReconnecting.get()) {
            return
        }

        try {
            isReconnecting.set(true)
            
            // 增加连接尝试计数
            connectionAttempts++
            Log.d(TAG, "尝试连接 #$connectionAttempts")

            // 销毁旧的连接并创建新连接
            wsClient?.close()
            wsClient = null
            
            // 关闭音频连接（如果存在）
            audioWsClient?.close()
            audioWsClient = null

            // 确保wsUrl是完整的URL
            val finalUrl = if (!wsUrl.startsWith("ws://") && !wsUrl.startsWith("wss://")) {
                "ws://$wsUrl"
            } else {
                wsUrl
            }

            // 显示连接状态
            runOnUiThread {
                findViewById<TextView>(R.id.tv_connection_info).apply {
                    text = "正在连接设备..."
                    visibility = View.VISIBLE
                }
            }
            
            // 设置连接超时计时器 - 10秒后直接返回上一页
            connectionTimeoutHandler = Handler(Looper.getMainLooper())
            connectionTimeoutHandler?.postDelayed({
                if (!isConnected.get() && !isFinishing) {
                    Log.e(TAG, "WebSocket连接超时，退出页面")
                    
                    // 清理资源
                    wsClient?.close()
                    wsClient = null
                    audioWsClient?.close()
                    audioWsClient = null
                    isReconnecting.set(false)
                    
                    runOnUiThread {
                        Toast.makeText(this@ScrcpyActivity, "连接超时，请检查网络和设备状态", Toast.LENGTH_LONG).show()
                        // 直接结束Activity返回上一页
                        finish()
                    }
                }
            }, CONNECTION_TIMEOUT_MS.toLong())

            // 创建主WebSocket连接（视频通道）
            wsClient = createWebSocketClient(finalUrl, false)
            wsClient?.connect()
            
            // 如果是免adb设备，创建音频WebSocket连接
            // if (isNoAdbDevice) {
            //     val audioUrl = if (finalUrl.contains("?")) {
            //         "$finalUrl&audio=1"
            //     } else {
            //         "$finalUrl?audio=1"
            //     }
                
            //     Log.d(TAG, "创建免adb设备音频连接: $audioUrl")
            //     audioWsClient = createWebSocketClient(audioUrl, true)
            //     audioWsClient?.connect()
            // }
            
        } catch (e: Exception) {
            Log.e(TAG, "创建WebSocket连接失败: ${e.message}")
            isReconnecting.set(false)
            
            // 取消超时计时器
            connectionTimeoutHandler?.removeCallbacksAndMessages(null)
            connectionTimeoutHandler = null
            
            runOnUiThread {
                findViewById<ProgressBar>(R.id.progress_bar).visibility = View.GONE
                findViewById<TextView>(R.id.tv_connection_info).apply {
                    text = "连接失败"
                    visibility = View.VISIBLE
                }
                Toast.makeText(this, "连接失败，请检查网络和设备状态", Toast.LENGTH_SHORT).show()
                // 直接结束Activity返回上一页
                finish()
            }
        }
    }
    
    // 新增：创建WebSocket客户端
    private fun createWebSocketClient(url: String, isAudio: Boolean): WebSocketClient {
        return object : WebSocketClient(URI(url)) {
            override fun onOpen(handshakedata: ServerHandshake?) {
                // 取消超时计时器
                if (!isAudio) {
                    connectionTimeoutHandler?.removeCallbacksAndMessages(null)
                    connectionTimeoutHandler = null
                    
                    isConnected.set(true)
                    isReconnecting.set(false)
                    consecutiveErrorCount = 0
                    // 重置连接尝试计数
                    connectionAttempts = 0
                    
                    // 记录连接开始时间
                    connectionStartTime = System.currentTimeMillis()
                    totalReceivedBytes = 0
                    
                    // 连接成功后开始定期测量延迟
                    startLatencyMeasurement()
                    
                    // 检查是否是画质切换
                    if (isChangingQuality && currentBitrateConfig != null) {
                        Log.d(TAG, "画质切换连接成功")
                        runOnUiThread {
                            // 调用画质切换成功方法
                            onQualityChangeSuccess(currentBitrateConfig!!)
                        }
                    }
                }
                
                Log.d(TAG, "${if (isAudio) "音频" else "视频"}WebSocket连接已建立")
            }

            override fun onMessage(message: String?) {
                try {
                    // 处理文本消息
                    if (message != null) {
                        // 累计接收到的字节数
                        if (!isAudio) {
                            totalReceivedBytes += message.length
                        }
                        
                        val jsonObject = JSONObject(message)
                        val msgType = jsonObject.optInt("msg_type", -1)
                        
                        // 处理延迟测量响应
                        if (msgType == 1000) { // 假设1000是延迟测量响应类型
                            val timestamp = jsonObject.optLong("timestamp", 0)
                            if (timestamp > 0) {
                                val latency = System.currentTimeMillis() - timestamp
                                updateLatencyDisplay(latency.toInt())
                            }
                        }
                        // 处理1001类型消息（接收服务器时间戳）
                        else if (message.contains("\"now\":")) {
                            try {
                                // 解析JSON
                                val jsonData = JSONObject(message)
                                val serverTimestamp = jsonData.optLong("now", 0)
                                
                                if (serverTimestamp > 0 && lastLatencyRequestTime > 0) {
                                    // 计算往返延迟
                                    val roundTripLatency = System.currentTimeMillis() - lastLatencyRequestTime
                                    
                                    // 计算客户端到服务器的延迟（服务器时间戳是秒为单位，转换为毫秒）
                                    val serverTimeMs = serverTimestamp * 1000
                                    val clientToServerLatency = serverTimeMs - lastLatencyRequestTime
                                    
                                    // 计算服务器到客户端的延迟
                                    val serverToClientLatency = roundTripLatency - clientToServerLatency
                                    
                                    Log.d(TAG, "延迟测量: 往返=${roundTripLatency}ms, 客户端到服务器=${clientToServerLatency}ms, 服务器到客户端=${serverToClientLatency}ms")
                                    
                                    // 显示往返延迟
                                    updateLatencyDisplay(roundTripLatency.toInt())
                                }
                            } catch (e: Exception) {
                                Log.e(TAG, "解析延迟测量响应失败", e)
                            }
                        }
                        // 处理长时间未操作的消息
                        else if (msgType == 400) {
                            Log.d(TAG, "收到长时间未操作消息: $message")
                            runOnUiThread {
                                showInactivityAlert()
                            }
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理WebSocket文本消息失败", e)
                }
            }

            override fun onMessage(bytes: ByteBuffer?) {
                if (bytes == null) return
                
                try {
                    // 复制数据并处理
                    val data = ByteArray(bytes.remaining())
                    bytes.get(data)
                    
                    // 累计接收到的字节数
                    if (!isAudio) {
                        totalReceivedBytes += data.size
                    }
                    
                    // 检查是否是剪贴板响应数据
                    if (!isAudio && data.size > 5 && 
                        data[0] == 0x00.toByte() && data[1] == 0x00.toByte() && 
                        data[2] == 0x00.toByte() && data[3] == 0x02.toByte()) {
                        
                        // 剪贴板获取响应 (0x00)
                        if (data[4] == 0x00.toByte()) {
                            handleGetClipboardResponse(data)
                            return
                        }
                        // 剪贴板设置响应 (0x01)
                        else if (data[4] == 0x01.toByte()) {
                            handleSetClipboardResponse(data)
                            return
                        }
                    }
                    
                    // 处理其他类型的二进制消息
                    if (isAudio) {
                        // 音频WebSocket只处理音频数据
                        processAudioData(data)
                    } else {
                        // 主WebSocket处理视频和控制数据
                        handleBinaryMessage(data)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理二进制消息失败", e)
                }
            }

            override fun onClose(code: Int, reason: String?, remote: Boolean) {
                if (!isAudio) {
                    // 主连接关闭事件
                    
                    // 取消超时计时器
                    connectionTimeoutHandler?.removeCallbacksAndMessages(null)
                    connectionTimeoutHandler = null
                    
                    isConnected.set(false)
                    
                    // 停止延迟测量
                    stopLatencyMeasurement()
                    
                    // 如果不是自己主动关闭的，且没有正在关闭Activity，则退出
                    if (remote && !isFinishing && !wasInBackground) {
                        Log.d(TAG, "WebSocket连接被远程关闭，退出页面")
                        runOnUiThread {
                            // Toast.makeText(this@ScrcpyActivity, "连接已断开", Toast.LENGTH_SHORT).show()
                            finish()
                        }
                    } else if (wasInBackground) {
                        Log.d(TAG, "应用在后台，WebSocket关闭不触发退出")
                    } else {
                        Log.d(TAG, "本地主动关闭WebSocket连接")
                    }
                } else {
                    // 音频连接关闭事件
                    Log.d(TAG, "音频WebSocket连接已关闭: code=$code, reason=$reason, remote=$remote")
                }
            }

            override fun onError(ex: Exception?) {
                if (!isAudio) {
                    // 主连接错误事件
                    
                    // 取消超时计时器
                    connectionTimeoutHandler?.removeCallbacksAndMessages(null)
                    connectionTimeoutHandler = null
                    
                    Log.e(TAG, "视频WebSocket连接错误", ex)
                    
                    runOnUiThread {
                        findViewById<TextView>(R.id.tv_connection_info).apply {
                            text = "连接失败，请重试"
                            visibility = View.VISIBLE
                        }
                        
                        // 直接退出Activity
                        Toast.makeText(this@ScrcpyActivity, "连接失败，请检查网络和设备状态", Toast.LENGTH_SHORT).show()
                        finish()
                    }
                } else {
                    // 音频连接错误事件
                    Log.e(TAG, "音频WebSocket连接错误", ex)
                }
            }
        }.apply {
            // 设置连接超时
            setConnectionLostTimeout(5000)
        }
    }

    // 添加延迟测量相关变量
    private var latencyMeasurementHandler: Handler? = null
    private val LATENCY_MEASUREMENT_INTERVAL = 5000L // 5秒测量一次延迟
    
    // 开始定期测量延迟
    private fun startLatencyMeasurement() {
        // 停止已有的测量
        stopLatencyMeasurement()
        
        // 创建新的Handler
        latencyMeasurementHandler = Handler(Looper.getMainLooper())
        
        // 定期执行延迟测量
        latencyMeasurementHandler?.post(object : Runnable {
            override fun run() {
                if (isConnected.get() && wsClient != null) {
                    measureLatency()
                }
                // 安排下一次测量
                latencyMeasurementHandler?.postDelayed(this, LATENCY_MEASUREMENT_INTERVAL)
            }
        })
    }
    
    // 停止延迟测量
    private fun stopLatencyMeasurement() {
        latencyMeasurementHandler?.removeCallbacksAndMessages(null)
        latencyMeasurementHandler = null
    }
    
    // 测量延迟的方法
    private fun measureLatency() {
        try {
            // 记录测量开始时间
            lastLatencyRequestTime = System.currentTimeMillis()
            
            // 构建ping消息
            val pingMessage = JSONObject().apply {
                put("msg_type", 1001) // 使用1001作为延迟测量请求类型
                put("timestamp", lastLatencyRequestTime)
            }
            
            // 发送ping消息
            wsClient?.send(pingMessage.toString())
            Log.d(TAG, "发送延迟测量请求: timestamp=$lastLatencyRequestTime")
        } catch (e: Exception) {
            Log.e(TAG, "测量延迟失败", e)
        }
    }

// 优化二进制消息处理方法 - 统一使用新的解码逻辑
    private fun handleBinaryMessage(data: ByteArray) {
        try {
            // 检测NAL单元
            if (data.size >= 5 &&
                data[0] == 0x00.toByte() &&
                data[1] == 0x00.toByte() &&
                data[2] == 0x00.toByte() &&
                data[3] == 0x01.toByte()) {
                
            // **直接调用统一的解码方法，不再重复处理**
            decodeFrame(data)
            } 
            // 检测ADTS音频头 (0xFF 0xF0 开头)
            else if (data.size >= 7 &&
                    (data[0].toInt() and 0xFF) == 0xFF &&
                    (data[1].toInt() and 0xF0) == 0xF0) {
            
                processAudioData(data)
            }
            // 自定义音频格式 (0x00 0x00 0x00 0x03)
            else if (data.size >= 4 &&
                    data[0] == 0x00.toByte() &&
                    data[1] == 0x00.toByte() &&
                    data[2] == 0x00.toByte() &&
                    data[3] == 0x03.toByte()) {
                
                // 跳过前4字节头部，提取真实音频数据
                val audioData = if (data.size > 4) {
                    data.copyOfRange(4, data.size)
                } else {
                    ByteArray(0)
                }
            
            // 处理提取后的音频数据
                processAudioData(audioData)
            }
            // 其他未知格式，尝试作为音频数据处理
            else {
                processAudioData(data)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理二进制消息失败: ${e.message}")
        }
    }
    
    // 新增：处理音频数据，根据设备类型选择解码器
    private fun processAudioData(data: ByteArray) {
        try {
            if (isNoAdbDevice) {
                // 免adb设备使用noAdbAudioDecoder
                if (::noAdbAudioDecoder.isInitialized) {
                    noAdbAudioDecoder.processAudioData(data)
                }
            } else {
                // adb设备使用标准AudioDecoder
                if (::audioDecoder.isInitialized) {
                    audioDecoder.processAudioData(data)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理音频数据失败: ${e.message}")
        }
    }

    // 处理视频数据 - 简化处理
    private fun processVideoData(data: ByteArray) {
        try {
            // 直接将帧提交给解码器
            decodeFrame(data)
        } catch (e: Exception) {
            Log.e(TAG, "处理视频数据失败: ${e.message}")
        }
    }


// 解码视频帧 - 重构版本
    private fun decodeFrame(data: ByteArray) {
        if (data.size <= 4) return
        
    // 快速NAL类型检测
            val nalType = getNalType(data)
            
            when (nalType) {
        7 -> handleSPS(data)      // SPS帧
        8 -> handlePPS(data)      // PPS帧  
        5 -> handleKeyFrame(data) // 关键帧
        else -> handleRegularFrame(data) // 普通帧
    }
}
// 快速NAL类型提取
    private fun getNalType(data: ByteArray): Int {
        return when {
            data.size >= 5 && data[0] == 0x00.toByte() && data[1] == 0x00.toByte() &&
        data[2] == 0x00.toByte() && data[3] == 0x01.toByte() -> 
                (data[4].toInt() and 0x1F)
            data.size >= 4 && data[0] == 0x00.toByte() && data[1] == 0x00.toByte() &&
        data[2] == 0x01.toByte() -> 
                (data[3].toInt() and 0x1F)
        else -> (data[0].toInt() and 0x1F)
            }
            }
// SPS处理 - 添加首次自动旋转逻辑
private fun handleSPS(data: ByteArray) {
    if (spsProcessed && Arrays.equals(sps, data)) {
        return // 相同SPS，跳过
    }
    
    sps = data
    val (width, height) = extractResolutionFromSPS(data)
    
    if (width > 0 && height > 0) {
        val needReconfigure = (cachedWidth != width || cachedHeight != height)
        val isFirstSPS = !spsProcessed // 标记是否首次解析SPS
        
        // **关键修复：设置准确分辨率标志**
        realWidth = width
        realHeight = height
        gotRealResolution = true
        
        cachedWidth = width
        cachedHeight = height
        spsProcessed = true
        
        Log.d(TAG, "SPS解析: ${width}x${height}, 需要重配置: $needReconfigure, 首次解析: $isFirstSPS")
        Log.d(TAG, "设置真实分辨率: ${realWidth}x${realHeight}, gotRealResolution=$gotRealResolution")
        
        if (needReconfigure) {
            // 检测屏幕旋转
            val isRotation = (width == deviceHeight && height == deviceWidth)
            if (isRotation) {
                handleScreenRotation(width, height)
            } else {
                deviceWidth = width
                deviceHeight = height
                
                // **首次进入时的自动旋转逻辑**
                if (isFirstSPS) {
                    handleFirstTimeOrientation(width, height)
                } else {
                    // **优化：不是首次且不是旋转，尝试温和重配置**
                    handleResolutionChange(width, height)
                }
            }
        }
    }
    
    tryConfigureDecoder()
}
// PPS处理
private fun handlePPS(data: ByteArray) {
            pps = data
    Log.d(TAG, "收到PPS帧")
    tryConfigureDecoder()
}
// 关键帧处理 - 改进同步逻辑
private fun handleKeyFrame(data: ByteArray) {
    Log.d(TAG, "收到关键帧")
    
    runOnUiThread {
        findViewById<TextView>(R.id.tv_connection_info)?.visibility = View.GONE
        findViewById<ProgressBar>(R.id.progress_bar)?.visibility = View.GONE
    }
    
    if (!isDecoderConfigured) {
        tryConfigureDecoder()
    }
    
    if (isDecoderConfigured) {
        // **关键修复：关键帧到达时，温和地清理队列，但保持帧序列完整性**
        val queueSize = frameQueue.size
        if (queueSize > 30) {
            // 只保留最近的10帧，确保不会完全清空队列
            val framesToKeep = mutableListOf<ByteArray>()
            val keepCount = minOf(10, queueSize)
            repeat(keepCount) {
                frameQueue.poll()?.let { framesToKeep.add(it) }
            }
            frameQueue.clear()
            framesToKeep.forEach { frameQueue.offer(it) }
            Log.d(TAG, "关键帧温和清理队列：${queueSize} -> ${frameQueue.size}")
        }
        
        // **关键帧优先处理，但要确保解码器状态稳定**
        if (!isProcessingFrames) {
            processVideoFrame(data)
        } else {
            // 如果正在处理其他帧，将关键帧插入队列头部
            val tempQueue = LinkedBlockingQueue<ByteArray>()
            tempQueue.offer(data) // 关键帧优先
            while (frameQueue.isNotEmpty()) {
                frameQueue.poll()?.let { tempQueue.offer(it) }
            }
            frameQueue.clear()
            while (tempQueue.isNotEmpty()) {
                tempQueue.poll()?.let { frameQueue.offer(it) }
            }
            Log.d(TAG, "关键帧已插入队列头部")
        }
    }
}
// 普通帧处理 - 改进丢帧策略，防止解码器状态损坏
private fun handleRegularFrame(data: ByteArray) {
    if (!isDecoderConfigured) return
    
    // **防止在旋转或配置期间处理帧**
    if (isProcessingOrientationChange || isConfiguringDecoder) {
        Log.v(TAG, "跳过帧处理：正在旋转或配置中")
        return
    }
    
    val queueSize = frameQueue.size
    
    // **改进的丢帧策略：更保守，避免破坏解码器状态**
    when {
        queueSize >= 200 -> {
            // 队列严重积压时，保留最近一半的帧
            val keepCount = queueSize / 2
            val framesToKeep = mutableListOf<ByteArray>()
            repeat(keepCount) {
                frameQueue.poll()?.let { framesToKeep.add(it) }
            }
            frameQueue.clear()
            framesToKeep.forEach { frameQueue.offer(it) }
            Log.w(TAG, "严重积压(${queueSize})，保守清理到${frameQueue.size}")
        }
        queueSize >= 120 -> {
            // 队列积压时，丢弃较老的帧，但不要太激进
            repeat(queueSize / 3) {
                frameQueue.poll()
            }
            Log.w(TAG, "中等积压(${queueSize})，适度清理到${frameQueue.size}")
        }
        queueSize >= 80 -> {
            // 轻微积压，只丢弃少量帧
            repeat(minOf(10, queueSize / 4)) {
                frameQueue.poll()
            }
            Log.v(TAG, "轻微积压(${queueSize})，轻微清理到${frameQueue.size}")
        }
    }
    
    // **使用offer而不是add，避免阻塞**
    if (!frameQueue.offer(data)) {
        Log.w(TAG, "队列已满，丢弃当前帧")
        return
    }
    
    // **智能触发处理：根据队列状态和系统负载决定**
    when {
        queueSize > 50 -> {
            // 积压较多时，立即处理
            processFrameQueue()
        }
        queueSize > 20 -> {
            // 中等积压时，稍微延迟处理
            frameProcessor.post { processFrameQueue() }
        }
        else -> {
            // 正常情况，常规处理
            frameProcessor.postDelayed({ processFrameQueue() }, 8)
        }
    }
}

// **新增：温和的分辨率变化处理，避免完全重置解码器**
private fun handleResolutionChange(width: Int, height: Int) {
    Log.d(TAG, "处理分辨率变化: ${deviceWidth}x${deviceHeight} -> ${width}x${height}")
    
    // 更新分辨率
    deviceWidth = width
    deviceHeight = height
    
    // **关键：不重置解码器，只重新配置**
    runOnUiThread {
        // 立即调整SurfaceView尺寸
        adjustSurfaceViewSize()
    }
    
    // **温和重配置：只有在解码器未配置时才重新配置**
    if (!isDecoderConfigured) {
        Log.d(TAG, "解码器未配置，尝试配置")
        frameProcessor.postDelayed({
            tryConfigureDecoder()
        }, 100)
    } else {
        Log.d(TAG, "解码器已配置，保持当前配置")
    }
}

// 首次进入时的方向处理
private fun handleFirstTimeOrientation(width: Int, height: Int) {
    val isContentLandscape = width > height // 内容是否为横屏
    val currentOrientation = resources.configuration.orientation
    val isCurrentLandscape = currentOrientation == Configuration.ORIENTATION_LANDSCAPE
    
    Log.d(TAG, "首次方向检测: 内容${if (isContentLandscape) "横屏" else "竖屏"}(${width}x${height}), 当前设备${if (isCurrentLandscape) "横屏" else "竖屏"}")
    
            runOnUiThread {
        if (isContentLandscape && !isCurrentLandscape) {
            // 内容是横屏，但设备当前是竖屏 -> 强制旋转为横屏
            Log.d(TAG, "内容为横屏，强制旋转设备为横屏")
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            
            // 显示旋转提示
            
        } else if (!isContentLandscape && isCurrentLandscape) {
            // 内容是竖屏，但设备当前是横屏 -> 强制旋转为竖屏
            Log.d(TAG, "内容为竖屏，强制旋转设备为竖屏")
            requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            
            // 显示旋转提示
            
        } else {
            Log.d(TAG, "内容方向与设备方向匹配，无需旋转")
        }
    }
    
    // 重置解码器
    resetDecoder()
}


// 处理帧队列 - 彻底重构，消除定期卡顿
private fun processFrameQueue() {
    if (isProcessingFrames) return
    
    val currentTime = System.currentTimeMillis()
    
    // **避免过于频繁的调度，但不要延迟太久**
    if (currentTime - lastProcessTime < 8) {
        return
    }
    
    isProcessingFrames = true
    lastProcessTime = currentTime
    
    frameProcessor.post {
        try {
            var processedCount = 0
            val startTime = System.currentTimeMillis()
            
            // **连续处理模式，一次性处理更多帧**
            val maxProcessTime = 25 // 最多处理25ms
            val maxFrames = 15 // 最多处理15帧
            
            while (frameQueue.isNotEmpty() && 
                   processedCount < maxFrames && 
                   (System.currentTimeMillis() - startTime) < maxProcessTime) {
                
                val frameData = frameQueue.poll()
                if (frameData != null) {
                    processVideoFrame(frameData)
                    processedCount++
                }
            }
            
            val queueSize = frameQueue.size
            // Log.v(TAG, "批量处理${processedCount}帧，队列剩余${queueSize}，耗时${System.currentTimeMillis() - startTime}ms")
            
                    } finally {
            isProcessingFrames = false
            
            // **如果队列还有帧，立即继续处理，不要延迟**
            if (frameQueue.isNotEmpty()) {
                // 根据队列积压情况决定是否立即处理
                val queueSize = frameQueue.size
                if (queueSize > 50) {
                    // 队列积压严重，立即处理
                    processFrameQueue()
                } else if (queueSize > 20) {
                    // 队列中等积压，短延迟处理
                    frameProcessor.post { processFrameQueue() }
                } else {
                    // 队列正常，稍微延迟
                    frameProcessor.postDelayed({ processFrameQueue() }, 8)
                    }
                }
            }
        }
    }
    
// 统一的视频帧处理
// 修复画质切换超时问题 - 优化检测逻辑
private fun processVideoFrame(data: ByteArray): Boolean {
    val codec = decoder ?: return false
    
    // **检查是否检测到花屏，如果是则暂停处理**
    if (corruptionDetected) {
        Log.v(TAG, "花屏恢复中，暂停处理帧")
        return false
    }
    
    try {
        val inputBufferId = codec.dequeueInputBuffer(0)
        if (inputBufferId >= 0) {
            val inputBuffer = codec.getInputBuffer(inputBufferId) ?: return false
            
            inputBuffer.clear()
            inputBuffer.put(data)
            
            codec.queueInputBuffer(inputBufferId, 0, data.size, System.nanoTime() / 1000, 0)
        }
        
        // **处理输出缓冲区 - 检测画质切换完成**
        val bufferInfo = MediaCodec.BufferInfo()
        var outputBufferId = codec.dequeueOutputBuffer(bufferInfo, 0)
        var hasOutput = false
        
        while (outputBufferId >= 0) {
            hasOutput = true
            
            // 有可用的输出缓冲区，隐藏加载提示
            runOnUiThread {
                findViewById<TextView>(R.id.tv_connection_info).visibility = View.GONE
                
                // **画质切换完成检测 - 优化逻辑**
                if (isChangingQuality) {
                    val isKeyFrame = (bufferInfo.flags and MediaCodec.BUFFER_FLAG_KEY_FRAME) != 0
                    val elapsedTime = System.currentTimeMillis() - qualityChangingStartTime
                    
                    // **更宽松的完成条件：有输出帧就认为切换完成**
                    if (isKeyFrame || elapsedTime > 500) { // 减少到500ms
                        Log.d(TAG, "画质切换完成，已解码新画质帧${if (isKeyFrame) "（关键帧）" else ""}, 耗时: ${elapsedTime}ms")
                        
                        // **立即关闭蒙版**
                        findViewById<View>(R.id.quality_change_overlay).visibility = View.GONE
                        findViewById<TextView>(R.id.tv_quality_changing).visibility = View.GONE
                        
                        isChangingQuality = false
                        qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
                        
                        // 显示成功提示
                        Toast.makeText(this@ScrcpyActivity, "画质切换完成", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            
            codec.releaseOutputBuffer(outputBufferId, true)
            outputBufferId = codec.dequeueOutputBuffer(bufferInfo, 0)
        }
        
        // **记录解码结果**
        if (hasOutput) {
            recordDecodeSuccess()
        } else {
            // 没有输出但也不算失败，只是暂时没有可用的输出缓冲区
        }
        
        return true
    } catch (e: Exception) {
        Log.w(TAG, "处理视频帧失败: ${e.message}")
        
        // **记录解码失败**
        recordDecodeFailure()
        
        return false
    }
}
    // **新增：安全地刷新解码器缓冲区**
    private fun flushDecoderSafely() {
        try {
            val codec = decoder
            if (codec != null && isDecoderConfigured) {
                Log.d(TAG, "正在刷新解码器缓冲区")
                
                // 先停止解码器
                codec.stop()
                
                // 重新启动，这会清空所有内部缓冲区
                codec.start()
                
                Log.d(TAG, "解码器缓冲区已刷新")
            }
        } catch (e: Exception) {
            Log.w(TAG, "刷新解码器缓冲区失败: ${e.message}")
            // 如果刷新失败，强制重置解码器
            resetDecoder()
        }
    }

    // **新增：花屏检测和恢复机制**
    private fun detectAndHandleCorruption() {
        val currentTime = System.currentTimeMillis()
        
        // 检测连续解码失败
        if (consecutiveDecodeFailures >= MAX_DECODE_FAILURES) {
            Log.w(TAG, "检测到连续解码失败${consecutiveDecodeFailures}次，可能存在花屏")
            triggerCorruptionRecovery("连续解码失败")
            return
        }
        
        // 检测解码超时
        if (lastSuccessfulDecodeTime > 0 && 
            currentTime - lastSuccessfulDecodeTime > DECODE_TIMEOUT_MS) {
            Log.w(TAG, "解码超时${currentTime - lastSuccessfulDecodeTime}ms，可能存在花屏")
            triggerCorruptionRecovery("解码超时")
            return
        }
        
        // 检测队列长期积压（间接指示解码器可能有问题）
        if (frameQueue.size > 100) {
            Log.w(TAG, "帧队列长期积压${frameQueue.size}，可能存在解码器问题")
            triggerCorruptionRecovery("队列积压")
        }
    }
    
    // **新增：触发花屏恢复**
    private fun triggerCorruptionRecovery(reason: String) {
        val currentTime = System.currentTimeMillis()
        
        // 防止频繁恢复
        if (currentTime - lastCorruptionRecoveryTime < MIN_RECOVERY_INTERVAL_MS) {
            Log.d(TAG, "恢复间隔太短，跳过恢复操作")
            return
        }
        
        Log.w(TAG, "触发花屏恢复，原因：$reason")
        corruptionDetected = true
        lastCorruptionRecoveryTime = currentTime
        
        try {
            // 1. 停止当前帧处理
            isProcessingFrames = true
            
            // 2. 清空帧队列
            frameQueue.clear()
            
            // 3. 重置解码器状态
            resetDecoder()
            
            // 4. 重置计数器
            consecutiveDecodeFailures = 0
            lastSuccessfulDecodeTime = currentTime
            
            // 5. 延迟重新启动帧处理
            frameProcessor.postDelayed({
                isProcessingFrames = false
                corruptionDetected = false
                Log.d(TAG, "花屏恢复完成，恢复正常解码")
                
                // 如果有SPS数据，重新配置解码器
                if (sps != null) {
                    frameProcessor.postDelayed({
                        tryConfigureDecoder()
                    }, 500)
                }
            }, 1000)
            
            // 6. 显示恢复提示
            runOnUiThread {
                Toast.makeText(this, "检测到画面异常，正在自动修复...", Toast.LENGTH_SHORT).show()
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "花屏恢复失败", e)
            isProcessingFrames = false
            corruptionDetected = false
        }
    }
    
    // **新增：记录解码成功**
    private fun recordDecodeSuccess() {
        consecutiveDecodeFailures = 0
        lastSuccessfulDecodeTime = System.currentTimeMillis()
    }
    
    // **新增：记录解码失败**
    private fun recordDecodeFailure() {
        consecutiveDecodeFailures++
        Log.v(TAG, "解码失败计数：$consecutiveDecodeFailures")
        
        // 触发检测
        detectAndHandleCorruption()
    }

// **优化屏幕旋转处理 - 减少解码器重置，提高流畅性**
private fun handleScreenRotation(newWidth: Int, newHeight: Int) {
    Log.d(TAG, "处理屏幕旋转: ${deviceWidth}x${deviceHeight} -> ${newWidth}x${newHeight}")
    
    // **防止并发配置和旋转过程中的多次调用**
    if (isConfiguringDecoder || isProcessingOrientationChange) {
        Log.d(TAG, "解码器正在配置或处理旋转中，跳过旋转处理")
        return
    }
    
    // 标记正在处理旋转
    isProcessingOrientationChange = true
    
    try {
        // **优化：先尝试温和处理，避免完全重置解码器**
        val needDecoderReset = shouldResetDecoderForRotation(newWidth, newHeight)
        
        if (needDecoderReset) {
            Log.d(TAG, "旋转需要重置解码器")
            
            // **暂停帧处理，清空队列**
            isProcessingFrames = true
            frameQueue.clear()
            
            // **温和地刷新解码器缓冲区**
            try {
                decoder?.flush() // 使用flush而不是stop/start
                Log.d(TAG, "解码器缓冲区已刷新")
            } catch (e: Exception) {
                Log.w(TAG, "解码器flush失败，进行完全重置: ${e.message}")
                resetDecoder()
            }
        } else {
            Log.d(TAG, "旋转不需要重置解码器，仅调整UI")
        }
        
        // 更新分辨率
        deviceWidth = newWidth
        deviceHeight = newHeight
        
        Log.d(TAG, "屏幕旋转：分辨率已更新为 ${newWidth}x${newHeight}")
        
        // **运行时旋转处理（非首次）**
        runOnUiThread {
            val isLandscape = newWidth > newHeight
            val currentOrientation = resources.configuration.orientation
            val isCurrentLandscape = currentOrientation == Configuration.ORIENTATION_LANDSCAPE
            
            // 只有当内容方向与当前设备方向不匹配时才旋转
            if (isLandscape && !isCurrentLandscape) {
                Log.d(TAG, "运行时检测到横屏内容，旋转为横屏")
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            } else if (!isLandscape && isCurrentLandscape) {
                Log.d(TAG, "运行时检测到竖屏内容，旋转为竖屏")
                requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }
            
            // **立即调整SurfaceView大小**
            adjustSurfaceViewSize()
        }
        
        // **根据是否重置解码器决定后续处理**
        if (needDecoderReset) {
            // **延迟重新配置解码器**
            frameProcessor.postDelayed({
                try {
                    val holder = surfaceView.holder
                    if (holder?.surface?.isValid == true) {
                        Log.d(TAG, "Surface准备完成，开始重新配置解码器")
                        tryConfigureDecoder()
                    } else {
                        Log.w(TAG, "Surface未准备好，等待Surface回调")
                    }
                } finally {
                    isProcessingFrames = false
                    isProcessingOrientationChange = false
                }
            }, 100) // 减少延迟
        } else {
            // **不需要重置解码器，直接恢复处理**
            frameProcessor.postDelayed({
                isProcessingFrames = false
                isProcessingOrientationChange = false
                Log.d(TAG, "旋转处理完成，恢复帧处理")
            }, 50)
        }
        
    } catch (e: Exception) {
        Log.e(TAG, "处理屏幕旋转时发生错误", e)
        // 确保状态被重置
        isProcessingFrames = false
        isProcessingOrientationChange = false
    }
}

// **新增：判断旋转是否需要重置解码器**
private fun shouldResetDecoderForRotation(newWidth: Int, newHeight: Int): Boolean {
    // **如果解码器未配置，肯定需要配置**
    if (!isDecoderConfigured || decoder == null) {
        return true
    }
    
    // **检查分辨率变化是否过大**
    val widthChange = Math.abs(newWidth - cachedWidth)
    val heightChange = Math.abs(newHeight - cachedHeight)
    val maxChange = Math.max(widthChange, heightChange)
    
    // **如果分辨率变化不大，不需要重置**
    if (maxChange < 50) {
        Log.d(TAG, "分辨率变化较小($maxChange像素)，不需要重置解码器")
        return false
    }
    
    // **检查是否只是简单的宽高交换（典型的旋转）**
    val isSimpleRotation = (newWidth == cachedHeight && newHeight == cachedWidth)
    if (isSimpleRotation) {
        Log.d(TAG, "检测到简单旋转，尝试不重置解码器")
        return false // 先尝试不重置
    }
    
    Log.d(TAG, "分辨率变化较大或非简单旋转，需要重置解码器")
    return true
}
// 尝试配置解码器 - 简化版
private fun tryConfigureDecoder() {
    if (isConfiguringDecoder || sps == null) return
    
    // 防止频繁重配置
    val now = System.currentTimeMillis()
    if (now - lastConfigTime < 500) return
    
    if (spsProcessed && cachedWidth > 0 && cachedHeight > 0) {
        configureDecoderOnce()
        lastConfigTime = now
    }
}
// 重置解码器 - 简化版
private fun resetDecoder() {
    try {
        decoder?.stop()
        decoder?.release()
        decoder = null
        
        isDecoderConfigured = false
        isConfiguringDecoder = false
        
        Log.d(TAG, "解码器已重置")
        } catch (e: Exception) {
        Log.e(TAG, "重置解码器失败", e)
        decoder = null
        isDecoderConfigured = false
        isConfiguringDecoder = false
        }
    }



    // 从SPS提取分辨率 - 快速简化版
    private fun extractResolutionFromSPS(spsData: ByteArray): Pair<Int, Int> {
        try {
            // 快速确定起始点
            var offset = when {
                spsData.size >= 4 && spsData[0] == 0x00.toByte() && spsData[1] == 0x00.toByte() &&
                spsData[2] == 0x00.toByte() && spsData[3] == 0x01.toByte() -> 4
                spsData.size >= 3 && spsData[0] == 0x00.toByte() && spsData[1] == 0x00.toByte() &&
                spsData[2] == 0x01.toByte() -> 3
                else -> 0
            }
            
            // 直接使用高效的二进制解析，跳过媒体库调用
            if (spsData.size > offset + 10) {
                try {
                    // 提取NAL单元类型(确认是SPS)
                    val nalUnitType = spsData[offset].toInt() and 0x1F
                    if (nalUnitType == 7) {
                        // 创建位流读取器，直接指向SPS数据实际开始位置
                        val reader = BitReader(spsData, offset + 1)
                        
                        // 快速跳过前三个字段:profile_idc, constraint_flags, level_idc
                        reader.skipBits(24)
                        
                        // 读取seq_parameter_set_id(通常很小)
                        val seqParameterSetId = reader.readUE()
                        
                        // 确定profile_idc，决定是否需要读取额外字段
                        val profileIdc = spsData[offset + 1].toInt() and 0xFF
                        
                        // 只有特殊profile需要处理额外字段，其它可以跳过
                        if (profileIdc == 100 || profileIdc == 110 || profileIdc == 122 || profileIdc == 244 ||
                            profileIdc == 44 || profileIdc == 83 || profileIdc == 86 || profileIdc == 118 ||
                            profileIdc == 128) {
                            
                            val chromaFormatIdc = reader.readUE()
                            
                            if (chromaFormatIdc == 3) {
                                reader.skipBits(1)
                            }
                            
                            reader.readUE() // bit_depth_luma_minus8
                            reader.readUE() // bit_depth_chroma_minus8
                            reader.skipBits(1) // qpprime_y_zero_transform_bypass_flag
                            
                            val seqScalingMatrixPresentFlag = reader.readBits(1)
                            if (seqScalingMatrixPresentFlag == 1) {
                                val limit = if (chromaFormatIdc != 3) 8 else 12
                                for (i in 0 until limit) {
                                    val seqScalingListPresentFlag = reader.readBits(1)
                                    if (seqScalingListPresentFlag == 1) {
                                        val size = if (i < 6) 16 else 64
                                        var lastScale = 8
                                        var nextScale = 8
                                        for (j in 0 until size) {
                                            if (nextScale != 0) {
                                                val deltaScale = reader.readSE()
                                                nextScale = (lastScale + deltaScale + 256) % 256
                                            }
                                            lastScale = if (nextScale == 0) lastScale else nextScale
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 标准字段
                        reader.readUE() // log2_max_frame_num_minus4
                        val picOrderCntType = reader.readUE()
                        
                        if (picOrderCntType == 0) {
                            reader.readUE() // log2_max_pic_order_cnt_lsb_minus4
                        } else if (picOrderCntType == 1) {
                            reader.skipBits(1) // delta_pic_order_always_zero_flag
                            reader.readSE() // offset_for_non_ref_pic
                            reader.readSE() // offset_for_top_to_bottom_field
                            
                            val numRefFramesInPicOrderCntCycle = reader.readUE()
                            for (i in 0 until numRefFramesInPicOrderCntCycle) {
                                reader.readSE() // offset_for_ref_frame[i]
                            }
                        }
                        
                        reader.readUE() // max_num_ref_frames
                        reader.skipBits(1) // gaps_in_frame_num_value_allowed_flag
                        
                        // 读取图像尺寸信息
                        val picWidthInMbsMinus1 = reader.readUE()
                        val picHeightInMapUnitsMinus1 = reader.readUE()
                        
                        // 计算宽高
                        val width = (picWidthInMbsMinus1 + 1) * 16
                        var height = (picHeightInMapUnitsMinus1 + 1) * 16
                        
                        // 读取frame_mbs_only_flag
                        val frameMbsOnlyFlag = reader.readBits(1)
                        
                        // 非帧编码时高度需要×2
                        if (frameMbsOnlyFlag == 0) {
                            height *= 2
                            reader.skipBits(1) // mb_adaptive_frame_field_flag
                        }
                        
                        reader.skipBits(1) // direct_8x8_inference_flag
                        
                        // 处理裁剪参数(如有)
                        val frameCroppingFlag = reader.readBits(1)
                        var finalWidth = width
                        var finalHeight = height
                        
                        if (frameCroppingFlag == 1) {
                            val cropLeft = reader.readUE()
                            val cropRight = reader.readUE()
                            val cropTop = reader.readUE()
                            val cropBottom = reader.readUE()
                            
                            finalWidth = width - (cropLeft + cropRight) * 2
                            finalHeight = height - (cropTop + cropBottom) * 2
                        }
                        
                        return Pair(finalWidth, finalHeight)
                    }
                } catch (e: Exception) {
                    // 忽略解析异常，使用备用方法
                }
            }
            
            // 如果第一种方法失败，使用备用方法尝试获取
            try {
                val format = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, 0, 0)
                format.setByteBuffer("csd-0", ByteBuffer.wrap(spsData))
                
                val tempDecoder = MediaCodec.createDecoderByType(MediaFormat.MIMETYPE_VIDEO_AVC)
                tempDecoder.configure(format, null, null, 0)
                val outputFormat = tempDecoder.outputFormat
                tempDecoder.release()
                
                if (outputFormat.containsKey(MediaFormat.KEY_WIDTH) && 
                    outputFormat.containsKey(MediaFormat.KEY_HEIGHT)) {
                    return Pair(
                        outputFormat.getInteger(MediaFormat.KEY_WIDTH),
                        outputFormat.getInteger(MediaFormat.KEY_HEIGHT)
                    )
                }
            } catch (e: Exception) {
                // 忽略异常
            }
            
            return Pair(0, 0)
        } catch (e: Exception) {
            return Pair(0, 0)
        }
    }

    // 解码视频帧 - 优化版
    private fun decodeVideoFrame(frameData: ByteArray): Boolean {
        // **关键修复：检查解码器状态**
        val codec = decoder
        if (codec == null) {
            Log.w(TAG, "解码器为空，跳过帧解码")
            return false
        }

        try {
            // **添加状态检查，避免在释放状态下调用**
            synchronized(this) {
                if (decoder == null) {
                    Log.w(TAG, "解码器已被释放，跳过帧解码")
                    return false
                }
                
                // 输入缓冲区
                val inputBufferId = codec.dequeueInputBuffer(0)
                if (inputBufferId >= 0) {
                    val inputBuffer = codec.getInputBuffer(inputBufferId)
                    if (inputBuffer == null) {
                        Log.e(TAG, "无法获取输入缓冲区")
                        return false
                    }

                        inputBuffer.clear()
                        inputBuffer.put(frameData)
                        
                        // 判断是否是关键帧
                        val isKeyFrame = (frameData[4].toInt() and 0x1F) == 5

                    // 设置帧标志
                        val flags = if (isKeyFrame) MediaCodec.BUFFER_FLAG_KEY_FRAME else 0
                        
                    // 使用当前时间戳
                    codec.queueInputBuffer(inputBufferId, 0, frameData.size, System.nanoTime() / 1000, flags)
                }

                // 输出缓冲区
                val bufferInfo = MediaCodec.BufferInfo()
                var outputBufferId = codec.dequeueOutputBuffer(bufferInfo, 0)
                
                // 处理输出格式变更
                if (outputBufferId == MediaCodec.INFO_OUTPUT_FORMAT_CHANGED) {
                    val newFormat = codec.outputFormat
                    
                    if (newFormat.containsKey(MediaFormat.KEY_WIDTH) && 
                        newFormat.containsKey(MediaFormat.KEY_HEIGHT)) {
                        
                        val width = newFormat.getInteger(MediaFormat.KEY_WIDTH)
                        val height = newFormat.getInteger(MediaFormat.KEY_HEIGHT)
                        
                        Log.d(TAG, "解码器输出格式变更: ${width}x${height}，但保持当前分辨率: ${deviceWidth}x${deviceHeight}")
                    }
                }
                
                // 处理输出缓冲区
                while (outputBufferId >= 0) {
                    // 有可用的输出缓冲区，隐藏加载提示
                    runOnUiThread {
                        findViewById<TextView>(R.id.tv_connection_info).visibility = View.GONE
                        
                        // 画质切换完成检测
                        if (isChangingQuality) {
                            val isKeyFrame = (bufferInfo.flags and MediaCodec.BUFFER_FLAG_KEY_FRAME) != 0
                            val elapsedTime = System.currentTimeMillis() - qualityChangingStartTime
                            
                            if (isKeyFrame || elapsedTime > 1000) {
                                Log.d(TAG, "画质切换完成，已解码新画质帧${if (isKeyFrame) "（关键帧）" else ""}, 耗时: ${elapsedTime}ms")
                                
                                findViewById<View>(R.id.quality_change_overlay).visibility = View.GONE
                                findViewById<TextView>(R.id.tv_quality_changing).visibility = View.GONE
                                
                                isChangingQuality = false
                                qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
                            }
                        }
                    }
                    
                    // 释放并渲染
                    codec.releaseOutputBuffer(outputBufferId, true)
                    
                    // 获取下一个输出缓冲区
                    outputBufferId = codec.dequeueOutputBuffer(bufferInfo, 0)
                }
                
                return true
            }
        } catch (e: IllegalStateException) {
            Log.w(TAG, "解码器状态异常，可能正在释放: ${e.message}")
            return false
        } catch (e: Exception) {
            Log.e(TAG, "解码视频帧失败: ${e.message}")
            return false
        }
    }

// 修复配置解码器方法 - 解决Surface连接问题
private fun configureDecoderOnce() {
    if (isDecoderConfigured || isConfiguringDecoder) return
    
    isConfiguringDecoder = true
    
    try {
        val holder = surfaceView.holder
        val surface = holder.surface
        
        // **更严格的Surface检查**
        if (surface == null || !surface.isValid) {
            Log.e(TAG, "Surface无效，延迟重试")
            frameProcessor.postDelayed({
                isConfiguringDecoder = false
                tryConfigureDecoder()
            }, 100)
                    return
                }

        // **检查Surface尺寸是否合理**
        val surfaceWidth = surfaceView.width
        val surfaceHeight = surfaceView.height
        if (surfaceWidth <= 0 || surfaceHeight <= 0) {
            Log.e(TAG, "SurfaceView尺寸无效: ${surfaceWidth}x${surfaceHeight}")
            frameProcessor.postDelayed({
                isConfiguringDecoder = false
                tryConfigureDecoder()
            }, 100)
                    return
                }

        // 使用缓存的分辨率
        val width = if (cachedWidth > 0) cachedWidth else deviceWidth
        val height = if (cachedHeight > 0) cachedHeight else deviceHeight
        
        Log.d(TAG, "配置解码器: ${width}x${height}, Surface: ${surfaceWidth}x${surfaceHeight}")
        
        // **验证SPS/PPS数据完整性**
        if (sps == null || sps!!.size < 4) {
            Log.e(TAG, "SPS数据无效，大小: ${sps?.size ?: 0}")
            isConfiguringDecoder = false
            return
        }
        
        // **验证分辨率合理性**
        if (width <= 0 || height <= 0 || width > 4096 || height > 4096) {
            Log.e(TAG, "分辨率无效: ${width}x${height}")
            isConfiguringDecoder = false
            return
        }
        
        // **完全释放旧解码器，避免Surface连接冲突**
                releaseDecoderSafely()

        // **等待更长时间，确保资源完全释放**
        Thread.sleep(200)
        
        try {
            // 创建新解码器
            val format = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, width, height)
            
            // **安全设置SPS数据**
            try {
                format.setByteBuffer("csd-0", ByteBuffer.wrap(sps!!))
                Log.d(TAG, "SPS数据设置成功，大小: ${sps!!.size}")
            } catch (e: Exception) {
                Log.e(TAG, "设置SPS数据失败", e)
                throw e
            }
            
            // **安全设置PPS数据**
            if (pps != null && pps!!.size > 0) {
                try {
                format.setByteBuffer("csd-1", ByteBuffer.wrap(pps!!))
                    Log.d(TAG, "PPS数据设置成功，大小: ${pps!!.size}")
                } catch (e: Exception) {
                    Log.w(TAG, "设置PPS数据失败，继续尝试", e)
                }
            }
                
                format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface)
            format.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, width * height * 2) // 增加缓冲区大小
            
            // **添加更多配置参数**
            format.setInteger(MediaFormat.KEY_FRAME_RATE, 30)
            format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1)
            
            Log.d(TAG, "MediaFormat配置: $format")
            
                decoder = MediaCodec.createDecoderByType(MediaFormat.MIMETYPE_VIDEO_AVC)
            
            // **配置解码器时捕获具体错误**
            try {
                decoder?.configure(format, surface, null, 0)
                Log.d(TAG, "解码器configure成功")
            } catch (e: IllegalArgumentException) {
                Log.e(TAG, "解码器configure失败 - 参数错误", e)
                Log.e(TAG, "Format详情: width=$width, height=$height, spsSize=${sps?.size}, ppsSize=${pps?.size}")
                throw e
            } catch (e: IllegalStateException) {
                Log.e(TAG, "解码器configure失败 - 状态错误", e)
                throw e
            }
            
            decoder?.start()
            Log.d(TAG, "解码器start成功")
            
                isDecoderConfigured = true
            Log.d(TAG, "解码器配置完全成功")
                
            // **隐藏连接提示**
                runOnUiThread {
                findViewById<TextView>(R.id.tv_connection_info)?.visibility = View.GONE
                findViewById<ProgressBar>(R.id.progress_bar)?.visibility = View.GONE
            }
            
            // 清空帧队列，重新开始
            frameQueue.clear()
            
        } catch (e: Exception) {
            Log.e(TAG, "创建或配置解码器失败", e)
            releaseDecoderSafely()
            throw e
                }
                
            } catch (e: Exception) {
                Log.e(TAG, "配置解码器失败", e)
        releaseDecoderSafely()
                
        // **显示错误信息**
                runOnUiThread {
            findViewById<TextView>(R.id.tv_connection_info)?.apply {
                text = "视频解码器配置失败，正在重试..."
                visibility = View.VISIBLE
            }
        }
        
        // **延迟重试，但限制重试次数**
        frameProcessor.postDelayed({
            isConfiguringDecoder = false
            if (sps != null && !isDecoderConfigured) {
                tryConfigureDecoder()
            }
        }, 2000) // 增加到2秒
        
    } finally {
        if (isDecoderConfigured) {
            isConfiguringDecoder = false
        }
        // 如果配置失败，isConfiguringDecoder在重试逻辑中重置
    }
}

// 新增：安全释放解码器
    private fun releaseDecoderSafely() {
    try {
        if (decoder != null) {
                        Log.d(TAG, "解码器已停止")
            decoder?.stop()
                        Log.d(TAG, "解码器已释放")
            decoder?.release()
                    decoder = null
                }
                
        // **重置配置标志，允许重新配置**
                isDecoderConfigured = false
                
            } catch (e: Exception) {
                Log.e(TAG, "释放解码器时出错", e)
                decoder = null
        // **确保标志被重置**
                isDecoderConfigured = false
        }
    }

  // 修复adjustSurfaceViewSize - 正确计算可用区域
    private fun adjustSurfaceViewSize() {
        try {
            if (!gotRealResolution) {
                Log.d(TAG, "尚未获取到准确分辨率，跳过调整SurfaceView尺寸")
                return
            }
            
            if (deviceWidth <= 0 || deviceHeight <= 0) {
                Log.e(TAG, "无效的设备分辨率: ${deviceWidth}x${deviceHeight}")
                return
            }
            
            Log.d(TAG, "开始调整SurfaceView大小，使用分辨率: ${deviceWidth}x${deviceHeight}")
            
            // 获取屏幕尺寸
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val screenHeight = displayMetrics.heightPixels
            
        // **关键修复：根据当前模式正确计算可用区域**
        val videoLayout = findViewById<FrameLayout>(R.id.video_layout)
        var availableWidth: Int
        var availableHeight: Int
        
        if (currentMode == MODE_FULLSCREEN) {
            // 全屏模式：使用整个屏幕
            availableWidth = screenWidth
            availableHeight = screenHeight
            Log.d(TAG, "全屏模式：可用区域 = 屏幕尺寸: ${availableWidth}x${availableHeight}")
        } else {
            // **普通模式：需要减去状态栏、导航栏、工具栏的尺寸**
            val isLandscape = resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
            
            if (isLandscape) {
                // 横屏普通模式：减去状态栏高度 + 左侧导航栏宽度 + 右侧工具栏宽度
                val statusBar = findViewById<LinearLayout>(R.id.status_bar)
                val leftNav = findViewById<LinearLayout>(R.id.navigation_layout_left)
                val rightControl = findViewById<ScrollView>(R.id.control_scroll)
                
                val statusBarHeight = if (statusBar.visibility == View.VISIBLE) statusBar.height else 0
                val leftNavWidth = if (leftNav.visibility == View.VISIBLE) leftNav.width else 0
                val rightControlWidth = if (rightControl.visibility == View.VISIBLE) rightControl.width else 0
                
                // 如果控件还没测量完成，使用预估值
                val estimatedStatusBarHeight = if (statusBarHeight > 0) statusBarHeight else 120
                val estimatedLeftNavWidth = if (leftNavWidth > 0) leftNavWidth else 150
                val estimatedRightControlWidth = if (rightControlWidth > 0) rightControlWidth else 200
                
                availableWidth = screenWidth - estimatedLeftNavWidth - estimatedRightControlWidth
                availableHeight = screenHeight - estimatedStatusBarHeight
                
                Log.d(TAG, "横屏普通模式：屏幕${screenWidth}x${screenHeight} - 状态栏${estimatedStatusBarHeight} - 左导航${estimatedLeftNavWidth} - 右工具栏${estimatedRightControlWidth} = ${availableWidth}x${availableHeight}")
                
            } else {
                // 竖屏普通模式：减去状态栏高度 + 底部导航栏高度 + 右侧工具栏宽度
                val statusBar = findViewById<LinearLayout>(R.id.status_bar)
                val bottomNav = findViewById<LinearLayout>(R.id.navigation_layout)
                val rightControl = findViewById<ScrollView>(R.id.control_scroll)
                
                val statusBarHeight = if (statusBar.visibility == View.VISIBLE) statusBar.height else 0
                val bottomNavHeight = if (bottomNav.visibility == View.VISIBLE) bottomNav.height else 0
                val rightControlWidth = if (rightControl.visibility == View.VISIBLE) rightControl.width else 0
                
                // 如果控件还没测量完成，使用预估值
                val estimatedStatusBarHeight = if (statusBarHeight > 0) statusBarHeight else 120
                val estimatedBottomNavHeight = if (bottomNavHeight > 0) bottomNavHeight else 150
                val estimatedRightControlWidth = if (rightControlWidth > 0) rightControlWidth else 200
                
                availableWidth = screenWidth - estimatedRightControlWidth
                availableHeight = screenHeight - estimatedStatusBarHeight - estimatedBottomNavHeight
                
                Log.d(TAG, "竖屏普通模式：屏幕${screenWidth}x${screenHeight} - 状态栏${estimatedStatusBarHeight} - 底导航${estimatedBottomNavHeight} - 右工具栏${estimatedRightControlWidth} = ${availableWidth}x${availableHeight}")
            }
        }
        
        // 确保可用区域有效
        if (availableWidth <= 0 || availableHeight <= 0) {
            Log.e(TAG, "计算出的可用区域无效: ${availableWidth}x${availableHeight}")
                return
            }
            
        // 检查SurfaceView当前尺寸
            val currentWidth = surfaceView.width
            val currentHeight = surfaceView.height
            
        Log.d(TAG, "视频分辨率: ${deviceWidth}x${deviceHeight}, 当前SurfaceView: ${currentWidth}x${currentHeight}")
        
        // 计算视频的纵横比
            val videoRatio = deviceWidth.toFloat() / deviceHeight.toFloat()
            val screenRatio = availableWidth.toFloat() / availableHeight.toFloat()
            
        // 根据比例计算最终尺寸
            var finalWidth: Int
            var finalHeight: Int
            
            if (videoRatio > screenRatio) {
            // 视频更宽，以宽度为基准
                finalWidth = availableWidth
                finalHeight = (availableWidth / videoRatio).toInt()
            } else {
            // 视频更高，以高度为基准
                finalHeight = availableHeight
                finalWidth = (availableHeight * videoRatio).toInt()
                }
                
        // **普通模式下额外缩小一点，确保不会超出**
            if (currentMode == MODE_NORMAL) {
            finalWidth = (finalWidth * 0.95f).toInt() // 缩小5%
            finalHeight = (finalHeight * 0.95f).toInt()
            Log.d(TAG, "普通模式额外缩小5%确保不超出")
        }
        
        // 检查是否需要调整
            val diffWidth = Math.abs(currentWidth - finalWidth)
            val diffHeight = Math.abs(currentHeight - finalHeight)
            
            if (currentWidth > 0 && currentHeight > 0 && diffWidth < 10 && diffHeight < 10) {
            Log.d(TAG, "尺寸变化不大，不需要调整")
                return
            }
            
        Log.d(TAG, "调整SurfaceView尺寸: ${currentWidth}x${currentHeight} -> ${finalWidth}x${finalHeight}")
            
        // 应用新尺寸
            val layoutParams = surfaceView.layoutParams
            layoutParams.width = finalWidth
            layoutParams.height = finalHeight
            surfaceView.layoutParams = layoutParams
            
        // 强制刷新布局
        surfaceView.requestLayout()
            
        } catch (e: Exception) {
            Log.e(TAG, "调整SurfaceView尺寸失败", e)
        }
    }

    private fun reconnect() {
        if (isReconnecting.get() || isFinishing) {
            return
        }

        // 增加重连计数
        reconnectionAttempts++
        
        // 如果超过最大重连次数，则退出
        if (reconnectionAttempts > MAX_RECONNECTION_ATTEMPTS) {
            Log.d(TAG, "超过最大重连次数($MAX_RECONNECTION_ATTEMPTS)，退出页面")
            runOnUiThread {
                Toast.makeText(this, "连接失败，请重新启动应用", Toast.LENGTH_SHORT).show()
                finish()
            }
            return
        }
        
        Log.d(TAG, "尝试重新连接WebSocket (尝试 $reconnectionAttempts/$MAX_RECONNECTION_ATTEMPTS)")
        runOnUiThread {
            findViewById<TextView>(R.id.tv_connection_info).apply {
                text = "正在重新连接..."
                visibility = View.VISIBLE
            }
            Toast.makeText(this, "正在重新连接...", Toast.LENGTH_SHORT).show()
        }

        // 关闭旧连接
        wsClient?.close()
        wsClient = null
        isConnected.set(false)

        // 稍微延迟后重新连接
        Handler(Looper.getMainLooper()).postDelayed({
            if (!isFinishing) {
            connectWebSocket()
            }
        }, 0)
    }

  // 修改后的handleTouchEvent方法
    @SuppressLint("ClickableViewAccessibility")
    private fun handleTouchEvent(event: MotionEvent): Boolean {
        if (!isConnected.get()) return true

    val action = event.actionMasked
    val pointerIndex = event.actionIndex
    val pointerId = event.getPointerId(pointerIndex)

    when (action) {
        MotionEvent.ACTION_DOWN,
        MotionEvent.ACTION_POINTER_DOWN -> {
            // 处理新的触摸点
            val x = event.getX(pointerIndex)
            val y = event.getY(pointerIndex)
            activePointers.put(pointerId, Pair(x, y))
            processPointerEvent(pointerId, x, y, ACTION_DOWN, event.getPressure(pointerIndex))
        }

        MotionEvent.ACTION_MOVE -> {
            // 处理所有活动指针的移动
            for (i in 0 until event.pointerCount) {
                val id = event.getPointerId(i)
                val x = event.getX(i)
                val y = event.getY(i)
                activePointers.put(id, Pair(x, y))
                processPointerEvent(id, x, y, ACTION_MOVE, event.getPressure(i))
            }
        }

        MotionEvent.ACTION_UP,
        MotionEvent.ACTION_POINTER_UP -> {
            // 处理触摸点释放
            val (x, y) = activePointers.get(pointerId, Pair(0f, 0f))
            processPointerEvent(pointerId, x, y, ACTION_UP, 0f)
            activePointers.remove(pointerId)
        }

        MotionEvent.ACTION_CANCEL -> {
            // 处理所有触摸点取消
            for (i in 0 until activePointers.size()) {
                val id = activePointers.keyAt(i)
                val (x, y) = activePointers.valueAt(i)
                processPointerEvent(id, x, y, ACTION_UP, 0f)
            }
            activePointers.clear()
        }
    }

        return true
    }

private fun processPointerEvent(pointerId: Int, rawX: Float, rawY: Float, action: Int, pressure: Float) {
    try {
        // 转换为设备坐标 - 添加安全检查防止除以零
        if (surfaceView.width <= 0 || surfaceView.height <= 0) {
            Log.w(TAG, "无效的surfaceView尺寸: ${surfaceView.width}x${surfaceView.height}")
            return
        }
        
        // 使用缓存的准确分辨率，如果可用的话
        val targetWidth = if (realWidth > 0) realWidth else deviceWidth
        val targetHeight = if (realHeight > 0) realHeight else deviceHeight
        
        if (targetWidth <= 0 || targetHeight <= 0) {
            Log.w(TAG, "无效的目标设备分辨率: ${targetWidth}x${targetHeight}")
            return
        }
        
        // 确保使用裁剪后的实际分辨率计算
        val x = ((rawX * targetWidth) / surfaceView.width).toInt().coerceIn(0, targetWidth - 1)
        val y = ((rawY * targetHeight) / surfaceView.height).toInt().coerceIn(0, targetHeight - 1)
        
        // Log.d(TAG, "触摸坐标: 原始(${rawX.toInt()}, ${rawY.toInt()}) -> 设备(${x}, ${y}), 分辨率=${targetWidth}x${targetHeight}")
        
        sendTouchEvent(x, y, action, pointerId, pressure)
        
        // 更新用户最后活动时间
        lastUserActivityTime = System.currentTimeMillis()
    } catch (e: Exception) {
        Log.e(TAG, "处理触摸坐标转换失败", e)
    }
}

    private fun sendTouchEvent(x: Int, y: Int, action: Int, pointerId: Int, pressure: Float) {
        try {
            // 使用准确的缓存分辨率
            val targetWidth = if (realWidth > 0) realWidth else deviceWidth
            val targetHeight = if (realHeight > 0) realHeight else deviceHeight
            
            // 完全匹配Flutter中的格式
            val touchMsg = JSONObject().apply {
                put("msg_type", 2) // SC_CONTROL_MSG_TYPE_INJECT_TOUCH_EVENT
                put("action", action)
                put("x", x)
                put("y", y)
                put("touch_id", pointerId)  // 使用MotionEvent的pointerId作为唯一标识
                
                // 使用固定值1.0代替pressure值，以便在模拟器和真机上都能正常工作
                put("pressure", 1.0) 
                // put("buttons", 1)  // 默认按钮状态
            

                // 重要：使用当前最新的设备分辨率
                val resolutionArray = JSONArray()
                resolutionArray.put(targetWidth)
                resolutionArray.put(targetHeight)
                put("resolution", resolutionArray)
            }

            wsClient?.send(touchMsg.toString())
            // Log.d(TAG, "发送触摸事件: x=$x, y=$y, action=$action, 分辨率=${targetWidth}x${targetHeight}")

            // 记录用户活动
            lastUserActivityTime = System.currentTimeMillis()
        } catch (e: Exception) {
            Log.e(TAG, "发送触摸事件失败", e)
        }
    }



    override fun onDestroy() {
        Log.d(TAG, "Activity销毁")
        
        // 停止延迟测量
        stopLatencyMeasurement()
        
        // 如果WebSocket仍然连接，保存流量统计并断开
        if (isConnected.get()) {
            saveTrafficStatsAndDisconnect(true)
        } else {
            // 彻底关闭WebSocket连接
            disconnectWebSocket(true)
        }
        
        // 释放解码器资源
        releaseDecoderSafely()
        
        // 关闭线程池
        threadPool.shutdown()
        
        // 释放音频解码器资源
        if (isNoAdbDevice) {
            if (::noAdbAudioDecoder.isInitialized) {
                noAdbAudioDecoder.dispose()
            }
        } else {
            if (::audioDecoder.isInitialized) {
                audioDecoder.dispose()
            }
        }
        
        // 清理回调和Handler
        surfaceView.holder.removeCallback(this)
        connectionTimeoutHandler?.removeCallbacksAndMessages(null)
        qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
        
        // 取消未活动提示计时器
        inactivityCountdownTimer?.cancel()
        inactivityHandler?.removeCallbacksAndMessages(null)
        
        super.onDestroy()
    }


    // 添加音频解码器初始化方法
    private fun initAudioDecoder() {
        try {
            // 根据设备类型选择不同的音频解码器
            if (isNoAdbDevice) {
                // 免adb设备使用noAdbAudioDecoder
                Log.d(TAG, "初始化免adb设备音频解码器")
                noAdbAudioDecoder = noAdbAudioDecoder(this)
                noAdbAudioDecoder.initialize()
                isAudioSupported = true
                Log.d(TAG, "免adb设备音频解码器初始化成功")
            } else {
                // adb设备使用普通AudioDecoder
                Log.d(TAG, "初始化标准adb设备音频解码器")
                audioDecoder = AudioDecoder(this)
                audioDecoder.initialize()
                isAudioSupported = true
                Log.d(TAG, "标准adb设备音频解码器初始化成功")
            }
        } catch (e: Exception) {
            Log.e(TAG, "音频解码器初始化失败", e)
            isAudioSupported = false
        }
    }

    // 添加缺失的configureSurface方法
    private fun configureSurface() {
        try {
            val codec = mediaCodec
            if (codec != null && synchronizedSurface != null && synchronizedSurface!!.isValid) {
                // 重新配置解码器输出到新的Surface
                Log.d(TAG, "重新配置解码器输出到新的Surface")
                releaseDecoderSafely()
            }
        } catch (e: Exception) {
            Log.e(TAG, "配置Surface失败", e)
        }
    }

    // 直接从SPS二进制数据解析H.264分辨率
    private fun parseH264SPSResolution(spsData: ByteArray): Pair<Int, Int> {
        try {
            // 找到真正的SPS数据起始位置（跳过起始码）
            var offset = 0
            if (spsData.size > 4 && spsData[0] == 0x00.toByte() && spsData[1] == 0x00.toByte() &&
                spsData[2] == 0x00.toByte() && spsData[3] == 0x01.toByte()) {
                offset = 4
            } else if (spsData.size > 3 && spsData[0] == 0x00.toByte() && spsData[1] == 0x00.toByte() &&
                       spsData[2] == 0x01.toByte()) {
                offset = 3
            }
            
            // 打印SPS数据关键字节用于分析
            val keyBytes = StringBuilder()
            for (i in offset until Math.min(offset + 16, spsData.size)) {
                keyBytes.append(String.format("%02X ", spsData[i].toInt() and 0xFF))
            }
            Log.d(TAG, "SPS关键字节 (偏移=$offset): $keyBytes")
            
            // 提取关键参数
            if (spsData.size > offset + 3) {
                // 提取NAL单元类型 (应该是7表示SPS)
                val nalUnitType = spsData[offset].toInt() and 0x1F
                
                // 检查是否确实是SPS
                if (nalUnitType == 7) {
                    // 提取profile_idc, level_idc等
                    val profileIdc = spsData[offset+1].toInt() and 0xFF
                    val constraints = spsData[offset+2].toInt() and 0xFF
                    val levelIdc = spsData[offset+3].toInt() and 0xFF
                    
                    Log.d(TAG, "H.264 SPS信息: profile_idc=$profileIdc, constraints=$constraints, level_idc=$levelIdc")
                    
                    // 现在开始真正解析SPS的分辨率信息
                    // 创建位流读取器，指向SPS数据的有效部分（跳过NAL头）
                    val reader = BitReader(spsData, offset + 1)
                    
                    // 跳过profile_idc (8位)
                    reader.skipBits(8)
                    
                    // 跳过constraint_set flags和reserved_zero bits (8位)
                    reader.skipBits(8)
                    
                    // 跳过level_idc (8位)
                    reader.skipBits(8)
                    
                    // 读取seq_parameter_set_id
                    val seqParameterSetId = reader.readUE()
                    Log.d(TAG, "SPS解析: seq_parameter_set_id=$seqParameterSetId")
                    
                    // 根据不同的profile处理
                    if (profileIdc == 100 || profileIdc == 110 || profileIdc == 122 || profileIdc == 244 ||
                        profileIdc == 44 || profileIdc == 83 || profileIdc == 86 || profileIdc == 118 ||
                        profileIdc == 128 || profileIdc == 138 || profileIdc == 139 || profileIdc == 134) {
                        
                        // 读取chroma_format_idc
                        val chromaFormatIdc = reader.readUE()
                        Log.d(TAG, "SPS解析: chroma_format_idc=$chromaFormatIdc")
                        
                        if (chromaFormatIdc == 3) {
                            // 读取separate_colour_plane_flag
                            reader.skipBits(1)
                        }
                        
                        // 读取bit_depth_luma_minus8
                        reader.readUE()
                        
                        // 读取bit_depth_chroma_minus8
                        reader.readUE()
                        
                        // 读取qpprime_y_zero_transform_bypass_flag
                        reader.skipBits(1)
                        
                        // 读取seq_scaling_matrix_present_flag
                        val seqScalingMatrixPresentFlag = reader.readBits(1)
                        
                        if (seqScalingMatrixPresentFlag == 1) {
                            // scaling_list处理
                            val scalingListCount = if (chromaFormatIdc != 3) 8 else 12
                            for (i in 0 until scalingListCount) {
                                val seqScalingListPresentFlag = reader.readBits(1)
                                if (seqScalingListPresentFlag == 1) {
                                    // 跳过scaling_list数据
                                    val size = if (i < 6) 16 else 64
                                    var lastScale = 8
                                    var nextScale = 8
                                    for (j in 0 until size) {
                                        if (nextScale != 0) {
                                            val deltaScale = reader.readSE()
                                            nextScale = (lastScale + deltaScale + 256) % 256
                                        }
                                        lastScale = if (nextScale == 0) lastScale else nextScale
                                    }
                                }
                            }
                        }
                    }
                    
                    // 读取log2_max_frame_num_minus4
                    reader.readUE()
                    
                    // 读取pic_order_cnt_type
                    val picOrderCntType = reader.readUE()
                    
                    if (picOrderCntType == 0) {
                        // 读取log2_max_pic_order_cnt_lsb_minus4
                        reader.readUE()
                    } else if (picOrderCntType == 1) {
                        // 跳过delta_pic_order处理
                        reader.skipBits(1) // delta_pic_order_always_zero_flag
                        reader.readSE() // offset_for_non_ref_pic
                        reader.readSE() // offset_for_top_to_bottom_field
                        
                        val numRefFramesInPicOrderCntCycle = reader.readUE()
                        for (i in 0 until numRefFramesInPicOrderCntCycle) {
                            reader.readSE() // offset_for_ref_frame[i]
                        }
                    }
                    
                    // 读取max_num_ref_frames
                    reader.readUE()
                    
                    // 读取gaps_in_frame_num_value_allowed_flag
                    reader.skipBits(1)
                    
                    // 读取图像尺寸信息
                    val picWidthInMbsMinus1 = reader.readUE()
                    val picHeightInMapUnitsMinus1 = reader.readUE()
                    
                    // 计算宽高（以宏块为单位，一个宏块是16x16像素）
                    val width = (picWidthInMbsMinus1 + 1) * 16
                    var height = (picHeightInMapUnitsMinus1 + 1) * 16
                    
                    // 读取frame_mbs_only_flag
                    val frameMbsOnlyFlag = reader.readBits(1)
                    
                    // 如果frame_mbs_only_flag为0，则高度需要乘以2
                    if (frameMbsOnlyFlag == 0) {
                        height *= 2
                        // 读取mb_adaptive_frame_field_flag（但不影响计算）
                        reader.skipBits(1)
                    }
                    
                    // 读取direct_8x8_inference_flag
                    reader.skipBits(1)
                    
                    // 读取frame_cropping_flag
                    val frameCroppingFlag = reader.readBits(1)
                    
                    // 如果有裁剪参数，需要处理
                    var cropLeft = 0
                    var cropRight = 0
                    var cropTop = 0
                    var cropBottom = 0
                    
                    if (frameCroppingFlag == 1) {
                        cropLeft = reader.readUE()
                        cropRight = reader.readUE()
                        cropTop = reader.readUE()
                        cropBottom = reader.readUE()
                    }
                    
                    // 记录原始数据
                    Log.d(TAG, "SPS原始分辨率: ${width}x${height}, 裁剪参数: left=$cropLeft, right=$cropRight, top=$cropTop, bottom=$cropBottom")
                    
                    // 修改：正确应用H.264的裁剪计算
                    // H.264规范中，裁剪参数的单位是像素的一半或更小，需要乘以适当的系数
                    var finalWidth = width
                    var finalHeight = height
                    
                    // 只有在有裁剪参数时才应用裁剪计算
                    if (frameCroppingFlag == 1) {
                        // 根据H.264规范计算实际裁剪像素数
                        // 色度格式为4:2:0时，宽度的裁剪单位是2，高度的裁剪单位是2
                        val widthCropUnits = 2
                        val heightCropUnits = 2
                        
                        finalWidth = width - (cropLeft + cropRight) * widthCropUnits
                        finalHeight = height - (cropTop + cropBottom) * heightCropUnits
                    }
                    
                    Log.d(TAG, "SPS解析最终使用的分辨率: ${finalWidth}x${finalHeight}")
                    
                    // 返回应用裁剪后的实际分辨率
                    return Pair(finalWidth, finalHeight)
                } else {
                    Log.w(TAG, "不是SPS NAL单元类型: $nalUnitType")
                }
            }
            
            // 如果解析失败，返回0,0
            return Pair(0, 0)
            
        } catch (e: Exception) {
            Log.e(TAG, "直接解析SPS二进制数据失败: ${e.message}")
            return Pair(0, 0)
        }
    }

    // 处理屏幕方向变化
    private fun handleOrientationChange(newOrientation: Int) {
        // 防止重复处理
        if (isProcessingOrientationChange) return
        isProcessingOrientationChange = true
        
        Log.d(TAG, "屏幕方向变化: ${if (newOrientation == ORIENTATION_PORTRAIT) "竖屏" else "横屏"}")
        Log.d(TAG, "当前视频分辨率: ${deviceWidth}x${deviceHeight}")
        
        // 更新当前方向
        currentOrientation = newOrientation
        
        runOnUiThread {
            // **关键修改：不强制旋转屏幕，让系统自然处理**
            // 移除强制旋转逻辑，避免冲突
            // 根据当前模式调整UI布局
            if (currentMode == MODE_NORMAL) {
                switchNavigationLayout(newOrientation)
            } else if (currentMode == MODE_FULLSCREEN) {
                adjustFloatingBallPosition(newOrientation)
            }
        }
        
        // 延迟重置标志
        Handler(Looper.getMainLooper()).postDelayed({
            isProcessingOrientationChange = false
        }, 300) // 减少延迟时间
    }

// 优化配置变更处理，避免重复旋转
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        
        Log.d(TAG, "配置变更: orientation=${newConfig.orientation}")
        
        val newOrientation = if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            ORIENTATION_LANDSCAPE
        } else {
            ORIENTATION_PORTRAIT
        }
        
    // 只有真正变化时才处理，避免首次旋转时的重复处理
        if (newOrientation != currentOrientation && !isProcessingOrientationChange) {
            Log.d(TAG, "配置变更检测到真实的屏幕方向变化")
            
            // 更新全屏状态
            updateFullscreenByOrientation(newConfig.orientation)
            
        // 处理方向变化（UI布局调整）
            handleOrientationChange(newOrientation)
        
        // **配置变更完成后，重新调整SurfaceView尺寸**
        Handler(Looper.getMainLooper()).postDelayed({
            adjustSurfaceViewSize()
        }, 200) // 等待布局稳定后调整
        } else {
            Log.d(TAG, "配置变更：方向未变化或正在处理中，跳过")
        }
    }
    
    // 根据屏幕方向切换导航栏
    private fun switchNavigationLayout(orientation: Int) {
                runOnUiThread {
            val bottomNavLayout = findViewById<LinearLayout>(R.id.navigation_layout)
            val leftNavLayout = findViewById<LinearLayout>(R.id.navigation_layout_left)
            
            if (orientation == ORIENTATION_LANDSCAPE) {
                // 横屏模式：显示左侧导航栏，隐藏底部导航栏
                leftNavLayout.visibility = View.VISIBLE
                bottomNavLayout.visibility = View.GONE
                
                // 确保左侧导航栏的约束正确
                val params = leftNavLayout.layoutParams as? ViewGroup.MarginLayoutParams
                if (params != null) {
                    params.topMargin = 0
                    leftNavLayout.layoutParams = params
                }
                
                Log.d(TAG, "切换为横屏模式导航栏")
                    } else {
                // 竖屏模式：显示底部导航栏，隐藏左侧导航栏
                leftNavLayout.visibility = View.GONE
                bottomNavLayout.visibility = View.VISIBLE
                
                // 确保底部导航栏的约束正确
                val videoLayout = findViewById<FrameLayout>(R.id.video_layout)
                val videoParams = videoLayout.layoutParams as? androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
                if (videoParams != null) {
                    videoParams.bottomToTop = R.id.navigation_layout
                    videoLayout.layoutParams = videoParams
                }
                
                Log.d(TAG, "切换为竖屏模式导航栏")
            }
            
            // 重新调整视频尺寸
            // adjustSurfaceViewSize()
        }
    }

    // 添加清晰度选择对话框
    private fun showResolutionSelector() {
        // 检查是否有比特率配置
        if (bitrateConfigs.isEmpty()) {
            Toast.makeText(this, "无可用画质配置", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 从比特率配置中提取名称
        val options = bitrateConfigs.map { it.name }.toTypedArray()
        
        AlertDialog.Builder(this)
            .setTitle("选择清晰度")
            .setItems(options) { dialog: DialogInterface, which: Int ->
                // 获取选择的配置
                val selectedConfig = bitrateConfigs[which]
                currentBitrateConfig = selectedConfig
                
                // 应用新配置
                changeResolution(selectedConfig)
            }
            .show()
    }
    
    // 优化画质切换超时设置
    private fun changeResolution(config: BitrateConfig) {
        try {
            // 如果正在切换画质，不允许再次点击
            if (isChangingQuality) {
                return
            }
            
            // 检查WebSocket连接状态
            if (!isConnected.get() || wsClient == null) {
                Toast.makeText(this, "未连接到设备，无法切换画质", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 标记正在切换画质，并记录开始时间
            isChangingQuality = true
            qualityChangingStartTime = System.currentTimeMillis()
            
            // 显示蒙版和提示信息
            runOnUiThread {
                findViewById<View>(R.id.quality_change_overlay).visibility = View.VISIBLE
                findViewById<TextView>(R.id.tv_quality_changing).visibility = View.VISIBLE
            }
            
        // 更新UI显示
            runOnUiThread {
                findViewById<TextView>(R.id.status_tv_quality).text = config.name
                findViewById<TextView>(R.id.overlay_tv_quality)?.text = config.name
            }
            
            // 保存当前选择的配置
            currentBitrateConfig = config
            
            // 显示进度提示
            Toast.makeText(this, "正在切换到${config.name}", Toast.LENGTH_SHORT).show()
            
        // **缩短超时时间，避免长时间显示蒙版**
            qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
            qualityChangeTimeoutHandler = Handler(Looper.getMainLooper())
            qualityChangeTimeoutHandler?.postDelayed({
                if (isChangingQuality) {
                Log.w(TAG, "画质切换超时，强制关闭蒙版")
                    runOnUiThread {
                        findViewById<View>(R.id.quality_change_overlay).visibility = View.GONE
                        findViewById<TextView>(R.id.tv_quality_changing).visibility = View.GONE
                        isChangingQuality = false
                    }
                }
        }, 2000L) // 缩短到2秒
            
        // 发送画质切换消息
        sendQualityChangeMessage(config)
            
        } catch (e: Exception) {
            Log.e(TAG, "更改画质失败", e)
            Toast.makeText(this, "更改画质失败: ${e.message}", Toast.LENGTH_SHORT).show()
            
            // 出错时重置状态并隐藏蒙版
            isChangingQuality = false
            qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
            runOnUiThread {
                findViewById<View>(R.id.quality_change_overlay).visibility = View.GONE
                findViewById<TextView>(R.id.tv_quality_changing).visibility = View.GONE
            }
        }
    }
    // 新增：发送画质切换消息
private fun sendQualityChangeMessage(config: BitrateConfig) {
    try {
        val qualityMessage = JSONObject().apply {
            put("msg_type", 999)
            put("fps", config.fps)
            put("bits", config.videoBitRate)
            put("size", config.maxSize)
        }
        
        Log.d(TAG, "发送画质切换消息: ${qualityMessage}")
        wsClient?.send(qualityMessage.toString())
        
        Log.d(TAG, "画质切换消息已发送: ${config.name}, FPS=${config.fps}, 码率=${config.videoBitRate}kbps, 最大边=${config.maxSize}")
        
    } catch (e: Exception) {
        Log.e(TAG, "发送画质切换消息失败", e)
        
        // 发送失败时重置状态
        isChangingQuality = false
        qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
        runOnUiThread {
            findViewById<View>(R.id.quality_change_overlay).visibility = View.GONE
            findViewById<TextView>(R.id.tv_quality_changing).visibility = View.GONE
            Toast.makeText(this, "画质切换失败，请重试", Toast.LENGTH_SHORT).show()
        }
    }
}
    // 添加使用新配置重新连接的方法
    private fun reconnectWithNewConfig(config: BitrateConfig) {
        try {
            // 先断开当前连接
            wsClient?.close()
            wsClient = null
            audioWsClient?.close()
            audioWsClient = null
            
            isConnected.set(false)
            
            // 重新构建WebSocket URL，添加新的画质参数
            val newWsUrl = buildWsUrlWithConfig(config)
            
            // 更新当前URL
            wsUrl = newWsUrl
            
            // 延迟一点点时间再重连
            Handler(Looper.getMainLooper()).postDelayed({
                if (!isFinishing) {
                    connectWebSocket()
                }
            }, 300)
            
        } catch (e: Exception) {
            Log.e(TAG, "使用新配置重新连接失败", e)
            isChangingQuality = false
            
            runOnUiThread {
                findViewById<View>(R.id.quality_change_overlay).visibility = View.GONE
                findViewById<TextView>(R.id.tv_quality_changing).visibility = View.GONE
                Toast.makeText(this, "切换画质失败，请重试", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    // 构建带配置参数的WebSocket URL
    private fun buildWsUrlWithConfig(config: BitrateConfig): String {
        try {
            // 解析原始URL，提取基本部分
            val baseUrl: String
            val queryPart: String
            
            val questionMarkIndex = wsUrl.indexOf('?')
            if (questionMarkIndex > 0) {
                baseUrl = wsUrl.substring(0, questionMarkIndex)
                queryPart = wsUrl.substring(questionMarkIndex + 1)
            } else {
                baseUrl = wsUrl
                queryPart = ""
            }
            
            // 创建配置参数对象
            val configObj = JSONObject().apply {
                put("audio", isAudioSupported)
                put("video_bit_rate", config.videoBitRate)
                put("audio_bit_rate", config.audioBitRate)
                put("max_fps", config.fps)
                put("height", config.maxSize)
            }
            
            // 将配置对象转换为JSON字符串，然后进行URL编码
            val configParam = URLEncoder.encode(configObj.toString(), "UTF-8")
            
            // 构建新的URL
            val newUrl = if (queryPart.isEmpty()) {
                "$baseUrl?config=$configParam"
            } else {
                // 如果原始URL中已有config参数，替换它
                if (queryPart.contains("config=")) {
                    val params = queryPart.split("&").toMutableList()
                    val newParams = mutableListOf<String>()
                    
                    for (param in params) {
                        if (!param.startsWith("config=")) {
                            newParams.add(param)
                        }
                    }
                    
                    newParams.add("config=$configParam")
                    "$baseUrl?${newParams.joinToString("&")}"
                } else {
                    // 如果没有config参数，直接添加
                    "$baseUrl?$queryPart&config=$configParam"
                }
            }
            
            Log.d(TAG, "新的WebSocket URL: $newUrl")
            return newUrl
            
        } catch (e: Exception) {
            Log.e(TAG, "构建WebSocket URL失败", e)
            return wsUrl // 出错时返回原始URL
        }
    }
    
    // 更新延迟显示的方法
    private fun updateLatency(latencyMs: Int) {
        // 已移除对tv_latency的引用
    }

    // 获取远程设备剪贴板内容
    private fun getRemoteClipboardContent() {
        try {
            if (!isConnected.get()) {
                Toast.makeText(this, "未连接到设备", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 构建消息
            val message = JSONObject().apply {
                put("msg_type", SC_CONTROL_MSG_TYPE_GET_CLIPBOARD)
            }       
            
            // 发送消息
            wsClient?.send(message.toString())
            Log.d(TAG, "已发送获取剪贴板内容请求")
            
            Toast.makeText(this, "正在获取远程剪贴板...", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e(TAG, "发送获取剪贴板请求失败", e)
            Toast.makeText(this, "获取剪贴板失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 设置远程设备剪贴板内容（从本地剪贴板获取并发送）
    private fun setRemoteClipboardContent() {
        try {
            if (!isConnected.get()) {
                Toast.makeText(this, "未连接到设备", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 从系统剪贴板获取内容
            if (!clipboardManager.hasPrimaryClip()) {
                Toast.makeText(this, "本地剪贴板为空", Toast.LENGTH_SHORT).show()
                return
            }
            
            val clipData = clipboardManager.primaryClip
            if (clipData == null || clipData.itemCount == 0) {
                Toast.makeText(this, "无法获取剪贴板内容", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 获取第一个剪贴项
            val item = clipData.getItemAt(0)
            val text = item.text?.toString() ?: ""
            
            if (text.isEmpty()) {
                Toast.makeText(this, "剪贴板内容为空", Toast.LENGTH_SHORT).show()
                return
            }
            
            // 构建发送消息
            val message = JSONObject().apply {
                put("msg_type", SC_CONTROL_MSG_TYPE_SET_CLIPBOARD)
                put("text", text)
                put("paste", true) // 不自动粘贴
            }
            
            // 发送消息
            wsClient?.send(message.toString())
            Log.d(TAG, "已发送设置剪贴板内容请求: ${text.take(50)}${if (text.length > 50) "..." else ""}")
            
            Toast.makeText(this, "正在发送到远程剪贴板...", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e(TAG, "发送设置剪贴板请求失败", e)
            Toast.makeText(this, "设置剪贴板失败", Toast.LENGTH_SHORT).show()
        }
    }
    
    // 处理获取剪贴板响应
    private fun handleGetClipboardResponse(data: ByteArray) {
        try {
            // 提取剪贴板数据 (跳过前5个字节的头部)
            if (data.size <= 5) {
                Log.w(TAG, "剪贴板响应数据为空")
                runOnUiThread {
                    Toast.makeText(this, "远程剪贴板为空", Toast.LENGTH_SHORT).show()
                }
                return
            }
            
            val clipboardData = ByteArray(data.size - 5)
            System.arraycopy(data, 5, clipboardData, 0, clipboardData.size)
            
            // 转换为文本
            val clipboardText = String(clipboardData, StandardCharsets.UTF_8)
            
            Log.d(TAG, "收到远程剪贴板内容: ${clipboardText.take(50)}${if (clipboardText.length > 50) "..." else ""}")
            
            // 设置到本地剪贴板
            runOnUiThread {
                val clip = ClipData.newPlainText("远程设备剪贴板", clipboardText)
                clipboardManager.setPrimaryClip(clip)
                Toast.makeText(this, "已复制到本地剪贴板", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理获取剪贴板响应失败", e)
            runOnUiThread {
                Toast.makeText(this, "处理剪贴板内容失败", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    // 处理设置剪贴板响应
    private fun handleSetClipboardResponse(data: ByteArray) {
        try {
            Log.d(TAG, "收到设置剪贴板响应")
            
            runOnUiThread {
                Toast.makeText(this, "远程剪贴板已更新", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理设置剪贴板响应失败", e)
        }
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "Activity暂停")
        
        // 标记应用已进入后台
        wasInBackground = true
        
        // 不在onPause中断开连接，因为可能只是暂时性的暂停（如对话框弹出）
    }
    
    override fun onStop() {
        super.onStop()
        Log.d(TAG, "Activity停止")
        
        // 在onStop中断开WebSocket连接，因为应用确实进入后台
        if (!isFinishing) {
            // 断开WebSocket连接，但不结束Activity
            disconnectWebSocket(false)
        }
        
        // 标记应用已进入后台，确保onResume时重新连接
        wasInBackground = true
    }
    
    private fun checkWebSocketConnection(): Boolean {
        // 检查WebSocket是否为null或已关闭
        if (wsClient == null || wsClient?.isClosed == true || wsClient?.isClosing == true) {
            Log.d(TAG, "WebSocket为null或已关闭")
            return false
        }
        
        // 尝试获取WebSocket是否打开
        val isOpen = wsClient?.isOpen ?: false
        Log.d(TAG, "WebSocket连接状态: ${if (isOpen) "已连接" else "已断开"}")
        
        // 检查连接状态是否与WebSocket状态一致
        if (isConnected.get() != isOpen) {
            Log.d(TAG, "连接状态与WebSocket状态不一致: isConnected=${isConnected.get()}, isOpen=$isOpen")
            return false
        }
        
        return isOpen
    }
    
    override fun onResume() {
        super.onResume()
        
        
        // 检查应用是否从后台切回前台
        if (wasInBackground) {
            Log.d(TAG, "应用从后台切回前台，启动重连")
            
            // 从后台恢复时总是重连（不检查之前的连接状态）
            reconnectionAttempts = 0 // 重置重连计数
            
            // 在Surface创建后会自动连接，此处不需要额外操作
            
            // 重置标志
            wasInBackground = false
        } else {
            // 检查WebSocket状态，防止某些情况下断开未重连
            if (!checkWebSocketConnection() && !isReconnecting.get()) {
                Log.d(TAG, "检测到WebSocket连接异常，尝试重连")
                reconnect()
            }
        }
    }

    // 添加断开WebSocket连接的方法
    private fun disconnectWebSocket(endActivity: Boolean) {
        try {
            Log.d(TAG, "正在断开WebSocket连接")
            
            // 隐藏关闭连接的蒙版
            runOnUiThread {
                findViewById<View>(R.id.closing_connection_overlay).visibility = View.GONE
            }
            
            // 清理连接超时处理器
            connectionTimeoutHandler?.removeCallbacksAndMessages(null)
            connectionTimeoutHandler = null
            
            // 关闭主WebSocket
            if (wsClient != null) {
                wsClient?.close()
                wsClient = null
            }
            
            // 关闭音频WebSocket
            if (audioWsClient != null) {
                audioWsClient?.close()
                audioWsClient = null
            }
            
            // 重置连接状态
            isConnected.set(false)
            isReconnecting.set(false)
            
            // 重置SPS/PPS数据，确保重连时重新配置
            sps = null
            pps = null
            
            if (endActivity && !isFinishing) {
                runOnUiThread {
                    Toast.makeText(this, "连接已断开", Toast.LENGTH_SHORT).show()
                    finish()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "断开WebSocket连接失败: ${e.message}")
        }
    }

    // 实现SurfaceHolder.Callback接口的方法
    override fun surfaceCreated(holder: SurfaceHolder) {
        // 已通过匿名内部类处理
    }

 // 修复Surface回调 - 确保在Surface准备好后配置解码器
    override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
        Log.d(TAG, "Surface changed: width=$width, height=$height")
        if (holder.surface != null && holder.surface.isValid) {
            synchronizedSurface = holder.surface
            
        // **Surface准备好后，如果有SPS数据就尝试配置解码器**
        if (sps != null && !isDecoderConfigured && !isConfiguringDecoder) {
            Log.d(TAG, "Surface准备完毕，尝试配置解码器")
            frameProcessor.postDelayed({
                tryConfigureDecoder()
            }, 50)
        }
        
        // 只有在获取到真实分辨率后才调整SurfaceView
            if (gotRealResolution && realWidth > 0 && realHeight > 0) {
            Log.d(TAG, "调整Surface尺寸到: $realWidth x $realHeight")
            deviceWidth = realWidth
            deviceHeight = realHeight
            adjustSurfaceViewSize()
            } else {
            Log.d(TAG, "等待获取真实分辨率")
            }
        }
    }

    override fun surfaceDestroyed(holder: SurfaceHolder) {
        // 已通过匿名内部类处理
    }

    // 显示长时间未操作提示
    private fun showInactivityAlert() {
        try {
            // 如果已经显示了提示，不再重复显示
            val inactivityOverlay = findViewById<View>(R.id.inactivity_overlay)
            if (inactivityOverlay.visibility == View.VISIBLE) {
                return
            }
            
            // 显示蒙版
            inactivityOverlay.visibility = View.VISIBLE
            val messageTextView = findViewById<TextView>(R.id.tv_inactivity_message)
            val continueButton = findViewById<Button>(R.id.btn_continue)
            val exitButton = findViewById<Button>(R.id.btn_exit)
            val countdownTextView = findViewById<TextView>(R.id.tv_countdown)
            
            // 设置初始倒计时文本
            countdownTextView.text = "(${INACTIVITY_COUNTDOWN_SECONDS}s)"
            
            // 设置继续按钮点击事件
            continueButton.setOnClickListener {
                // 取消倒计时
                inactivityCountdownTimer?.cancel()
                
                // 隐藏蒙版
                inactivityOverlay.visibility = View.GONE
                
                // 发送一个空操作事件，表示用户仍在活动
                sendUserActiveSignal()
            }
            
            // 设置退出按钮点击事件
            exitButton.setOnClickListener {
                // 取消倒计时
                inactivityCountdownTimer?.cancel()
                
                // 断开连接并退出
                disconnectAndExit()
            }
            
            // 设置倒计时
            inactivityCountdownTimer = object : CountDownTimer(INACTIVITY_COUNTDOWN_SECONDS * 1000L, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    val secondsLeft = millisUntilFinished / 1000 + 1
                    countdownTextView.text = "(${secondsLeft}s)"
                }
                
                override fun onFinish() {
                    // 倒计时结束，断开连接并退出
                    disconnectAndExit()
                }
            }.start()
            
        } catch (e: Exception) {
            Log.e(TAG, "显示未活动提示失败", e)
        }
    }
    
    // 发送用户活动信号
    private fun sendUserActiveSignal() {
        try {
            // 发送表示用户仍然活跃的消息
            val activeMessage = JSONObject().apply {
                put("msg_type", 401) // 用户仍然活跃的消息类型
            }
            wsClient?.send(activeMessage.toString())
            
            // 更新最后用户活动时间
            lastUserActivityTime = System.currentTimeMillis()
            
            Log.d(TAG, "发送用户活动信号")
        } catch (e: Exception) {
            Log.e(TAG, "发送用户活动信号失败", e)
        }
    }
    
    // 断开连接并退出
    private fun disconnectAndExit() {
        Log.d(TAG, "因未活动断开连接并退出")
        saveTrafficStatsAndDisconnect(true)
    }
    
    // 保存流量统计并断开连接
    private fun saveTrafficStatsAndDisconnect(endActivity: Boolean, showLoadingOverlay: Boolean = true) {
        try {
            // 根据参数决定是否显示关闭连接的蒙版
            if (showLoadingOverlay) {
            runOnUiThread {
                findViewById<View>(R.id.closing_connection_overlay).visibility = View.VISIBLE
                }
            }
            
            // 如果连接时间太短或参数无效，直接断开
            if (connectionStartTime == 0L || userId == 0 || deviceId == 0 || deviceOnlyCode.isEmpty()) {
                Log.w(TAG, "统计参数无效，直接断开连接")
                disconnectWebSocket(endActivity)
                return
            }
            
            // 计算连接时长（秒）
            val currentTime = System.currentTimeMillis()
            val totalTimeSeconds = (currentTime - connectionStartTime) / 1000
            
            // 计算总流量（KB）
            val totalTrafficKB = totalReceivedBytes / 1024
            
            // 计算平均流量（KB/s）
            val averageTrafficKBps = if (totalTimeSeconds > 0) {
                (totalTrafficKB.toDouble() / totalTimeSeconds.toDouble())
            } else {
                0.0
            }
            
            Log.d(TAG, "保存流量统计: 总时长=${totalTimeSeconds}秒, 总流量=${totalTrafficKB}KB, 平均=${averageTrafficKBps}KB/s")
            Log.d(TAG, "参数详情: userId=${userId}, deviceId=${deviceId}, deviceOnlyCode=${deviceOnlyCode}")
            
            // 创建请求对象
            val request = TrafficStatsRequest(
                userId = userId,
                deviceId = deviceId,
                deviceOnlyCode = deviceOnlyCode,
                totalTraffic = totalTrafficKB,
                averageTraffic = averageTrafficKBps,
                totalTime = totalTimeSeconds
            )
            
            // 打印完整请求体内容
            Log.d(TAG, "完整请求体: userId=${request.userId}, deviceId=${request.deviceId}, " +
                   "deviceOnlyCode=${request.deviceOnlyCode}, totalTraffic=${request.totalTraffic}, " +
                   "averageTraffic=${request.averageTraffic}, totalTime=${request.totalTime}")
            
            // 创建Retrofit API服务
            val apiService = RetrofitClient.getInstance().create(ApiService::class.java)
            Log.d(TAG, "RetrofitClient baseUrl: ${RetrofitClient.getInstance().baseUrl()}")
            
            // 发送请求
            apiService.saveTrafficStats(request).enqueue(object : retrofit2.Callback<BaseResponse<Any>> {
                override fun onResponse(call: retrofit2.Call<BaseResponse<Any>>, response: retrofit2.Response<BaseResponse<Any>>) {
                    if (response.isSuccessful) {
                        val baseResponse = response.body()
                        Log.d(TAG, "流量统计保存成功: code=${baseResponse?.code}, msg=${baseResponse?.msg}")
                    } else {
                        try {
                            val errorBody = response.errorBody()?.string() ?: "未知错误"
                            Log.e(TAG, "流量统计保存失败: HTTP ${response.code()}, 错误信息: $errorBody")
                        } catch (e: Exception) {
                            Log.e(TAG, "解析错误响应失败", e)
                        }
                    }
                    // 无论成功与否，都断开连接并退出
                    disconnectWebSocket(endActivity)
                }
                
                override fun onFailure(call: retrofit2.Call<BaseResponse<Any>>, t: Throwable) {
                    Log.e(TAG, "流量统计保存失败", t)
                    Log.e(TAG, "错误详情: ${t.message}")
                    // 发送失败，仍然断开连接并退出
                    disconnectWebSocket(endActivity)
                }
            })
            
            // 设置超时，防止请求一直不返回
            Handler(Looper.getMainLooper()).postDelayed({
                if (!isFinishing) {
                    Log.w(TAG, "流量统计保存请求超时")
                    disconnectWebSocket(endActivity)
                }
            }, 500) // 3秒超时
            
        } catch (e: Exception) {
            Log.e(TAG, "保存流量统计失败", e)
            // 出现异常，直接断开连接
            disconnectWebSocket(endActivity)
        }
    }

    // 切换显示模式：普通模式 <-> 全屏模式
    private fun toggleDisplayMode() {
        if (currentMode == MODE_NORMAL) {
            // 从普通模式切换到全屏模式
            switchToFullscreenMode()
        } else {
            // 从全屏模式切换到普通模式
            switchToNormalMode()
        }
    }
    
    // 修复切换到全屏模式 - 确保约束正确清除
    private fun switchToFullscreenMode() {
        currentMode = MODE_FULLSCREEN
        
    Log.d(TAG, "切换到全屏模式开始")
        
    // 隐藏所有UI元素
    findViewById<LinearLayout>(R.id.status_bar).visibility = View.GONE
        findViewById<ScrollView>(R.id.control_scroll).visibility = View.GONE
        findViewById<LinearLayout>(R.id.navigation_layout).visibility = View.GONE
        findViewById<LinearLayout>(R.id.navigation_layout_left).visibility = View.GONE
        
    // **关键修复：重新设置video_layout为全屏约束**
        val videoLayout = findViewById<FrameLayout>(R.id.video_layout)
        val params = videoLayout.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
    
    // **清除所有约束**
    params.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.leftToLeft = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.rightToRight = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.leftToRight = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.rightToLeft = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.topToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.bottomToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    
    // **设置全屏约束**
    params.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
    params.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
    params.leftToLeft = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
    params.rightToRight = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
    
    // **立即应用约束**
        videoLayout.layoutParams = params
    
    // **强制重新布局**
    videoLayout.requestLayout()
        
        // 显示悬浮球
        findViewById<FrameLayout>(R.id.floating_ball_container).visibility = View.VISIBLE
        
    // **等待布局完成后再调整SurfaceView**
        videoLayout.post {
        Log.d(TAG, "全屏布局完成，开始调整SurfaceView")
        
        // 打印当前video_layout的实际位置和尺寸
        val location = IntArray(2)
        videoLayout.getLocationOnScreen(location)
        Log.d(TAG, "全屏video_layout位置: x=${location[0]}, y=${location[1]}, width=${videoLayout.width}, height=${videoLayout.height}")
        
                    adjustSurfaceViewSize()
                }
                
}
    
  // 修复切换到普通模式 - 重点修复布局约束
    private fun switchToNormalMode() {
        currentMode = MODE_NORMAL
    
    Log.d(TAG, "切换到普通模式开始")
        
        // 显示状态栏
        findViewById<LinearLayout>(R.id.status_bar).visibility = View.VISIBLE
        
        // 显示右侧工具栏
        findViewById<ScrollView>(R.id.control_scroll).visibility = View.VISIBLE
        
        // 根据屏幕方向显示对应的导航栏
        val isLandscape = resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
        if (isLandscape) {
            findViewById<LinearLayout>(R.id.navigation_layout_left).visibility = View.VISIBLE
            findViewById<LinearLayout>(R.id.navigation_layout).visibility = View.GONE
        } else {
            findViewById<LinearLayout>(R.id.navigation_layout).visibility = View.VISIBLE
            findViewById<LinearLayout>(R.id.navigation_layout_left).visibility = View.GONE
        }
        
    // **关键修复：重新设置video_layout的约束**
        val videoLayout = findViewById<FrameLayout>(R.id.video_layout)
        val params = videoLayout.layoutParams as androidx.constraintlayout.widget.ConstraintLayout.LayoutParams
    
    // **清除所有旧约束，避免冲突**
    params.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.leftToLeft = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.rightToRight = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.leftToRight = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.rightToLeft = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.topToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    params.bottomToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET
    
    // **重新设置正确的约束**
        if (isLandscape) {
        // 横屏：顶部约束到状态栏底部，左边约束到左导航右边，右边约束到右工具栏左边，底部约束到父容器底部
        params.topToBottom = R.id.status_bar
        params.leftToRight = R.id.navigation_layout_left  
        params.rightToLeft = R.id.control_scroll
        params.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        
        Log.d(TAG, "横屏模式约束设置完成")
        } else {
        // 竖屏：顶部约束到状态栏底部，左边约束到父容器左边，右边约束到右工具栏左边，底部约束到底导航顶部
        params.topToBottom = R.id.status_bar
        params.leftToLeft = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID
        params.rightToLeft = R.id.control_scroll
        params.bottomToTop = R.id.navigation_layout
        
        Log.d(TAG, "竖屏模式约束设置完成")
    }
    
    // **立即应用约束**
        videoLayout.layoutParams = params
    
    // **强制重新布局**
    videoLayout.requestLayout()
        
        // 隐藏悬浮球
        findViewById<FrameLayout>(R.id.floating_ball_container).visibility = View.GONE
        hideToolbarOverlay()
        
    // **等待布局完成后再调整SurfaceView**
        videoLayout.post {
        Log.d(TAG, "布局完成，开始调整SurfaceView")
        
        // 打印当前video_layout的实际位置和尺寸
        val location = IntArray(2)
        videoLayout.getLocationOnScreen(location)
        Log.d(TAG, "video_layout位置: x=${location[0]}, y=${location[1]}, width=${videoLayout.width}, height=${videoLayout.height}")
        
        // 打印状态栏位置
        val statusBar = findViewById<LinearLayout>(R.id.status_bar)
        statusBar.getLocationOnScreen(location)
        Log.d(TAG, "status_bar位置: x=${location[0]}, y=${location[1]}, width=${statusBar.width}, height=${statusBar.height}")
        
                adjustSurfaceViewSize()
        }
        
    }
    
    // 显示工具栏蒙版
    private fun showToolbarOverlay() {
        val toolbarOverlay = findViewById<FrameLayout>(R.id.toolbar_overlay)
        toolbarOverlay.setOnClickListener { 
            // 点击蒙版背景时关闭工具栏
            hideToolbarOverlay()
        }
        
        // 显示工具栏蒙版
        toolbarOverlay.visibility = View.VISIBLE
        
        // 更新设备名称显示
        val deviceName = intent.getStringExtra(DEVICE_NAME_KEY) ?: "云手机"
        findViewById<TextView>(R.id.overlay_header_device_name)?.text = deviceName
        
        // 更新延迟显示
        val latencyText = findViewById<TextView>(R.id.tv_latency).text.toString()
        findViewById<TextView>(R.id.overlay_header_latency)?.text = latencyText
        
        // 更新画质名称显示
        val currentQualityName = currentBitrateConfig?.name ?: "标准"
        findViewById<TextView>(R.id.overlay_tv_quality)?.text = currentQualityName
        findViewById<TextView>(R.id.overlay_header_quality)?.text = currentQualityName
    }
    
    // 隐藏工具栏蒙版
    private fun hideToolbarOverlay(autoShow: Boolean = false) {
        findViewById<FrameLayout>(R.id.toolbar_overlay).visibility = View.GONE
        
        // 如果需要延迟重新显示(用于短暂隐藏查看效果后再显示)
        if (autoShow && currentMode == MODE_FULLSCREEN) {
            Handler(Looper.getMainLooper()).postDelayed({
                showToolbarOverlay()
            }, 2000) // 2秒后再次显示
        }
    }

    // 根据屏幕方向调整悬浮球位置
    private fun adjustFloatingBallPosition(orientation: Int) {
        val floatingBallContainer = findViewById<FrameLayout>(R.id.floating_ball_container)
        
        // 尝试加载保存的位置
        if (loadFloatingBallPosition(floatingBallContainer)) {
            // 如果成功加载了保存的位置，则直接返回
            return
        }
        
        // 如果没有保存的位置，则使用默认位置
        // 获取悬浮球布局参数
        val layoutParams = floatingBallContainer.layoutParams as FrameLayout.LayoutParams
        
        // 重置所有margin
        layoutParams.leftMargin = 0
        layoutParams.topMargin = 0
        layoutParams.rightMargin = 0
        layoutParams.bottomMargin = 0
        
        if (orientation == ORIENTATION_LANDSCAPE) {
            // 横屏时，放在右侧中间
            layoutParams.gravity = Gravity.END or Gravity.CENTER_VERTICAL
            layoutParams.marginEnd = resources.getDimensionPixelSize(R.dimen.floating_ball_margin_landscape) // 需要在dimens.xml定义
        } else {
            // 竖屏时，放在右下角
            layoutParams.gravity = Gravity.END or Gravity.BOTTOM
            layoutParams.marginEnd = resources.getDimensionPixelSize(R.dimen.floating_ball_margin_portrait) // 需要在dimens.xml定义
            layoutParams.bottomMargin = resources.getDimensionPixelSize(R.dimen.floating_ball_bottom_margin) // 需要在dimens.xml定义
        }
        
        // 应用新布局参数
        floatingBallContainer.layoutParams = layoutParams
    }

    // 阻止点击事件传播到背景
    fun stopPropagation(view: View) {
        // 此方法仅用于阻止事件传播，什么都不做
        // 通过在XML中为内容区域设置android:onClick="stopPropagation"实现
    }

    // 添加截图功能实现
    private fun captureScreenshot() {
        try {
            // 获取当前显示的内容
            val bitmap = getBitmapFromSurface()
            if (bitmap == null) {
                Toast.makeText(this, "截图失败", Toast.LENGTH_SHORT).show()
                return
            }

            // 创建存储目录
            val storageDir = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), "CloudPhone")
            if (!storageDir.exists()) {
                storageDir.mkdirs()
            }

            // 生成时间戳文件名
            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val imageFile = File(storageDir, "Screenshot_$timeStamp.jpg")

            // 保存图片
            FileOutputStream(imageFile).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out)
            }

            // 通知媒体库更新
            MediaScannerConnection.scanFile(this, arrayOf(imageFile.toString()), null) { path, uri ->
                Log.d(TAG, "图片已保存: $path")
            }

            Toast.makeText(this, "截图已保存至相册", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e(TAG, "截图失败", e)
            Toast.makeText(this, "截图失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    // 从Surface获取Bitmap
    private fun getBitmapFromSurface(): Bitmap? {
        if (surfaceView == null || surfaceView.width == 0 || surfaceView.height == 0) {
            return null
        }

        try {
            // 创建与SurfaceView相同大小的像素缓冲区
            val bitmap = Bitmap.createBitmap(surfaceView.width, surfaceView.height, Bitmap.Config.ARGB_8888)
            
            // 从Surface上绘制内容到Canvas
            val canvas = Canvas(bitmap)
            surfaceView.draw(canvas)
            
            return bitmap
        } catch (e: Exception) {
            Log.e(TAG, "从Surface获取Bitmap失败", e)
            return null
        }
    }
    
    // 权限请求结果处理
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 100) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                // 权限已授予
                Log.d(TAG, "存储权限已授予")
            } else {
                // 权限被拒绝
                Toast.makeText(this, "截图功能需要存储权限", Toast.LENGTH_SHORT).show()
            }
        }
    }

    // 更新画质显示
    private fun updateQualityDisplay(qualityName: String) {
        runOnUiThread {
            // 更新状态栏的画质显示
            findViewById<TextView>(R.id.status_tv_quality)?.text = qualityName
            
            // 更新悬浮窗中的画质显示
            findViewById<TextView>(R.id.overlay_tv_quality)?.text = qualityName
            findViewById<TextView>(R.id.overlay_header_quality)?.text = qualityName
        }
    }
    
    // 在画质切换成功后调用此方法
    private fun onQualityChangeSuccess(config: BitrateConfig) {
        // 保存当前配置
        currentBitrateConfig = config
        
        // 更新画质显示
        updateQualityDisplay(config.name)
        
        // 隐藏切换中提示
        findViewById<View>(R.id.quality_change_overlay).visibility = View.GONE
        findViewById<TextView>(R.id.tv_quality_changing).visibility = View.GONE
        
        // 重置标志
        isChangingQuality = false
        
        // 取消超时处理
        qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
        
        // 显示成功提示
    }

    // 保存悬浮球位置的方法
    private fun saveFloatingBallPosition(view: View) {
        val layoutParams = view.layoutParams as FrameLayout.LayoutParams
        
        // 保存位置到SharedPreferences
        val prefs = getSharedPreferences("floating_ball_prefs", Context.MODE_PRIVATE)
        val orientation = resources.configuration.orientation
        
        prefs.edit().apply {
            putInt("left_margin_${orientation}", layoutParams.leftMargin)
            putInt("top_margin_${orientation}", layoutParams.topMargin)
            apply()
        }
    }
    
    // 加载悬浮球保存的位置
    private fun loadFloatingBallPosition(view: View): Boolean {
        val prefs = getSharedPreferences("floating_ball_prefs", Context.MODE_PRIVATE)
        val orientation = resources.configuration.orientation
        
        val leftMargin = prefs.getInt("left_margin_${orientation}", -1)
        val topMargin = prefs.getInt("top_margin_${orientation}", -1)
        
        // 如果有保存的位置，则使用保存的位置
        if (leftMargin != -1 && topMargin != -1) {
            val layoutParams = view.layoutParams as FrameLayout.LayoutParams
            layoutParams.gravity = Gravity.NO_GRAVITY
            layoutParams.leftMargin = leftMargin
            layoutParams.topMargin = topMargin
            layoutParams.rightMargin = 0
            layoutParams.bottomMargin = 0
            view.layoutParams = layoutParams
            return true
        }
        
        return false
    }
} 

