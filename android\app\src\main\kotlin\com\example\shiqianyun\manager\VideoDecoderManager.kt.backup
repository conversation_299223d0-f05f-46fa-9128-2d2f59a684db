package com.example.shiqianyun.manager

import android.content.pm.ActivityInfo
import android.content.res.Configuration
import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.os.Handler
import android.os.HandlerThread
import android.os.Process
import android.util.Log
import android.view.Surface
import android.view.SurfaceView
import androidx.appcompat.app.AppCompatActivity
import java.nio.ByteBuffer
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.atomic.AtomicBoolean

class VideoDecoderManager(
    private val surfaceView: SurfaceView,
    private val activity: AppCompatActivity,
    private val onDecoderConfigured: () -> Unit,
    private val onDecodeSuccess: () -> Unit,
    private val onDecodeFailure: () -> Unit,
    private val onResolutionChanged: (Int, Int) -> Unit
) {
    companion object {
        private const val TAG = "VideoDecoderManager"
        private const val MAX_DECODE_FAILURES = 15
        private const val DECODE_TIMEOUT_MS = 5000L
        private const val MIN_RECOVERY_INTERVAL_MS = 10000L
    }

    // 解码器相关
    private var decoder: MediaCodec? = null
    private var isDecoderConfigured = false
    private var isConfiguringDecoder = false
    
    // 分辨率缓存
    private var cachedWidth = 0
    private var cachedHeight = 0
    private var spsProcessed = false
    private var lastConfigTime = 0L
    
    // Surface尺寸跟踪
    private var lastSurfaceWidth = 0
    private var lastSurfaceHeight = 0
    
    // SPS/PPS数据
    private var sps: ByteArray? = null
    private var pps: ByteArray? = null
    
    // 帧处理
    private val frameQueue = LinkedBlockingQueue<ByteArray>(200)
    private var isProcessingFrames = false
    private var lastProcessTime = 0L
    
    // 帧处理器线程
    private val frameProcessor = HandlerThread("FrameProcessor", Process.THREAD_PRIORITY_URGENT_DISPLAY).apply {
        start()
    }.let { Handler(it.looper) }
    
    // 花屏检测和恢复
    private var consecutiveDecodeFailures = 0
    private var lastSuccessfulDecodeTime = 0L
    private var corruptionDetected = false
    private var lastCorruptionRecoveryTime = 0L

    /**
     * 处理SPS帧
     */
    fun handleSPS(data: ByteArray) {
        if (spsProcessed && sps?.contentEquals(data) == true) {
            return // 相同SPS，跳过
        }
        
        sps = data
        val (width, height) = extractResolutionFromSPS(data)
        
        if (width > 0 && height > 0) {
            val needReconfigure = (cachedWidth != width || cachedHeight != height)
            
            cachedWidth = width
            cachedHeight = height
            spsProcessed = true
            
            Log.d(TAG, "SPS解析: ${width}x${height}, 需要重配置: $needReconfigure")
            
            // 检测并处理旋转
            checkAndHandleOrientation(width, height)
            
            if (needReconfigure) {
                resetDecoder()
            }
            
            // 通知分辨率变化
            onResolutionChanged(width, height)
        }
        
        tryConfigureDecoder()
    }

    /**
     * 处理PPS帧
     */
    fun handlePPS(data: ByteArray) {
        pps = data
        Log.d(TAG, "收到PPS帧")
        tryConfigureDecoder()
    }

    /**
     * 处理关键帧
     */
    fun handleKeyFrame(data: ByteArray) {
        Log.d(TAG, "收到关键帧")
        
        if (!isDecoderConfigured) {
            tryConfigureDecoder()
        }
        
        if (isDecoderConfigured) {
            // 温和地清理队列
            val queueSize = frameQueue.size
            if (queueSize > 30) {
                val framesToKeep = mutableListOf<ByteArray>()
                val keepCount = minOf(10, queueSize)
                repeat(keepCount) {
                    frameQueue.poll()?.let { framesToKeep.add(it) }
                }
                frameQueue.clear()
                framesToKeep.forEach { frameQueue.offer(it) }
                Log.d(TAG, "关键帧温和清理队列：${queueSize} -> ${frameQueue.size}")
            }
            
            if (!isProcessingFrames) {
                processVideoFrame(data)
            } else {
                // 将关键帧插入队列头部
                val tempQueue = LinkedBlockingQueue<ByteArray>()
                tempQueue.offer(data)
                while (frameQueue.isNotEmpty()) {
                    frameQueue.poll()?.let { tempQueue.offer(it) }
                }
                frameQueue.clear()
                while (tempQueue.isNotEmpty()) {
                    tempQueue.poll()?.let { frameQueue.offer(it) }
                }
                Log.d(TAG, "关键帧已插入队列头部")
            }
        }
    }

    /**
     * 处理普通帧
     */
    fun handleRegularFrame(data: ByteArray) {
        if (!isDecoderConfigured) return
        
        // 防止在配置期间处理帧
        if (isConfiguringDecoder) {
            Log.v(TAG, "跳过帧处理：正在配置中")
            return
        }
        
        val queueSize = frameQueue.size
        
        // 改进的丢帧策略
        when {
            queueSize >= 200 -> {
                val keepCount = queueSize / 2
                val framesToKeep = mutableListOf<ByteArray>()
                repeat(keepCount) {
                    frameQueue.poll()?.let { framesToKeep.add(it) }
                }
                frameQueue.clear()
                framesToKeep.forEach { frameQueue.offer(it) }
                Log.w(TAG, "严重积压(${queueSize})，保守清理到${frameQueue.size}")
            }
            queueSize >= 120 -> {
                repeat(queueSize / 3) {
                    frameQueue.poll()
                }
                Log.w(TAG, "中等积压(${queueSize})，适度清理到${frameQueue.size}")
            }
            queueSize >= 80 -> {
                repeat(minOf(10, queueSize / 4)) {
                    frameQueue.poll()
                }
                Log.v(TAG, "轻微积压(${queueSize})，轻微清理到${frameQueue.size}")
            }
        }
        
        if (!frameQueue.offer(data)) {
            Log.w(TAG, "队列已满，丢弃当前帧")
            return
        }
        
        // 智能触发处理
        when {
            queueSize > 50 -> processFrameQueue()
            queueSize > 20 -> frameProcessor.post { processFrameQueue() }
            else -> frameProcessor.postDelayed({ processFrameQueue() }, 8)
        }
    }

    /**
     * 尝试配置解码器
     */
    private fun tryConfigureDecoder() {
        if (isConfiguringDecoder || sps == null) return
        
        val now = System.currentTimeMillis()
        if (now - lastConfigTime < 500) return
        
        if (spsProcessed && cachedWidth > 0 && cachedHeight > 0) {
            configureDecoderOnce()
            lastConfigTime = now
        }
    }

    /**
     * 配置解码器
     */
    private fun configureDecoderOnce() {
        if (isDecoderConfigured || isConfiguringDecoder) return
        
        isConfiguringDecoder = true
        
        try {
            val holder = surfaceView.holder
            val surface = holder.surface
            
            if (surface == null || !surface.isValid) {
                Log.e(TAG, "Surface无效，延迟重试")
                frameProcessor.postDelayed({
                    isConfiguringDecoder = false
                    tryConfigureDecoder()
                }, 100)
                return
            }

            val surfaceWidth = surfaceView.width
            val surfaceHeight = surfaceView.height
            if (surfaceWidth <= 0 || surfaceHeight <= 0) {
                Log.e(TAG, "SurfaceView尺寸无效: ${surfaceWidth}x${surfaceHeight}")
                frameProcessor.postDelayed({
                    isConfiguringDecoder = false
                    tryConfigureDecoder()
                }, 100)
                return
            }

            val width = if (cachedWidth > 0) cachedWidth else 0
            val height = if (cachedHeight > 0) cachedHeight else 0
            
            Log.d(TAG, "配置解码器: ${width}x${height}, Surface: ${surfaceWidth}x${surfaceHeight}")
            
            if (sps == null || sps!!.size < 4) {
                Log.e(TAG, "SPS数据无效，大小: ${sps?.size ?: 0}")
                isConfiguringDecoder = false
                return
            }
            
            if (width <= 0 || height <= 0 || width > 4096 || height > 4096) {
                Log.e(TAG, "分辨率无效: ${width}x${height}")
                isConfiguringDecoder = false
                return
            }
            
            releaseDecoderSafely()
            Thread.sleep(200)
            
            try {
                val format = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, width, height)
                
                try {
                    format.setByteBuffer("csd-0", ByteBuffer.wrap(sps!!))
                    Log.d(TAG, "SPS数据设置成功，大小: ${sps!!.size}")
                } catch (e: Exception) {
                    Log.e(TAG, "设置SPS数据失败", e)
                    throw e
                }
                
                if (pps != null && pps!!.size > 0) {
                    try {
                        format.setByteBuffer("csd-1", ByteBuffer.wrap(pps!!))
                        Log.d(TAG, "PPS数据设置成功，大小: ${pps!!.size}")
                    } catch (e: Exception) {
                        Log.w(TAG, "设置PPS数据失败，继续尝试", e)
                    }
                }
                    
                format.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface)
                format.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, width * height * 2)
                format.setInteger(MediaFormat.KEY_FRAME_RATE, 30)
                format.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1)
                
                // 优化缓冲区管理，防止花屏
                format.setInteger(MediaFormat.KEY_MAX_WIDTH, width)
                format.setInteger(MediaFormat.KEY_MAX_HEIGHT, height)
                format.setInteger("max-bframes", 0) // 禁用B帧，减少延迟
                format.setInteger("low-latency", 1) // 启用低延迟模式
                
                // 设置优化参数
                try {
                    format.setInteger("priority", 0) // 最高优先级
                    format.setInteger("operating-rate", 60) // 设置操作频率
                } catch (e: Exception) {
                    Log.w(TAG, "设置优化参数失败，继续", e)
                }
                
                Log.d(TAG, "MediaFormat配置: $format")
                
                decoder = MediaCodec.createDecoderByType(MediaFormat.MIMETYPE_VIDEO_AVC)
                
                try {
                    decoder?.configure(format, surface, null, 0)
                    Log.d(TAG, "解码器configure成功")
                } catch (e: IllegalArgumentException) {
                    Log.e(TAG, "解码器configure失败 - 参数错误", e)
                    Log.e(TAG, "Format详情: width=$width, height=$height, spsSize=${sps?.size}, ppsSize=${pps?.size}")
                    throw e
                } catch (e: IllegalStateException) {
                    Log.e(TAG, "解码器configure失败 - 状态错误", e)
                    throw e
                }
                
                decoder?.start()
                Log.d(TAG, "解码器start成功")
                
                isDecoderConfigured = true
                Log.d(TAG, "解码器配置完全成功")
                
                onDecoderConfigured()
                frameQueue.clear()
                
            } catch (e: Exception) {
                Log.e(TAG, "创建或配置解码器失败", e)
                releaseDecoderSafely()
                throw e
            }
                
        } catch (e: Exception) {
            Log.e(TAG, "配置解码器失败", e)
            releaseDecoderSafely()
            
            frameProcessor.postDelayed({
                isConfiguringDecoder = false
                if (sps != null && !isDecoderConfigured) {
                    tryConfigureDecoder()
                }
            }, 2000)
            
        } finally {
            if (isDecoderConfigured) {
                isConfiguringDecoder = false
            }
        }
    }

    /**
     * 处理帧队列
     */
    private fun processFrameQueue() {
        if (isProcessingFrames) return
        
        val currentTime = System.currentTimeMillis()
        
        if (currentTime - lastProcessTime < 8) {
            return
        }
        
        isProcessingFrames = true
        lastProcessTime = currentTime
        
        frameProcessor.post {
            try {
                var processedCount = 0
                val startTime = System.currentTimeMillis()
                
                val maxProcessTime = 25
                val maxFrames = 15
                
                while (frameQueue.isNotEmpty() && 
                       processedCount < maxFrames && 
                       (System.currentTimeMillis() - startTime) < maxProcessTime) {
                    
                    val frameData = frameQueue.poll()
                    if (frameData != null) {
                        processVideoFrame(frameData)
                        processedCount++
                    }
                }
                
                val queueSize = frameQueue.size
                        
            } finally {
                isProcessingFrames = false
                
                if (frameQueue.isNotEmpty()) {
                    val queueSize = frameQueue.size
                    if (queueSize > 50) {
                        processFrameQueue()
                    } else if (queueSize > 20) {
                        frameProcessor.post { processFrameQueue() }
                    } else {
                        frameProcessor.postDelayed({ processFrameQueue() }, 8)
                    }
                }
            }
        }
    }

    /**
     * 处理视频帧
     */
    private fun processVideoFrame(data: ByteArray): Boolean {
        val codec = decoder ?: return false
        
        // 检查解码器状态
        if (!isDecoderConfigured || isConfiguringDecoder) {
            Log.v(TAG, "解码器未就绪，跳过帧处理")
            return false
        }
        
        if (corruptionDetected) {
            Log.v(TAG, "花屏恢复中，暂停处理帧")
            return false
        }
        
        // 检查Surface状态
        val holder = surfaceView.holder
        val surface = holder.surface
        if (surface == null || !surface.isValid) {
            Log.v(TAG, "Surface无效，跳过帧处理")
            return false
        }
        
        try {
            val inputBufferId = codec.dequeueInputBuffer(0)
            if (inputBufferId >= 0) {
                val inputBuffer = codec.getInputBuffer(inputBufferId) ?: return false
                
                inputBuffer.clear()
                inputBuffer.put(data)
                
                codec.queueInputBuffer(inputBufferId, 0, data.size, System.nanoTime() / 1000, 0)
            }
            
            val bufferInfo = MediaCodec.BufferInfo()
            var outputBufferId = codec.dequeueOutputBuffer(bufferInfo, 0)
            var hasOutput = false
            
            while (outputBufferId >= 0) {
                hasOutput = true
                // 确保Surface仍然有效
                if (surface.isValid) {
                    codec.releaseOutputBuffer(outputBufferId, true)
                } else {
                    codec.releaseOutputBuffer(outputBufferId, false)
                    Log.w(TAG, "Surface在解码过程中变为无效")
                    break
                }
                outputBufferId = codec.dequeueOutputBuffer(bufferInfo, 0)
            }
            
            if (hasOutput) {
                recordDecodeSuccess()
            }
            
            return true
        } catch (e: IllegalStateException) {
            Log.w(TAG, "解码器状态异常: ${e.message}")
            recordDecodeFailure()
            return false
        } catch (e: Exception) {
            Log.w(TAG, "处理视频帧失败: ${e.message}")
            recordDecodeFailure()
            return false
        }
    }

    /**
     * 重置解码器
     */
    fun resetDecoder() {
        try {
            decoder?.stop()
            decoder?.release()
            decoder = null
            
            isDecoderConfigured = false
            isConfiguringDecoder = false
            
            Log.d(TAG, "解码器已重置")
        } catch (e: Exception) {
            Log.e(TAG, "重置解码器失败", e)
            decoder = null
            isDecoderConfigured = false
            isConfiguringDecoder = false
        }
    }

    /**
     * 安全释放解码器
     */
    private fun releaseDecoderSafely() {
        try {
            if (decoder != null) {
                Log.d(TAG, "解码器已停止")
                decoder?.stop()
                Log.d(TAG, "解码器已释放")
                decoder?.release()
                decoder = null
            }
            
            isDecoderConfigured = false
            
        } catch (e: Exception) {
            Log.e(TAG, "释放解码器时出错", e)
            decoder = null
            isDecoderConfigured = false
        }
    }

    /**
     * 从SPS提取分辨率
     */
    private fun extractResolutionFromSPS(spsData: ByteArray): Pair<Int, Int> {
        try {
            var offset = when {
                spsData.size >= 4 && spsData[0] == 0x00.toByte() && spsData[1] == 0x00.toByte() &&
                spsData[2] == 0x00.toByte() && spsData[3] == 0x01.toByte() -> 4
                spsData.size >= 3 && spsData[0] == 0x00.toByte() && spsData[1] == 0x00.toByte() &&
                spsData[2] == 0x01.toByte() -> 3
                else -> 0
            }
            
            if (spsData.size > offset + 10) {
                try {
                    val nalUnitType = spsData[offset].toInt() and 0x1F
                    if (nalUnitType == 7) {
                        val reader = BitReader(spsData, offset + 1)
                        
                        reader.skipBits(24)
                        val seqParameterSetId = reader.readUE()
                        
                        val profileIdc = spsData[offset + 1].toInt() and 0xFF
                        
                        if (profileIdc == 100 || profileIdc == 110 || profileIdc == 122 || profileIdc == 244 ||
                            profileIdc == 44 || profileIdc == 83 || profileIdc == 86 || profileIdc == 118 ||
                            profileIdc == 128) {
                            
                            val chromaFormatIdc = reader.readUE()
                            
                            if (chromaFormatIdc == 3) {
                                reader.skipBits(1)
                            }
                            
                            reader.readUE()
                            reader.readUE()
                            reader.skipBits(1)
                            
                            val seqScalingMatrixPresentFlag = reader.readBits(1)
                            if (seqScalingMatrixPresentFlag == 1) {
                                val limit = if (chromaFormatIdc != 3) 8 else 12
                                for (i in 0 until limit) {
                                    val seqScalingListPresentFlag = reader.readBits(1)
                                    if (seqScalingListPresentFlag == 1) {
                                        val size = if (i < 6) 16 else 64
                                        var lastScale = 8
                                        var nextScale = 8
                                        for (j in 0 until size) {
                                            if (nextScale != 0) {
                                                val deltaScale = reader.readSE()
                                                nextScale = (lastScale + deltaScale + 256) % 256
                                            }
                                            lastScale = if (nextScale == 0) lastScale else nextScale
                                        }
                                    }
                                }
                            }
                        }
                        
                        reader.readUE()
                        val picOrderCntType = reader.readUE()
                        
                        if (picOrderCntType == 0) {
                            reader.readUE()
                        } else if (picOrderCntType == 1) {
                            reader.skipBits(1)
                            reader.readSE()
                            reader.readSE()
                            
                            val numRefFramesInPicOrderCntCycle = reader.readUE()
                            for (i in 0 until numRefFramesInPicOrderCntCycle) {
                                reader.readSE()
                            }
                        }
                        
                        reader.readUE()
                        reader.skipBits(1)
                        
                        val picWidthInMbsMinus1 = reader.readUE()
                        val picHeightInMapUnitsMinus1 = reader.readUE()
                        
                        val width = (picWidthInMbsMinus1 + 1) * 16
                        var height = (picHeightInMapUnitsMinus1 + 1) * 16
                        
                        val frameMbsOnlyFlag = reader.readBits(1)
                        
                        if (frameMbsOnlyFlag == 0) {
                            height *= 2
                            reader.skipBits(1)
                        }
                        
                        reader.skipBits(1)
                        
                        val frameCroppingFlag = reader.readBits(1)
                        var finalWidth = width
                        var finalHeight = height
                        
                        if (frameCroppingFlag == 1) {
                            val cropLeft = reader.readUE()
                            val cropRight = reader.readUE()
                            val cropTop = reader.readUE()
                            val cropBottom = reader.readUE()
                            
                            finalWidth = width - (cropLeft + cropRight) * 2
                            finalHeight = height - (cropTop + cropBottom) * 2
                        }
                        
                        return Pair(finalWidth, finalHeight)
                    }
                } catch (e: Exception) {
                    // 忽略解析异常
                }
            }
            
            return Pair(0, 0)
        } catch (e: Exception) {
            return Pair(0, 0)
        }
    }

    /**
     * 记录解码成功
     */
    private fun recordDecodeSuccess() {
        consecutiveDecodeFailures = 0
        lastSuccessfulDecodeTime = System.currentTimeMillis()
    }

    /**
     * 记录解码失败
     */
    private fun recordDecodeFailure() {
        consecutiveDecodeFailures++
        Log.v(TAG, "解码失败计数：$consecutiveDecodeFailures")
        
        detectAndHandleCorruption()
    }

    /**
     * 检测和处理花屏
     */
    private fun detectAndHandleCorruption() {
        val currentTime = System.currentTimeMillis()
        
        if (consecutiveDecodeFailures >= MAX_DECODE_FAILURES) {
            Log.w(TAG, "检测到连续解码失败${consecutiveDecodeFailures}次，可能存在花屏")
            triggerCorruptionRecovery("连续解码失败")
            return
        }
        
        if (lastSuccessfulDecodeTime > 0 && 
            currentTime - lastSuccessfulDecodeTime > DECODE_TIMEOUT_MS) {
            Log.w(TAG, "解码超时${currentTime - lastSuccessfulDecodeTime}ms，可能存在花屏")
            triggerCorruptionRecovery("解码超时")
            return
        }
        
        if (frameQueue.size > 100) {
            Log.w(TAG, "帧队列长期积压${frameQueue.size}，可能存在解码器问题")
            triggerCorruptionRecovery("队列积压")
        }
    }

    /**
     * 触发花屏恢复
     */
    private fun triggerCorruptionRecovery(reason: String) {
        val currentTime = System.currentTimeMillis()
        
        if (currentTime - lastCorruptionRecoveryTime < MIN_RECOVERY_INTERVAL_MS) {
            Log.d(TAG, "恢复间隔太短，跳过恢复操作")
            return
        }
        
        Log.w(TAG, "触发花屏恢复，原因：$reason")
        corruptionDetected = true
        lastCorruptionRecoveryTime = currentTime
        
        try {
            isProcessingFrames = true
            frameQueue.clear()
            resetDecoder()
            
            consecutiveDecodeFailures = 0
            lastSuccessfulDecodeTime = currentTime
            
            frameProcessor.postDelayed({
                isProcessingFrames = false
                corruptionDetected = false
                Log.d(TAG, "花屏恢复完成，恢复正常解码")
                
                if (sps != null) {
                    frameProcessor.postDelayed({
                        tryConfigureDecoder()
                    }, 500)
                }
            }, 1000)
            
        } catch (e: Exception) {
            Log.e(TAG, "花屏恢复失败", e)
            isProcessingFrames = false
            corruptionDetected = false
        }
    }

    /**
     * 检测并处理屏幕旋转
     */
    private fun checkAndHandleOrientation(newWidth: Int, newHeight: Int) {
        try {
            val isContentLandscape = newWidth > newHeight
            val currentOrientation = activity.resources.configuration.orientation
            val isCurrentLandscape = currentOrientation == Configuration.ORIENTATION_LANDSCAPE
            
            Log.d(TAG, "旋转检测: 内容=${newWidth}x${newHeight} (${if (isContentLandscape) "横屏" else "竖屏"}), " +
                     "设备方向=${if (isCurrentLandscape) "横屏" else "竖屏"}")
            
            activity.runOnUiThread {
                if (isContentLandscape && !isCurrentLandscape) {
                    // 内容是横屏，但设备当前是竖屏 -> 强制旋转为横屏
                    Log.d(TAG, "内容为横屏，强制旋转设备为横屏")
                    activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                } else if (!isContentLandscape && isCurrentLandscape) {
                    // 内容是竖屏，但设备当前是横屏 -> 强制旋转为竖屏
                    Log.d(TAG, "内容为竖屏，强制旋转设备为竖屏")
                    activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                } else {
                    Log.d(TAG, "内容方向与设备方向匹配，无需旋转")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理屏幕旋转失败", e)
        }
    }

    /**
     * 处理运行时分辨率变化（来自视频流）
     */
    fun handleRuntimeResolutionChange(newWidth: Int, newHeight: Int) {
        try {
            Log.d(TAG, "运行时分辨率变化: ${newWidth}x${newHeight}")
            
            val isLandscape = newWidth > newHeight
            val currentOrientation = activity.resources.configuration.orientation
            val isCurrentLandscape = currentOrientation == Configuration.ORIENTATION_LANDSCAPE
            
            // 只有当内容方向与当前设备方向不匹配时才旋转
            activity.runOnUiThread {
                if (isLandscape && !isCurrentLandscape) {
                    Log.d(TAG, "运行时检测到横屏内容，旋转为横屏")
                    activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                } else if (!isLandscape && isCurrentLandscape) {
                    Log.d(TAG, "运行时检测到竖屏内容，旋转为竖屏")
                    activity.requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理运行时分辨率变化失败", e)
        }
    }

    /**
     * 获取当前分辨率
     */
    fun getCurrentResolution(): Pair<Int, Int> {
        return Pair(cachedWidth, cachedHeight)
    }

    /**
     * 是否已配置解码器
     */
    fun isConfigured(): Boolean = isDecoderConfigured

    /**
     * Surface变化通知
     */
    fun notifySurfaceChanged() {
        // 延迟检查Surface稳定性，避免频繁重配置
        frameProcessor.removeCallbacksAndMessages(null)
        frameProcessor.postDelayed({
            try {
                val holder = surfaceView.holder
                val surface = holder.surface
                
                if (surface != null && surface.isValid) {
                    val surfaceWidth = surfaceView.width
                    val surfaceHeight = surfaceView.height
                    
                    // 检查Surface尺寸是否真的变化了
                    if (surfaceWidth == lastSurfaceWidth && surfaceHeight == lastSurfaceHeight) {
                        Log.v(TAG, "Surface尺寸无变化，跳过处理: ${surfaceWidth}x${surfaceHeight}")
                        return@postDelayed
                    }
                    
                    lastSurfaceWidth = surfaceWidth
                    lastSurfaceHeight = surfaceHeight
                    
                    Log.d(TAG, "Surface尺寸变化: ${surfaceWidth}x${surfaceHeight}, 解码器已配置: $isDecoderConfigured")
                    
                    // 只在解码器未配置时才尝试配置
                    if (!isDecoderConfigured && (surfaceWidth > 0 && surfaceHeight > 0) && sps != null) {
                        Log.d(TAG, "解码器未配置但Surface已稳定，尝试配置解码器")
                        tryConfigureDecoder()
                    } else if (isDecoderConfigured) {
                        Log.d(TAG, "解码器已配置，Surface尺寸变化不影响解码")
                        // 解码器已配置，MediaCodec可以适应Surface尺寸变化，无需重置
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "处理Surface变化失败", e)
            }
        }, 150) // 减少延迟到150ms
    }

    /**
     * 强制重新配置解码器（仅在必要时使用，如视频分辨率变化）
     */
    fun forceReconfigure() {
        Log.d(TAG, "强制重新配置解码器")
        frameProcessor.post {
            try {
                if (isDecoderConfigured) {
                    resetDecoder()
                    frameProcessor.postDelayed({
                        tryConfigureDecoder()
                    }, 100)
                }
            } catch (e: Exception) {
                Log.e(TAG, "强制重新配置失败", e)
            }
        }
    }

    /**
     * 释放资源
     */
    fun release() {
        releaseDecoderSafely()
        frameProcessor.looper.quit()
    }
}

/**
 * 位流读取器辅助类
 */
private class BitReader(private val data: ByteArray, private var offset: Int) {
    private var bitOffset = 0

    fun skipBits(count: Int) {
        val totalBits = bitOffset + count
        offset += totalBits / 8
        bitOffset = totalBits % 8
    }

    fun readBits(count: Int): Int {
        var result = 0
        for (i in 0 until count) {
            if (offset >= data.size) return result
            
            val bit = (data[offset].toInt() shr (7 - bitOffset)) and 1
            result = (result shl 1) or bit
            
            bitOffset++
            if (bitOffset == 8) {
                bitOffset = 0
                offset++
            }
        }
        return result
    }

    fun readUE(): Int {
        var leadingZeros = 0
        while (readBits(1) == 0) {
            leadingZeros++
        }
        
        if (leadingZeros == 0) return 0
        
        val value = readBits(leadingZeros)
        return (1 shl leadingZeros) - 1 + value
    }

    fun readSE(): Int {
        val value = readUE()
        return if (value % 2 == 0) -(value / 2) else (value + 1) / 2
    }
}
