<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
        <item name="android:windowBackground">@drawable/launch_background</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <item name="android:windowBackground">?android:colorBackground</item>
    </style>
    
    <!-- Material Components主题 -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- 主要颜色 -->
        <item name="colorPrimary">#529cff</item>
        <item name="colorPrimaryDark">#3b70b9</item>
        <item name="colorAccent">#529cff</item>
        
        <!-- 状态栏颜色 -->
        <item name="android:statusBarColor">#FFFFFF</item>
        <item name="android:windowLightStatusBar">false</item>
        
        <!-- 文本输入框样式 -->
        <item name="textInputStyle">@style/Widget.App.TextInputLayout</item>
    </style>
    
    <!-- 自定义TextInputLayout样式 -->
    <style name="Widget.App.TextInputLayout" parent="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxStrokeColor">#529cff</item>
        <item name="hintTextColor">#529cff</item>
    </style>
    
    <!-- 底部弹出对话框样式 -->
    <style name="BottomDialogStyle" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowAnimationStyle">@style/BottomDialogAnimation</item>
    </style>
    
    <!-- 底部弹窗动画 -->
    <style name="BottomDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_in_bottom</item>
        <item name="android:windowExitAnimation">@anim/slide_out_bottom</item>
    </style>

    <!-- 全屏对话框样式 -->
    <style name="FullScreenDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowBackground">@android:color/white</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowFullscreen">false</item>
    </style>
     <style name="Theme.WsMyAndroid" parent="android:Theme.Material.Light.NoActionBar" />
     
     <!-- 全屏主题 -->
     <style name="FullscreenTheme" parent="android:Theme.Material.Light.NoActionBar">
         <item name="android:windowFullscreen">true</item>
         <item name="android:windowNoTitle">true</item>
         <item name="android:windowActionBar">false</item>
         <item name="android:windowBackground">@android:color/black</item>
         <item name="android:windowContentOverlay">@null</item>
         <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
         <item name="android:statusBarColor">@android:color/transparent</item>
         <item name="android:navigationBarColor">@android:color/transparent</item>
     </style>
</resources>
