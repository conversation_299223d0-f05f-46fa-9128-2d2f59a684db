package com.example.shiqianyun.network

import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.CloudFile
import com.example.shiqianyun.network.model.DeviceInfo
import com.example.shiqianyun.network.model.DeviceListResponse
import com.example.shiqianyun.network.model.GroupDevicesRequest
import com.example.shiqianyun.network.model.LoginRequest
import com.example.shiqianyun.network.model.LoginResponse
import com.example.shiqianyun.network.model.OrderSubmitRequest
import com.example.shiqianyun.network.model.RenewRequest
import com.example.shiqianyun.network.model.SessionResponse
import com.example.shiqianyun.network.model.SystemPackage
import com.example.shiqianyun.network.model.UpdateGroupRequest
import com.example.shiqianyun.network.model.VipLevel
import com.example.shiqianyun.network.model.SessionRequest
import com.example.shiqianyun.network.model.BitrateConfig
import com.example.shiqianyun.network.model.TrafficStatsRequest
import com.example.shiqianyun.network.model.ConsumptionRecordResponse
import com.example.shiqianyun.network.model.ConsumptionRecordRequest
import com.example.shiqianyun.network.model.AnnouncementConfigResponse
import com.example.shiqianyun.network.model.NodeService
import okhttp3.RequestBody
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Query
import retrofit2.http.Url

/**
 * API接口服务
 */
interface ApiService {
    /**
     * 获取验证码
     */
    @GET("/generateCode")
    fun generateCode(@Query("phone") phone: String): Call<BaseResponse<Any>>
    
    /**
     * 检查手机号是否已注册
     */
    @GET("/api/clientUser/inspectPhone")
    fun checkPhoneExists(@Query("phone") phone: String): Call<BaseResponse<Any>>
    
    /**
     * 手机号登录/注册
     */
    @POST("/api/clientUser/register")
    fun register(@Body request: LoginRequest): Call<BaseResponse<LoginResponse>>
    
    /**
     * 获取全部设备列表
     */
    @GET("/api/app/rent/allPhone")
    fun getAllPhones(@Query("user_id") userId: Int): Call<BaseResponse<DeviceListResponse>>
    
    /**
     * 获取分组内设备
     */
    @GET("/api/app/group/get")
    fun getGroupDevices(
        @Query("group_id") groupId: Int,
        @Query("limit") limit: Int,
        @Query("sort") sort: String
    ): Call<BaseResponse<List<DeviceInfo>>>
    
    /**
     * 添加分组
     */
    @POST("/api/app/group/add")
    fun addGroup(
        @Query("group_name") groupName: String,
        @Query("user_id") userId: String
    ): Call<BaseResponse<Any>>
    
    /**
     * 更新分组
     */
    @PUT("/api/app/group/update")
    fun updateGroup(
        @Body request: UpdateGroupRequest
    ): Call<BaseResponse<Any>>
    
    /**
     * 删除分组
     */
    @retrofit2.http.DELETE("/api/app/group/del")
    fun deleteGroup(
        @Query("user_id") userId: String,
        @Query("group_id") groupId: String
    ): Call<BaseResponse<Any>>
    
    /**
     * 获取VIP等级列表
     */
    @GET("/api/package/vip")
    fun getVipLevels(): Call<BaseResponse<List<VipLevel>>>
    
    /**
     * 获取用户消费记录
     */
    @POST("/api/clientUser/userOrder")
    fun getUserConsumptionRecords(
        @Body request: ConsumptionRecordRequest
    ): Call<ConsumptionRecordResponse>
    
    /**
     * 激活码激活 (使用查询参数)
     */
    @POST("/api/app/activateCode/activate")
    fun activateCode(
        @Query("user_id") userId: Int,
        @Query("activate_code") activateCode: String,
        @Query("tag_side") tagSide: Int = 4
    ): Call<BaseResponse<Any>>
    
    /**
     * 激活码激活 (使用JSON请求体)
     */
    @POST
    @retrofit2.http.Headers("Content-Type: application/json")
    fun activateCodeWithBody(
        @Url url: String,
        @Body requestBody: RequestBody
    ): Call<BaseResponse<Any>>
    
    /**
     * 激活码续费 (使用JSON请求体)
     */
    @POST
    @retrofit2.http.Headers("Content-Type: application/json")
    fun renewCodeWithBody(
        @Url url: String,
        @Body requestBody: RequestBody
    ): Call<BaseResponse<Any>>
    
    /**
     * 获取VIP套餐信息
     */
    @GET("/api/package/get")
    fun getVipPackages(
        @Query("vipid") vipId: Int,
        @Query("side") side: Int
    ): Call<BaseResponse<List<SystemPackage>>>
    
    /**
     * 提交订单
     */
    // @POST("/api/app/rent/submit")
    // fun submitOrder(@Body request: OrderSubmitRequest): Call<BaseResponse<Any>>

      /**
     * 提交订单
     */
    @POST("/api/app/pay/genCode")
    fun submitOrder(@Body request: OrderSubmitRequest): Call<BaseResponse<Any>>
    
    /**
     * 通知支付成功
     */
    @POST("api/order/notify_payment")
    fun notifyPaymentSuccess(@Query("order_id") orderId: String): Call<BaseResponse<Any>>
    
    /**
     * 续费云手机
     */
    @POST("/api/app/rent/RenewRentPhone")
    fun renewRentPhone(@Body request: RenewRequest): Call<BaseResponse<Any>>
    
    /**
     * 获取云盘文件列表
     */
    @GET("/api/cloud/get")
    fun getCloudFiles(
        @Query("user_id") userId: Int,
        @Query("type_path") typePath: String
    ): Call<BaseResponse<List<CloudFile>>>

    /**
     * 创建设备会话
     */
    @POST
    @retrofit2.http.Headers("Content-Type: application/json")
    fun createSession(
        @retrofit2.http.Url url: String,
        @Body request: SessionRequest
    ): Call<BaseResponse<SessionResponse>>

    /**
     * 获取码率配置
     */
    @GET("/api/bitrate")
    fun getBitrateConfig(): Call<BaseResponse<List<BitrateConfig>>>
    
    /**
     * 获取设备截图 - 无adb设备 (adb_type=true)
     */
    @POST
    @retrofit2.http.Headers("Content-Type: application/json")
    fun getDeviceTaskScreen(
        @retrofit2.http.Url url: String,
        @Body requestBody: Map<String, String>
    ): Call<BaseResponse<Map<String, Any>>>
    
    /**
     * 获取设备截图 - adb设备 (adb_type=false)
     */
    @GET
    fun getDeviceFrame(
        @retrofit2.http.Url url: String
    ): Call<BaseResponse<Map<String, Any>>>

    /**
     * 保存流量统计信息
     */
    @POST("/api/dashboard/traffic/save")
    fun saveTrafficStats(@Body request: TrafficStatsRequest): Call<BaseResponse<Any>>

    /**
     * 检查用户单点登录状态
     */
    @GET("/api/app/clientUser/userSingleLogin")
    fun checkUserSingleLogin(): Call<BaseResponse<Any>>
    
    /**
     * 修改设备名称
     */
    @GET("/api/app/rent/updateNickName")
    fun updateDeviceName(
        @Query("rent_id") rentId: Int,
        @Query("nick_name") nickName: String
    ): Call<BaseResponse<Any>>
    
    /**
     * 获取公告配置
     */
    @GET("/api/config/1")
    fun getAnnouncementConfig(): Call<BaseResponse<AnnouncementConfigResponse>>
    
    /**
     * 获取节点服务信息
     */
    @GET("/api/node/get")
    fun getNodeServices(): Call<BaseResponse<Map<String, NodeService>>>
    
    /**
     * 执行Shell命令（重启设备等）
     */
    @POST("/api/cmd/shell")
    @retrofit2.http.Headers("Content-Type: application/json")
    fun executeShellCommandWithBody(@Body requestBody: RequestBody): Call<BaseResponse<Any>>
    
    /**
     * 转移设备
     */
    @POST("/api/app/clientUser/translatePhone")
    @retrofit2.http.Headers("Content-Type: application/json")
    fun transferDevices(@Body requestBody: RequestBody): Call<BaseResponse<Any>>

    /**
     * 推送文件到设备
     */
    @POST
    @retrofit2.http.Headers("Content-Type: application/json")
    fun pushFileToDevices(
        @Url url: String,
        @Body requestBody: Map<String, Any>
    ): Call<BaseResponse<Any>>
} 