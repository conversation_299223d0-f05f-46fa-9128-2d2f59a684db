package com.example.shiqianyun

import android.app.AlertDialog
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.ConsumptionRecordResponse
import com.example.shiqianyun.network.model.ConsumptionRecord
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import org.json.JSONObject
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.lang.Exception
import android.graphics.Color
import com.example.shiqianyun.network.model.ConsumptionRecordRequest
import android.widget.LinearLayout
import android.widget.ScrollView
import android.widget.RadioGroup
import android.widget.RadioButton
import android.app.Activity
import android.content.Intent
import com.example.shiqianyun.network.model.DeviceInfo

class ActivationCodeActivity : AppCompatActivity() {
    
    private lateinit var etActivationCodes: EditText
    private lateinit var btnActivate: Button
    private lateinit var tvRecordCount: TextView
    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: ActivationRecordAdapter
    private val recordsList = mutableListOf<ConsumptionRecord>()
    
    private lateinit var progressRecyclerView: RecyclerView
    private lateinit var progressAdapter: ActivationProgressAdapter
    private val progressList = mutableListOf<ActivationProgress>()
    
    private lateinit var paginationLayout: LinearLayout
    private lateinit var btnPrevPage: TextView
    private lateinit var btnNextPage: TextView
    private lateinit var pageNumbersLayout: LinearLayout
    private lateinit var mainScrollView: ScrollView
    
    // 分页参数
    private var currentPage = 1
    private var pageSize = 10
    private var totalItems = 0
    private var totalPages = 0
    
    // 记录滚动位置
    private var savedScrollPosition = 0
    
    // 续费相关控件
    private lateinit var rgDeviceType: RadioGroup
    private lateinit var rbNewDevice: RadioButton
    private lateinit var rbRenewDevice: RadioButton
    private lateinit var llSelectRenewDevice: LinearLayout
    private lateinit var tvSelectedDevice: TextView
    
    // 续费相关数据
    private var selectedDeviceForRenew: DeviceInfo? = null
    private var isRenewMode = false
    
    data class ActivationProgress(
        val code: String,
        var status: String = "",
        var isSuccess: Boolean = false
    )
    
    // 激活进度对话框
    private lateinit var progressDialog: AlertDialog
    private lateinit var tvProgress: TextView
    private lateinit var progressBar: ProgressBar
    private lateinit var tvSuccessCount: TextView
    private lateinit var tvFailCount: TextView
    private lateinit var tvErrorInfo: TextView
    
    companion object {
        private const val TAG = "ActivationCodeActivity"
        private const val MAX_VISIBLE_PAGES = 5 // 最多显示5个页码按钮
        private const val REQUEST_CODE_SELECT_DEVICE = 1001
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val layoutId = resources.getIdentifier("activity_activation_code", "layout", packageName)
        setContentView(layoutId)
        
        // 初始化视图
        initViews()
        
        // 设置点击事件
        setupClickListeners()
        
        // 加载激活码记录
        loadActivationRecords()
        
        // 初始化进度对话框
        initProgressDialog()
    }
    
    private fun initViews() {
        // 返回按钮
        findViewById<ImageView>(resources.getIdentifier("iv_back", "id", packageName)).setOnClickListener {
            finish()
        }
        
        // 主滚动视图
        mainScrollView = findViewById(resources.getIdentifier("main_scroll_view", "id", packageName))
        
        // 激活码输入框
        etActivationCodes = findViewById(resources.getIdentifier("et_activation_codes", "id", packageName))
        
        // 激活按钮
        btnActivate = findViewById(resources.getIdentifier("btn_activate", "id", packageName))
        
        // 记录数量
        tvRecordCount = findViewById(resources.getIdentifier("tv_record_count", "id", packageName))
        
        // 初始化RecyclerView
        recyclerView = findViewById(resources.getIdentifier("rv_activation_records", "id", packageName))
        recyclerView.layoutManager = LinearLayoutManager(this)
        adapter = ActivationRecordAdapter(this, recordsList)
        recyclerView.adapter = adapter
        
        // 确保RecyclerView可以独立滚动
        recyclerView.isNestedScrollingEnabled = true
        // 设置滚动触摸事件拦截，确保RecyclerView能自行处理滚动
        recyclerView.setOnTouchListener { v, event ->
            // 确保RecyclerView可以拦截并处理自己的触摸事件
            v.parent.requestDisallowInterceptTouchEvent(true)
            false
        }
        
        // 初始化空状态视图
        findViewById<TextView>(resources.getIdentifier("tv_empty_state", "id", packageName))?.visibility = View.GONE
        
        // 初始化分页控件
        paginationLayout = findViewById(resources.getIdentifier("pagination_layout", "id", packageName))
        btnPrevPage = findViewById(resources.getIdentifier("btn_prev_page", "id", packageName))
        btnNextPage = findViewById(resources.getIdentifier("btn_next_page", "id", packageName))
        pageNumbersLayout = findViewById(resources.getIdentifier("page_numbers_layout", "id", packageName))
        
        // 设置分页按钮点击事件
        btnPrevPage.setOnClickListener {
            if (currentPage > 1) {
                // 保存当前滚动位置
                savedScrollPosition = mainScrollView.scrollY
                
                currentPage--
                loadActivationRecords()
            }
        }
        
        btnNextPage.setOnClickListener {
            if (currentPage < totalPages) {
                // 保存当前滚动位置
                savedScrollPosition = mainScrollView.scrollY
                
                currentPage++
                loadActivationRecords()
            }
        }
        
        // 初始化续费相关控件
        initRenewControls()
    }
    
    private fun initRenewControls() {
        // 初始化续费相关控件
        rgDeviceType = findViewById(resources.getIdentifier("rg_device_type", "id", packageName))
        rbNewDevice = findViewById(resources.getIdentifier("rb_new_device", "id", packageName))
        rbRenewDevice = findViewById(resources.getIdentifier("rb_renew_device", "id", packageName))
        llSelectRenewDevice = findViewById(resources.getIdentifier("ll_select_renew_device", "id", packageName))
        tvSelectedDevice = findViewById(resources.getIdentifier("tv_selected_device", "id", packageName))
        
        // 设置RadioGroup监听
        rgDeviceType.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                rbNewDevice.id -> {
                    isRenewMode = false
                    llSelectRenewDevice.visibility = View.GONE
                    selectedDeviceForRenew = null
                    updateActivateButtonText()
                }
                rbRenewDevice.id -> {
                    isRenewMode = true
                    llSelectRenewDevice.visibility = View.VISIBLE
                    updateActivateButtonText()
                }
            }
        }
        
        // 设置选择续费设备点击事件
        llSelectRenewDevice.setOnClickListener {
            val intent = Intent(this, DeviceSelectActivity::class.java)
            startActivityForResult(intent, REQUEST_CODE_SELECT_DEVICE)
        }
        
        // 初始化按钮文本
        updateActivateButtonText()
    }
    
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        
        if (requestCode == REQUEST_CODE_SELECT_DEVICE && resultCode == Activity.RESULT_OK) {
            val selectedDevices = data?.getParcelableArrayListExtra<DeviceInfo>(DeviceSelectActivity.RESULT_KEY_SELECTED_DEVICES)
            if (!selectedDevices.isNullOrEmpty()) {
                selectedDeviceForRenew = selectedDevices[0]
                updateSelectedDeviceDisplay()
            }
        }
    }
    
    private fun updateSelectedDeviceDisplay() {
        selectedDeviceForRenew?.let { device ->
            tvSelectedDevice.text = "${device.nick_name} (ID: ${device.phone_id})"
            tvSelectedDevice.setTextColor(resources.getColor(android.R.color.black))
        }
    }
    
    private fun updateActivateButtonText() {
        val buttonText = if (isRenewMode) "续费设备" else "激活设备"
        btnActivate.text = buttonText
    }
    
    private fun initProgressDialog() {
        // 创建对话框视图
        val dialogViewId = resources.getIdentifier("dialog_activation_progress", "layout", packageName)
        val dialogView = LayoutInflater.from(this).inflate(dialogViewId, null)
        
        // 初始化RecyclerView
        progressRecyclerView = dialogView.findViewById(resources.getIdentifier("rv_activation_progress", "id", packageName))
        progressRecyclerView.layoutManager = LinearLayoutManager(this)
        progressAdapter = ActivationProgressAdapter(progressList)
        progressRecyclerView.adapter = progressAdapter
        
        // 获取其他视图引用
        tvSuccessCount = dialogView.findViewById(resources.getIdentifier("tv_success_count", "id", packageName))
        tvFailCount = dialogView.findViewById(resources.getIdentifier("tv_fail_count", "id", packageName))
        val tvTotalCount = dialogView.findViewById<TextView>(resources.getIdentifier("tv_total_count", "id", packageName))
        val btnConfirm = dialogView.findViewById<Button>(resources.getIdentifier("btn_confirm", "id", packageName))
        
        // 创建对话框
        progressDialog = AlertDialog.Builder(this)
            .setView(dialogView)
            .setCancelable(false)
            .create()
            
        // 确认按钮点击事件
        btnConfirm.setOnClickListener {
            progressDialog.dismiss()
            if (progressList.any { it.isSuccess }) {
                etActivationCodes.setText("")
                loadActivationRecords()
            }
        }
    }
    
    private fun setupClickListeners() {
        // 激活按钮点击事件
        btnActivate.setOnClickListener {
            val codesText = etActivationCodes.text.toString().trim()
            if (codesText.isEmpty()) {
                Toast.makeText(this, "请输入激活码", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            // 分割多个激活码
            val codes = codesText.split("\n").filter { it.isNotEmpty() }
            if (codes.isEmpty()) {
                Toast.makeText(this, "请输入有效的激活码", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            // 续费模式验证
            if (isRenewMode) {
                if (selectedDeviceForRenew == null) {
                    Toast.makeText(this, "请选择要续费的设备", Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }
                Log.d(TAG, "续费模式验证通过，选中设备: ${selectedDeviceForRenew!!.nick_name}")
            } else {
                Log.d(TAG, "新增设备模式")
            }

            // 先显示弹窗
            showActivationDialog(codes)
        }
    }

    private fun showActivationDialog(codes: List<String>) {
        // 初始化进度列表
        progressList.clear()
        progressList.addAll(codes.map { ActivationProgress(it) })
        
        // 更新UI
        val tvTotalCount = progressDialog.findViewById<TextView>(resources.getIdentifier("tv_total_count", "id", packageName))
        tvTotalCount?.text = "总数: ${codes.size}"
        tvSuccessCount.text = "成功: 0"
        tvFailCount.text = "失败: 0"
        progressAdapter.notifyDataSetChanged()
        
        // 显示进度对话框
        progressDialog.show()

        // 禁用激活按钮
        btnActivate.isEnabled = false
        val processingText = if (isRenewMode) "续费中..." else "激活中..."
        btnActivate.text = processingText

        // 开始激活流程
        startActivation(codes)
    }

    private fun startActivation(codes: List<String>) {
        // 在后台线程中执行激活
        Thread {
            var successCount = 0
            var failCount = 0
            val userId = getUserId()

            for ((index, code) in codes.withIndex()) {
                try {
                    // 创建JSON请求体
                    val jsonObject = JSONObject().apply {
                        put("user_id", userId)
                        put("activate_code", code)
                        put("tag_side", 4)
                        
                        // 如果是续费模式，添加续费设备ID
                        if (isRenewMode && selectedDeviceForRenew != null) {
                            put("renew_rent_id", selectedDeviceForRenew!!.id)
                            Log.d(TAG, "续费模式: 设备ID=${selectedDeviceForRenew!!.id}, 设备名称=${selectedDeviceForRenew!!.nick_name}")
                        } else {
                            Log.d(TAG, "新增设备模式")
                        }
                    }
                    val requestBody = jsonObject.toString().toRequestBody("application/json".toMediaType())
                    
                    // 打印请求体内容
                    Log.d(TAG, "请求体内容: ${jsonObject.toString()}")
                    
                    // 根据模式选择不同的API接口
                    val response = if (isRenewMode && selectedDeviceForRenew != null) {
                        // 续费模式使用续费接口
                        val url = "${RetrofitManager.BASE_URL}/api/app/activateCode/renew"
                        Log.d(TAG, "调用续费接口: $url")
                        RetrofitManager.apiService.renewCodeWithBody(url, requestBody).execute()
                    } else {
                        // 新增设备模式使用激活接口
                        val url = "${RetrofitManager.BASE_URL}/api/app/activateCode/activate"
                        Log.d(TAG, "调用激活接口: $url")
                        RetrofitManager.apiService.activateCodeWithBody(url, requestBody).execute()
                    }

                    runOnUiThread {
                        try {
                            if (response.isSuccessful) {
                                val baseResponse = response.body()
                                if (baseResponse != null && baseResponse.code == 200) {
                                    successCount++
                                    val statusText = if (isRenewMode) "续费成功" else "激活成功"
                                    progressList[index].apply {
                                        status = statusText
                                        isSuccess = true
                                    }
                                } else {
                                    failCount++
                                    val errorMsg = baseResponse?.msg ?: "未知错误"
                                    val failPrefix = if (isRenewMode) "续费失败" else "激活失败"
                                    progressList[index].status = when {
                                        errorMsg.contains("activateCode is used") -> "$failPrefix: 激活码已使用"
                                        errorMsg.contains("activateCode is not found") -> "$failPrefix: 激活码错误"
                                        else -> "$failPrefix: $errorMsg"
                                    }
                                }
                            } else {
                                failCount++
                                val failPrefix = if (isRenewMode) "续费失败" else "激活失败"
                                var errorMsg = "$failPrefix: 请求失败(${response.code()})"
                                try {
                                    val errorBody = response.errorBody()?.string()
                                    if (errorBody != null) {
                                        errorMsg = when {
                                            errorBody.contains("activateCode is used") -> "$failPrefix: 激活码已使用"
                                            errorBody.contains("activateCode is not found") -> "$failPrefix: 激活码错误"
                                            else -> "$failPrefix: $errorBody"
                                        }
                                    }
                                } catch (e: Exception) {
                                    // 忽略错误体解析错误
                                }
                                progressList[index].status = errorMsg
                            }
                        } catch (e: Exception) {
                            failCount++
                            progressList[index].status = "激活失败: ${e.message ?: "处理异常"}"
                        }

                        // 更新UI
                        updateProgressUI(successCount, failCount)
                        progressAdapter.notifyDataSetChanged()

                        // 检查是否完成
                        if (successCount + failCount == codes.size) {
                            btnActivate.isEnabled = true
                            val buttonText = if (isRenewMode) "续费设备" else "激活设备"
                            btnActivate.text = buttonText
                            progressDialog.findViewById<Button>(resources.getIdentifier("btn_confirm", "id", packageName))?.visibility = View.VISIBLE
                        }
                    }

                } catch (e: Exception) {
                    runOnUiThread {
                        failCount++
                        val failPrefix = if (isRenewMode) "续费失败" else "激活失败"
                        progressList[index].status = "$failPrefix: ${e.message ?: "网络错误"}"
                        updateProgressUI(successCount, failCount)
                        progressAdapter.notifyDataSetChanged()

                        // 检查是否完成
                        if (successCount + failCount == codes.size) {
                            btnActivate.isEnabled = true
                            val buttonText = if (isRenewMode) "续费设备" else "激活设备"
                            btnActivate.text = buttonText
                            progressDialog.findViewById<Button>(resources.getIdentifier("btn_confirm", "id", packageName))?.visibility = View.VISIBLE
                        }
                    }
                }

                // 添加延迟，避免请求过快
                try {
                    Thread.sleep(300)
                } catch (e: InterruptedException) {
                    // 忽略中断异常
                }
            }
        }.start()
    }
    
    private fun updateProgressUI(successCount: Int, failCount: Int) {
        runOnUiThread {
            tvSuccessCount.text = "成功: $successCount"
            tvFailCount.text = "失败: $failCount"
            progressAdapter.notifyDataSetChanged()
        }
    }
    
    private fun checkActivationComplete(total: Int, success: Int, fail: Int) {
        if (success + fail == total) {
            // 所有激活码处理完毕
            runOnUiThread {
                btnActivate.isEnabled = true
                btnActivate.text = "激活设备"
                progressDialog.findViewById<Button>(resources.getIdentifier("btn_confirm", "id", packageName))?.visibility = View.VISIBLE
            }
        }
    }
    
    private fun loadActivationRecords() {
        val userId = getUserId()
        
        // 显示加载中
        findViewById<ProgressBar>(resources.getIdentifier("progress_bar", "id", packageName))?.visibility = View.VISIBLE
        recyclerView.visibility = View.GONE
        
        // 并行获取两种类型的记录：激活码新增和激活码续费
        loadActivationRecordsWithTypes(userId)
    }
    
    private fun loadActivationRecordsWithTypes(userId: Int) {
        var activateCodeRecords: List<ConsumptionRecord>? = null
        var renewCodeRecords: List<ConsumptionRecord>? = null
        var activateCodeTotal = 0
        var renewCodeTotal = 0
        var completedRequests = 0
        
        val onRequestComplete = {
            completedRequests++
            if (completedRequests == 2) {
                // 两个请求都完成，合并结果
                val allRecords = mutableListOf<ConsumptionRecord>()
                
                activateCodeRecords?.let { allRecords.addAll(it) }
                renewCodeRecords?.let { allRecords.addAll(it) }
                
                // 按创建时间降序排序
                allRecords.sortByDescending { it.createTime }
                
                Log.d(TAG, "合并记录完成: 总共${allRecords.size}条记录 (新增:${activateCodeRecords?.size ?: 0}, 续费:${renewCodeRecords?.size ?: 0})")
                
                // 计算总记录数
                val totalRecords = activateCodeTotal + renewCodeTotal
                
                // 应用分页逻辑
                val startIndex = (currentPage - 1) * pageSize
                val endIndex = minOf(startIndex + pageSize, allRecords.size)
                val pagedRecords = if (startIndex < allRecords.size) {
                    allRecords.subList(startIndex, endIndex)
                } else {
                    emptyList()
                }
                
                // 更新UI
                recordsList.clear()
                recordsList.addAll(pagedRecords)
                
                // 更新分页信息
                updatePagination(currentPage, pageSize, totalRecords)
                
                adapter.notifyDataSetChanged()
                
                // 更新记录数量
                tvRecordCount.text = "共${totalRecords}条记录"
                
                // 隐藏加载中
                findViewById<ProgressBar>(resources.getIdentifier("progress_bar", "id", packageName))?.visibility = View.GONE
                        
                        // 如果有记录，隐藏空状态
                        if (recordsList.isNotEmpty()) {
                            recyclerView.visibility = View.VISIBLE
                            findViewById<TextView>(resources.getIdentifier("tv_empty_state", "id", packageName))?.visibility = View.GONE
                            
                            // 恢复滚动位置
                            restoreScrollPosition()
                            
                            // 强制刷新RecyclerView布局以确保滚动正常工作
                            recyclerView.post {
                                recyclerView.invalidate()
                                recyclerView.requestLayout()
                            }
                        } else {
                            showEmptyState()
                        }
            }
        }
        
        // 请求1: 获取激活码新增记录
        val activateRequest = ConsumptionRecordRequest(
            userId = userId,
            payWay = "activateCode",
            page = 1,
            pageSize = 1000 // 使用大页面获取所有记录
        )
        
        RetrofitManager.apiService.getUserConsumptionRecords(activateRequest).enqueue(object : Callback<ConsumptionRecordResponse> {
            override fun onResponse(call: Call<ConsumptionRecordResponse>, response: Response<ConsumptionRecordResponse>) {
                if (response.isSuccessful) {
                    val recordResponse = response.body()
                    if (recordResponse != null && recordResponse.code == 200) {
                        activateCodeRecords = recordResponse.data.list ?: emptyList()
                        activateCodeTotal = recordResponse.data.total
                        Log.d(TAG, "获取到激活码新增记录: ${activateCodeRecords?.size}条")
                    } else {
                        activateCodeRecords = emptyList()
                        Log.e(TAG, "获取激活码新增记录失败: ${recordResponse?.msg}")
                    }
                } else {
                    activateCodeRecords = emptyList()
                    Log.e(TAG, "获取激活码新增记录失败: ${response.code()}")
                }
                onRequestComplete()
            }
            
            override fun onFailure(call: Call<ConsumptionRecordResponse>, t: Throwable) {
                activateCodeRecords = emptyList()
                Log.e(TAG, "获取激活码新增记录失败", t)
                onRequestComplete()
            }
        })
        
        // 请求2: 获取激活码续费记录
        val renewRequest = ConsumptionRecordRequest(
            userId = userId,
            payWay = "activateRenewPay",
            page = 1,
            pageSize = 1000 // 使用大页面获取所有记录
        )
        
        RetrofitManager.apiService.getUserConsumptionRecords(renewRequest).enqueue(object : Callback<ConsumptionRecordResponse> {
            override fun onResponse(call: Call<ConsumptionRecordResponse>, response: Response<ConsumptionRecordResponse>) {
                if (response.isSuccessful) {
                    val recordResponse = response.body()
                    if (recordResponse != null && recordResponse.code == 200) {
                        renewCodeRecords = recordResponse.data.list ?: emptyList()
                        renewCodeTotal = recordResponse.data.total
                        Log.d(TAG, "获取到激活码续费记录: ${renewCodeRecords?.size}条")
                    } else {
                        renewCodeRecords = emptyList()
                        Log.e(TAG, "获取激活码续费记录失败: ${recordResponse?.msg}")
                    }
                } else {
                    renewCodeRecords = emptyList()
                    Log.e(TAG, "获取激活码续费记录失败: ${response.code()}")
                }
                onRequestComplete()
            }
            
            override fun onFailure(call: Call<ConsumptionRecordResponse>, t: Throwable) {
                renewCodeRecords = emptyList()
                Log.e(TAG, "获取激活码续费记录失败", t)
                onRequestComplete()
            }
        })
    }
    
    private fun updatePagination(page: Int, pageSize: Int, total: Int) {
        currentPage = page
        this.pageSize = pageSize
        totalItems = total
        totalPages = if (total == 0) 1 else (total + pageSize - 1) / pageSize // 计算总页数，向上取整
        
        // 更新按钮状态
        btnPrevPage.isEnabled = currentPage > 1
        btnNextPage.isEnabled = currentPage < totalPages
        
        // 设置按钮透明度来表示可用状态
        btnPrevPage.alpha = if (currentPage > 1) 1.0f else 0.5f
        btnNextPage.alpha = if (currentPage < totalPages) 1.0f else 0.5f
        
        // 清除现有的页码按钮
        pageNumbersLayout.removeAllViews()
        
        // 计算要显示的页码范围
        val pageRange = calculateVisiblePageRange(currentPage, totalPages)
        
        // 创建并添加页码按钮
        for (i in pageRange.first..pageRange.second) {
            val pageButton = createPageButton(i)
            pageNumbersLayout.addView(pageButton)
        }
    }
    
    private fun calculateVisiblePageRange(currentPage: Int, totalPages: Int): Pair<Int, Int> {
        // 如果总页数小于等于最大显示页数，则全部显示
        if (totalPages <= MAX_VISIBLE_PAGES) {
            return Pair(1, totalPages)
        }
        
        // 当前页在前半部分
        if (currentPage <= MAX_VISIBLE_PAGES / 2 + 1) {
            return Pair(1, MAX_VISIBLE_PAGES)
        }
        
        // 当前页在后半部分
        if (currentPage >= totalPages - MAX_VISIBLE_PAGES / 2) {
            return Pair(totalPages - MAX_VISIBLE_PAGES + 1, totalPages)
        }
        
        // 当前页在中间
        val start = currentPage - MAX_VISIBLE_PAGES / 2
        val end = currentPage + MAX_VISIBLE_PAGES / 2
        return Pair(start, end)
    }
    
    private fun createPageButton(pageNum: Int): TextView {
        val pageButton = TextView(this)
        
        // 创建无缝的按钮
        val params = LinearLayout.LayoutParams(80, ViewGroup.LayoutParams.MATCH_PARENT)
        // 添加1px的负边距，确保按钮之间无缝连接
        if (pageNum > 0) {
            params.marginStart = -1
        }
        pageButton.layoutParams = params
        
        pageButton.text = pageNum.toString()
        pageButton.textSize = 18f
        pageButton.gravity = android.view.Gravity.CENTER
        pageButton.setPadding(0, 0, 0, 0)
        
        // 设置当前页高亮
        if (pageNum == currentPage) {
            pageButton.setTextColor(android.graphics.Color.WHITE)
            pageButton.setBackgroundColor(android.graphics.Color.parseColor("#007BFF"))
        } else {
            pageButton.setTextColor(android.graphics.Color.parseColor("#007BFF"))
            pageButton.setBackgroundColor(android.graphics.Color.parseColor("#F2F2F2"))
        }
        
        // 设置点击事件
        pageButton.setOnClickListener {
            if (pageNum != currentPage) {
                // 保存当前滚动位置
                savedScrollPosition = mainScrollView.scrollY
                
                currentPage = pageNum
                loadActivationRecords()
            }
        }
        
        return pageButton
    }
    
    private fun showEmptyState() {
        // 显示空状态视图
        recyclerView.visibility = View.GONE
        findViewById<TextView>(resources.getIdentifier("tv_empty_state", "id", packageName))?.visibility = View.VISIBLE
        
        // 隐藏分页控件
        paginationLayout.visibility = View.GONE
    }
    
    /**
     * 获取用户ID
     */
    private fun getUserId(): Int {
        val sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        return sharedPreferences.getInt("user_id", 0)
    }

    // 恢复滚动位置的方法
    private fun restoreScrollPosition() {
        mainScrollView.post {
            mainScrollView.scrollTo(0, savedScrollPosition)
        }
    }
}

class ActivationRecordAdapter(
    private val context: Context,
    private val records: List<ConsumptionRecord>
) : RecyclerView.Adapter<ActivationRecordAdapter.ViewHolder>() {
    
    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvId: TextView = view.findViewById(view.resources.getIdentifier("tv_record_id", "id", view.context.packageName))
        val tvType: TextView = view.findViewById(view.resources.getIdentifier("tv_record_type", "id", view.context.packageName))
        val tvTime: TextView = view.findViewById(view.resources.getIdentifier("tv_record_time", "id", view.context.packageName))
        val tvCode: TextView = view.findViewById(view.resources.getIdentifier("tv_activation_code", "id", view.context.packageName))
        val tvDeviceId: TextView = view.findViewById(view.resources.getIdentifier("tv_device_id", "id", view.context.packageName))
        val tvDuration: TextView = view.findViewById(view.resources.getIdentifier("tv_duration", "id", view.context.packageName))
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layoutId = parent.resources.getIdentifier("item_activation_record", "layout", parent.context.packageName)
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        return ViewHolder(view)
    }
    
    override fun getItemCount() = records.size
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val record = records[position]
        
        // 设置ID
        holder.tvId.text = "#${record.id}"
        
        // 设置类型标签 - 使用更准确的判断方式
        val isRenew = record.payWay == "activateRenewPay" || record.isRenew == true
        holder.tvType.text = if (isRenew) "续费" else "新增"
        
        // 添加调试日志
        Log.d("ActivationRecordAdapter", "记录#${record.id}: payWay=${record.payWay}, isRenew=${record.isRenew}, 显示类型=${if (isRenew) "续费" else "新增"}")
        
        val bgTagRenew = holder.itemView.resources.getIdentifier("bg_tag_renew", "drawable", holder.itemView.context.packageName)
        val bgTagNew = holder.itemView.resources.getIdentifier("bg_tag_new", "drawable", holder.itemView.context.packageName)
        
        holder.tvType.background = ContextCompat.getDrawable(
            context,
            if (isRenew) bgTagRenew else bgTagNew
        )
        
        // 设置文字颜色为白色，因为背景是纯色
        holder.tvType.setTextColor(ContextCompat.getColor(context, android.R.color.white))
        
        // 设置时间
        holder.tvTime.text = record.createTime
        
        // 设置激活码
        val activateCode = record.activateCodes?.firstOrNull()?.uuid ?: "无激活码信息"
        holder.tvCode.text = activateCode
        
        // 设置设备ID
        val deviceInfo = when {
            // 优先从devices数组获取设备名称
            !record.devices.isNullOrEmpty() -> {
                record.devices.firstOrNull()?.deviceDesignation ?: "设备ID: ${record.devices.firstOrNull()?.id}"
            }
            // 如果是续费记录，从device_id_list获取
            !record.deviceIdList.isNullOrEmpty() -> {
                "设备ID: ${record.deviceIdList.first()}"
            }
            // 备用方案
            record.deviceDesignation != null -> record.deviceDesignation
            else -> "无设备信息"
        }
        holder.tvDeviceId.text = deviceInfo
        
        // 设置时长
        val timeType = record.activateCodes?.firstOrNull()?.timeType ?: 0
        val buyDay = record.activateCodes?.firstOrNull()?.buyDay ?: record.buyDay
        val timeUnit = if (timeType == 0) "小时" else "天"
        holder.tvDuration.text = "${buyDay}${timeUnit}"
    }
} 

class ActivationProgressAdapter(
    private val items: List<ActivationCodeActivity.ActivationProgress>
) : RecyclerView.Adapter<ActivationProgressAdapter.ViewHolder>() {
    
    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        val tvCode: TextView = view.findViewById(view.resources.getIdentifier("tv_activation_code", "id", view.context.packageName))
        val tvStatus: TextView = view.findViewById(view.resources.getIdentifier("tv_status", "id", view.context.packageName))
    }
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val layoutId = parent.resources.getIdentifier("item_activation_progress", "layout", parent.context.packageName)
        val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
        return ViewHolder(view)
    }
    
    override fun getItemCount() = items.size
    
    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val item = items[position]
        holder.tvCode.text = item.code
        holder.tvStatus.text = item.status
        holder.tvStatus.setTextColor(
            if (item.isSuccess) Color.parseColor("#4CAF50")
            else Color.parseColor("#F44336")
        )
    }
} 