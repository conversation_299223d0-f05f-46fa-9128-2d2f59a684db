<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F8F8"
    tools:context=".DeviceListActivity">

    <!-- 顶部状态栏 -->
    <LinearLayout
        android:id="@+id/top_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:background="#FFFFFF"
        android:gravity="center_vertical"
        android:paddingHorizontal="16dp"
        android:elevation="2dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/title_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="史前云手机"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold"/>

        <ImageButton
            android:id="@+id/btn_refresh"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_refresh"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="刷新"/>

        <ImageButton
            android:id="@+id/btn_batch_operation"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@drawable/ic_more"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="批量操作"/>
    </LinearLayout>

    <!-- 筛选栏 -->
    <LinearLayout
        android:id="@+id/filter_bar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:orientation="horizontal"
        android:background="#FFFFFF"
        android:gravity="center_vertical"
        android:elevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/top_bar">

        <!-- 显示模式 -->
        <TextView
            android:id="@+id/tv_display_mode"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="预览模式"
            android:textColor="#333333"
            android:textSize="12sp"
            android:gravity="center"
            android:drawableEnd="@drawable/ic_arrow_drop_down"
            android:padding="8dp"/>

        <View
            android:layout_width="1dp"
            android:layout_height="24dp"
            android:background="#E0E0E0"/>

        <!-- 列数模式 -->
        <TextView
            android:id="@+id/tv_column_mode"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="2列图"
            android:textColor="#333333"
            android:textSize="12sp"
            android:gravity="center"
            android:drawableEnd="@drawable/ic_arrow_drop_down"
            android:padding="8dp"/>

        <View
            android:layout_width="1dp"
            android:layout_height="24dp"
            android:background="#E0E0E0"/>

        <!-- 分组模式 -->
        <TextView
            android:id="@+id/tv_group_mode"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="全部分组"
            android:textColor="#333333"
            android:textSize="12sp"
            android:gravity="center"
            android:drawableEnd="@drawable/ic_arrow_drop_down"
            android:padding="8dp"/>

        <!-- 添加三条杠图标按钮 -->
        <ImageButton
            android:id="@+id/btn_group_menu"
            android:layout_width="48dp"
            android:layout_height="match_parent"
            android:src="@drawable/ic_menu"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="分组菜单"/>
    </LinearLayout>

    <!-- 公告区域 -->
    <LinearLayout
        android:id="@+id/announcement_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#FFF8E1"
        android:padding="12dp"
        android:gravity="center_vertical"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/filter_bar">

        <ImageView
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_announcement"
            android:layout_marginEnd="8dp"
            app:tint="#FF9800"
            android:contentDescription="公告"/>

        <HorizontalScrollView
            android:id="@+id/scroll_view_announcement"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:scrollbars="none"
            android:fadeScrollbars="false">

            <TextView
                android:id="@+id/tv_announcement"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#E65100"
                android:textSize="14sp"
                android:text="这里显示公告内容"
                android:singleLine="true"
                android:ellipsize="none"/>
        </HorizontalScrollView>

        <ImageView
            android:id="@+id/btn_close_announcement"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:src="@drawable/ic_close"
            android:layout_marginStart="8dp"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            app:tint="#FF9800"
            android:contentDescription="关闭公告"/>
    </LinearLayout>

    <!-- 设备列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:clipToPadding="false"
        android:padding="8dp"
        app:layout_constraintTop_toBottomOf="@id/announcement_layout"
        app:layout_constraintBottom_toTopOf="@id/bottom_nav"/>

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

    <!-- 底部导航栏 -->
    <LinearLayout
        android:id="@+id/bottom_nav"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:background="#FFFFFF"
        android:elevation="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:id="@+id/nav_home"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_home"
                app:tint="#529cff"
                android:contentDescription="云手机"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="云手机"
                android:textColor="#529cff"
                android:textSize="12sp"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_discovery"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_shopping_cart"
                app:tint="#666666"
                android:contentDescription="购买"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="购买"
                android:textColor="#666666"
                android:textSize="12sp"/>
        </LinearLayout>

        <LinearLayout
            android:id="@+id/nav_profile"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_person"
                app:tint="#666666"
                android:contentDescription="我的"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="我的"
                android:textColor="#666666"
                android:textSize="12sp"/>
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 