package com.example.shiqianyun.decoder.parser

import android.util.Log

/**
 * SPS (Sequence Parameter Set) 解析器
 * 负责解析H.264 SPS数据，提取视频参数信息
 */
class SPSParser {
    
    companion object {
        private const val TAG = "SPSParser"
    }
    
    data class SPSInfo(
        val width: Int,
        val height: Int,
        val profileIdc: Int,
        val levelIdc: Int,
        val constraintFlags: Int,
        val cropLeft: Int = 0,
        val cropRight: Int = 0,
        val cropTop: Int = 0,
        val cropBottom: Int = 0
    ) {
        /**
         * 检查分辨率是否与另一个SPS不同
         */
        fun hasResolutionChanged(other: SPSInfo?): <PERSON><PERSON><PERSON> {
            return other == null || width != other.width || height != other.height
        }
        
        /**
         * 检查屏幕方向是否与另一个SPS不同
         */
        fun hasOrientationChanged(other: SPSInfo?): Boolean {
            return other == null || isLandscape() != other.isLandscape()
        }
        
        /**
         * 判断是否为横屏（宽度大于高度）
         */
        fun isLandscape(): Boolean = width > height
        
        /**
         * 判断是否为竖屏（高度大于等于宽度）
         */
        fun isPortrait(): Boolean = height >= width
        
        /**
         * 获取分辨率描述
         */
        fun getResolutionString(): String = "${width}x${height}"
        
        /**
         * 获取方向描述
         */
        fun getOrientationString(): String = if (isLandscape()) "横屏" else "竖屏"
        
        /**
         * 获取裁剪信息描述
         */
        fun getCropString(): String = "裁剪: left=$cropLeft, right=$cropRight, top=$cropTop, bottom=$cropBottom"
    }
    
    /**
     * 解析SPS数据
     */
    fun parseSPS(sps: ByteArray): SPSInfo {
        return try {
            if (sps.size < 8) {
                Log.w(TAG, "SPS too short: ${sps.size}")
                return getDefaultSPSInfo()
            }

            Log.d(TAG, "Full SPS data: ${sps.joinToString { "%02x".format(it) }}")
            
            // 跳过NAL单元头 (第一个字节)
            val profileIdc = sps[1].toInt() and 0xFF
            val constraintFlags = sps[2].toInt() and 0xFF
            val levelIdc = sps[3].toInt() and 0xFF
            
            Log.d(TAG, "SPS profile_idc: $profileIdc, constraint_flags: $constraintFlags, level_idc: $levelIdc")
            
            // 创建位读取器来解析SPS的变长编码部分
            val bitReader = SPSBitReader(sps, 4) // 从第4个字节开始
            
            // 读取seq_parameter_set_id (ue(v))
            val seqParameterSetId = bitReader.readUE()
            Log.d(TAG, "seq_parameter_set_id: $seqParameterSetId")
            
            // 根据profile判断是否有色度格式信息
            if (profileIdc == 100 || profileIdc == 110 || profileIdc == 122 || profileIdc == 244 || 
                profileIdc == 44 || profileIdc == 83 || profileIdc == 86 || profileIdc == 118 || 
                profileIdc == 128 || profileIdc == 138 || profileIdc == 139 || profileIdc == 134) {
                
                val chromaFormatIdc = bitReader.readUE()
                Log.d(TAG, "chroma_format_idc: $chromaFormatIdc")
                
                if (chromaFormatIdc == 3) {
                    bitReader.readBit() // separate_colour_plane_flag
                }
                
                bitReader.readUE() // bit_depth_luma_minus8
                bitReader.readUE() // bit_depth_chroma_minus8
                bitReader.readBit() // qpprime_y_zero_transform_bypass_flag
                
                val seqScalingMatrixPresentFlag = bitReader.readBit()
                if (seqScalingMatrixPresentFlag == 1) {
                    // 跳过scaling matrix处理，这里简化
                    repeat(if (chromaFormatIdc != 3) 8 else 12) {
                        val seqScalingListPresentFlag = bitReader.readBit()
                        if (seqScalingListPresentFlag == 1) {
                            // 简化：跳过scaling list
                            repeat(if (it < 6) 16 else 64) { bitReader.readBit() }
                        }
                    }
                }
            }
            
            // 读取关键参数
            val log2MaxFrameNumMinus4 = bitReader.readUE()
            val picOrderCntType = bitReader.readUE()
            
            Log.d(TAG, "log2_max_frame_num_minus4: $log2MaxFrameNumMinus4")
            Log.d(TAG, "pic_order_cnt_type: $picOrderCntType")
            
            if (picOrderCntType == 0) {
                bitReader.readUE() // log2_max_pic_order_cnt_lsb_minus4
            } else if (picOrderCntType == 1) {
                bitReader.readBit() // delta_pic_order_always_zero_flag
                bitReader.readSE() // offset_for_non_ref_pic
                bitReader.readSE() // offset_for_top_to_bottom_field
                val numRefFramesInPicOrderCntCycle = bitReader.readUE()
                repeat(numRefFramesInPicOrderCntCycle) {
                    bitReader.readSE() // offset_for_ref_frame
                }
            }
            
            bitReader.readUE() // max_num_ref_frames
            bitReader.readBit() // gaps_in_frame_num_value_allowed_flag
            
            // 读取分辨率信息
            val picWidthInMbsMinus1 = bitReader.readUE()
            val picHeightInMapUnitsMinus1 = bitReader.readUE()
            val frameMbsOnlyFlag = bitReader.readBit()
            
            Log.d(TAG, "pic_width_in_mbs_minus1: $picWidthInMbsMinus1")
            Log.d(TAG, "pic_height_in_map_units_minus1: $picHeightInMapUnitsMinus1")
            Log.d(TAG, "frame_mbs_only_flag: $frameMbsOnlyFlag")
            
            // 检查是否有帧场自适应标志
            var mbAdaptiveFrameFieldFlag = 0
            if (frameMbsOnlyFlag == 0) {
                mbAdaptiveFrameFieldFlag = bitReader.readBit()
                Log.d(TAG, "mb_adaptive_frame_field_flag: $mbAdaptiveFrameFieldFlag")
            }
            
            // 检查直接8x8推断标志
            val direct8x8InferenceFlag = bitReader.readBit()
            Log.d(TAG, "direct_8x8_inference_flag: $direct8x8InferenceFlag")
            
            // 检查帧裁剪标志
            val frameCroppingFlag = bitReader.readBit()
            Log.d(TAG, "frame_cropping_flag: $frameCroppingFlag")
            
            // 读取裁剪参数
            var cropLeft = 0
            var cropRight = 0
            var cropTop = 0
            var cropBottom = 0
            
            if (frameCroppingFlag == 1) {
                cropLeft = bitReader.readUE()
                cropRight = bitReader.readUE()
                cropTop = bitReader.readUE()
                cropBottom = bitReader.readUE()
                Log.d(TAG, "frame_crop: left=$cropLeft, right=$cropRight, top=$cropTop, bottom=$cropBottom")
            }
            
            // 计算实际分辨率（考虑裁剪）
            val mbWidth = picWidthInMbsMinus1 + 1
            val mbHeight = (picHeightInMapUnitsMinus1 + 1) * (if (frameMbsOnlyFlag == 1) 1 else 2)
            
            // 基本分辨率（宏块数 × 16）
            val baseWidth = mbWidth * 16
            val baseHeight = mbHeight * 16
            
            // 应用裁剪
            val width = baseWidth - (cropLeft + cropRight) * 2
            val height = baseHeight - (cropTop + cropBottom) * 2
            
            Log.d(TAG, "Base resolution: ${baseWidth}x${baseHeight}")
            Log.d(TAG, "Cropped resolution: ${width}x${height}")
            
            SPSInfo(width, height, profileIdc, levelIdc, constraintFlags, cropLeft, cropRight, cropTop, cropBottom)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error parsing SPS", e)
            getDefaultSPSInfo()
        }
    }
    
    private fun getDefaultSPSInfo(): SPSInfo {
        return SPSInfo(1920, 1080, 66, 32, 128)
    }
}

/**
 * SPS位读取器类
 */
class SPSBitReader(private val data: ByteArray, startByte: Int) {
    private var byteIndex = startByte
    private var bitIndex = 0
    
    fun readBit(): Int {
        if (byteIndex >= data.size) return 0
        
        val bit = (data[byteIndex].toInt() and 0xFF) shr (7 - bitIndex) and 1
        bitIndex++
        if (bitIndex == 8) {
            bitIndex = 0
            byteIndex++
        }
        return bit
    }
    
    fun readBits(count: Int): Int {
        var result = 0
        repeat(count) {
            result = (result shl 1) or readBit()
        }
        return result
    }
    
    // 读取无符号指数Golomb编码
    fun readUE(): Int {
        var leadingZeros = 0
        while (readBit() == 0) {
            leadingZeros++
            if (leadingZeros > 32) break // 防止无限循环
        }
        if (leadingZeros == 0) return 0
        return readBits(leadingZeros) + (1 shl leadingZeros) - 1
    }
    
    // 读取有符号指数Golomb编码
    fun readSE(): Int {
        val value = readUE()
        return if (value % 2 == 1) (value + 1) / 2 else -(value / 2)
    }
}