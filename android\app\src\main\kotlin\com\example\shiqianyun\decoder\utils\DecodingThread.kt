package com.example.shiqianyun.decoder.utils

import android.os.Handler
import android.os.HandlerThread
import android.util.Log

/**
 * 解码线程管理器
 * 负责管理H.264解码的后台线程
 */
class DecodingThread {
    private var handlerThread: HandlerThread? = null
    private var handler: Handler? = null
    @Volatile
    private var isRunning = false
    
    companion object {
        private const val TAG = "DecodingThread"
        private const val THREAD_NAME = "H264DecoderThread"
    }
    
    /**
     * 启动解码线程
     */
    fun start(): Boolean {
        return try {
            if (isRunning) {
                Log.w(TAG, "Thread already running")
                return true
            }
            
            handlerThread = HandlerThread(THREAD_NAME).apply {
                start()
                handler = Handler(looper)
            }
            isRunning = true
            Log.d(TAG, "Decoding thread started")
            true
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start decoding thread", e)
            false
        }
    }
    
    /**
     * 在解码线程中执行任务
     */
    fun post(task: Runnable) {
        if (!isRunning) {
            Log.w(TAG, "Thread not running, cannot post task")
            return
        }
        handler?.post(task)
    }
    
    /**
     * 检查线程是否在运行
     */
    fun isRunning(): Boolean = isRunning
    
    /**
     * 停止解码线程
     */
    fun stop() {
        if (!isRunning) return
        
        isRunning = false
        try {
            handlerThread?.quitSafely()
            handlerThread = null
            handler = null
            Log.d(TAG, "Decoding thread stopped")
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping decoding thread", e)
        }
    }
}