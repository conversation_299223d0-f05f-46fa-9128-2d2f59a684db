{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/idedManger/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/idedManger/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/idedManger/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/idedManger/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-8cf149d12c55a1c916d8.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-eb3ae4b7d8ea466a4372.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-f318fe68d959e39c8b3f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-eb3ae4b7d8ea466a4372.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-f318fe68d959e39c8b3f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-8cf149d12c55a1c916d8.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}