<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F8F8F8">

    <!-- 顶部标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#FFFFFF"
        android:elevation="2dp"
        android:paddingHorizontal="16dp">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_centerVertical="true"
            android:src="@drawable/ic_arrow_back"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回"/>
            
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="选择续费设备"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageButton
            android:id="@+id/btn_sort"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@drawable/ic_sort"
            android:contentDescription="排序" />
    </RelativeLayout>

    <!-- VIP分类标签 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF"
        app:tabTextColor="#9E9E9E"
        app:tabSelectedTextColor="#529cff"
        app:tabIndicatorColor="#529cff"
        app:tabIndicatorHeight="2dp"
        app:tabMode="scrollable"/>

    <!-- 安卓版本选择器 -->
    <HorizontalScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF"
        android:scrollbars="none">
        
        <LinearLayout
            android:id="@+id/android_version_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">
            
            <!-- 动态添加配置按钮 -->
            
        </LinearLayout>
    </HorizontalScrollView>

    <!-- 设备列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_devices"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:padding="8dp"
        android:clipToPadding="false"/>

    <!-- 底部操作栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp"
        android:background="#FFFFFF"
        android:gravity="center_vertical">

        <CheckBox
            android:id="@+id/cb_select_all"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="全选"
            android:textColor="#333333"
            android:textSize="14sp"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_selected_count"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="(0台设备)"
            android:textColor="#666666"
            android:textSize="14sp"
            android:layout_marginStart="4dp"/>

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="120dp"
            android:layout_height="40dp"
            android:background="@drawable/bg_button_primary"
            android:text="确认选择"
            android:textColor="#FFFFFF"
            android:textSize="16sp"/>
    </LinearLayout>
</LinearLayout>