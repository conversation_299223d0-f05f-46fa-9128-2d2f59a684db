{"buildFiles": ["D:\\idedManger\\FlutterSdk\\flutter_windows_3.29.0-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\idedManger\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\CODE\\futter3x\\flutter04\\android\\app\\.cxx\\Debug\\4y1j4u36\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\idedManger\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\CODE\\futter3x\\flutter04\\android\\app\\.cxx\\Debug\\4y1j4u36\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\idedManger\\AndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\idedManger\\AndroidSDK\\ndk\\26.3.11579264\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}