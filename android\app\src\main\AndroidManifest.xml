<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.example.shiqianyun">
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <uses-permission android:name="android.permission.READ_CLIPBOARD" />
    <uses-permission android:name="android.permission.WRITE_CLIPBOARD" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
    <application
        android:label="@string/app_name"
        android:name=".MyApplication"
        android:icon="@drawable/yunshouji"
        android:usesCleartextTraffic="true">
        
        <!-- 启动页面 -->
        <activity
            android:name=".SplashActivity"
            android:exported="true"
            android:theme="@style/AppTheme"
            android:screenOrientation="portrait">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        
        <!-- MainActivity -->
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
        </activity>
        
        <!-- 微信支付回调Activity -->
        <activity
            android:name=".wxapi.WXPayEntryActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:taskAffinity="${applicationId}"
            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
            
        <!-- 登录界面 -->
        <activity
            android:name=".LoginActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:windowSoftInputMode="adjustResize"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true">
        </activity>
        
        <!-- 设备列表界面 -->
        <activity
            android:name=".DeviceListActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true">
        </activity>
        
        <!-- 分组管理界面 -->
        <activity
            android:name=".GroupManageActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
        </activity>
        
        <!-- 分组设备列表界面 -->
        <activity
            android:name=".GroupDevicesActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
        </activity>
        
        <!-- 批量续费界面 -->
        <activity
            android:name=".BatchRenewActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
        </activity>
        
        <!-- 续费套餐界面 -->
        <activity
            android:name=".RenewPackageActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
        </activity>
        
        <!-- 添加设备到分组界面 -->
        <activity
            android:name=".AddGroupDeviceActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
        </activity>
        <!--投屏页面 -->
         <activity
            android:name=".ScrcpyActivityRefactored"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/FullscreenTheme"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="sensor">
            <!-- <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter> -->
        </activity>
        <!-- 远程控制界面（重构版本） -->
        <!-- <activity
            android:name=".ScrcpyActivityRefactored"
            android:exported="true"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="fullSensor">
        </activity> -->
        
        <!-- 远程控制界面（原版本，保留备用） -->
        <activity
            android:name=".ScrcpyActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="fullSensor">
        </activity>
        
        <!-- 购买界面 -->
        <activity
            android:name=".PurchaseActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
        </activity>
        
        <!-- Cloud Disk Activity -->
        <activity
            android:name=".CloudDiskActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
        </activity>
        
        <!-- 消费记录界面 -->
        <activity
            android:name=".ConsumptionRecordActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait" />
            
        <!-- 激活码兑换界面 -->
        <activity
            android:name=".ActivationCodeActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait" />
            
        <!-- 设备选择界面 -->
        <activity
            android:name=".DeviceSelectActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait" />
            
        <!-- 批量重启设备界面 -->
        <activity
            android:name=".BatchRestartActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait" />
            
        <!-- 设备转移界面 -->
        <activity
            android:name=".DeviceTransferActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait" />
            
        <!-- 设备转移选择界面 -->
        <activity
            android:name=".DeviceTransferSelectActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait" />
            
        <!-- 文件推送设备选择界面 -->
        <activity
            android:name=".FilePushSelectActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait" />
            
        <!-- Local Files Activity -->
        <activity
            android:name=".LocalFilesActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
        </activity>
        
        <!-- Profile Activity -->
        <activity
            android:name=".ProfileActivity"
            android:exported="false"
            android:theme="@style/AppTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait">
        </activity>
        
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
    <!-- Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT"/>
            <data android:mimeType="text/plain"/>
        </intent>
    </queries>
</manifest>
