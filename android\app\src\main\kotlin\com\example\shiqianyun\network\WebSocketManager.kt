package com.example.shiqianyun.network

import android.util.Log
import com.example.shiqianyun.common.AppConstants
import com.example.shiqianyun.decoder.H264Decoder
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import okhttp3.*
import okio.ByteString

class WebSocketManager {
    private var webSocket: WebSocket? = null
    private var decoder: H264Decoder? = null
    private var isConnected: Boolean = false
    
    companion object {
        private const val TAG = "WebSocketManager"
    }

    fun connect(
        urlParam: String,
        decoder: H264Decoder,
        scope: CoroutineScope,
        onConnectionStateChanged: (Boolean) -> Unit
    ) {
        this.decoder = decoder
        
        val client = OkHttpClient()
        val request = Request.Builder()
            .url(AppConstants.WEBSOCKET_BASE_URL + urlParam)
            .build()

        connectInternal(request, scope, onConnectionStateChanged)
    }

    fun connectWithFullUrl(
        fullUrl: String,
        decoder: H264Decoder,
        scope: CoroutineScope,
        onConnectionStateChanged: (<PERSON>olean) -> Unit
    ) {
        this.decoder = decoder
        
        val client = OkHttpClient()
        val request = Request.Builder()
            .url(fullUrl)
            .build()

        connectInternal(request, scope, onConnectionStateChanged)
    }

    private fun connectInternal(
        request: Request,
        scope: CoroutineScope,
        onConnectionStateChanged: (Boolean) -> Unit
    ) {
        val client = OkHttpClient()
        webSocket = client.newWebSocket(request, object : WebSocketListener() {
            override fun onOpen(webSocket: WebSocket, response: Response) {
                scope.launch {
                    isConnected = true
                    onConnectionStateChanged(true)
                    Log.d(TAG, "onOpen: Connection established.")
                }
            }

            override fun onMessage(webSocket: WebSocket, bytes: ByteString) {
                val dataArray = bytes.toByteArray()
                Log.d(TAG, "onMessage: Received ${dataArray.size} bytes. Array: ${dataArray.contentToString()}")
                decoder?.onFrame(dataArray)
            }

            override fun onClosing(webSocket: WebSocket, code: Int, reason: String) {
                Log.d(TAG, "onClosing: Peer is closing: $code / $reason")
            }

            override fun onClosed(webSocket: WebSocket, code: Int, reason: String) {
                scope.launch {
                    isConnected = false
                    onConnectionStateChanged(false)
                    decoder?.release()
                    Log.d(TAG, "onClosed: Connection confirmed closed.")
                }
            }

            override fun onFailure(webSocket: WebSocket, t: Throwable, response: Response?) {
                scope.launch {
                    isConnected = false
                    onConnectionStateChanged(false)
                    decoder?.release()
                    Log.e(TAG, "onFailure: Connection failed.", t)
                }
            }
        })
    }

    fun disconnect() {
        Log.d(TAG, "Disconnect: Cancelling WebSocket.")
        webSocket?.cancel()
        decoder?.release()
    }

    fun isConnected(): Boolean = isConnected

    /**
     * 发送消息到WebSocket
     */
    fun sendMessage(message: String): Boolean {
        return if (isConnected && webSocket != null) {
            val success = webSocket!!.send(message)
            if (success) {
                Log.d(TAG, "消息发送成功: $message")
            } else {
                Log.e(TAG, "消息发送失败: $message")
            }
            success
        } else {
            Log.w(TAG, "WebSocket未连接，无法发送消息: $message")
            false
        }
    }
}