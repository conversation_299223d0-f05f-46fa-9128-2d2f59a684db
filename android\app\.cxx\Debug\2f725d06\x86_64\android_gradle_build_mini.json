{"buildFiles": ["D:\\idedManger\\FlutterSdk\\flutter_windows_3.29.0-stable\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\idedManger\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\CODE\\noAdbCloudPhone\\flutter04\\android\\app\\.cxx\\Debug\\2f725d06\\x86_64", "clean"]], "buildTargetsCommandComponents": ["D:\\idedManger\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\CODE\\noAdbCloudPhone\\flutter04\\android\\app\\.cxx\\Debug\\2f725d06\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}