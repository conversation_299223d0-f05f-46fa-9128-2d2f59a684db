package com.example.shiqianyun.manager

import android.content.Context
import android.util.Log
import com.example.shiqianyun.AudioDecoder
import com.example.shiqianyun.noAdbAudioDecoder

class AudioManager(
    private val context: Context,
    private val isNoAdbDevice: Boolean
) {
    companion object {
        private const val TAG = "AudioManager"
    }

    // 音频解码器
    private lateinit var audioDecoder: AudioDecoder
    private lateinit var noAdbAudioDecoder: noAdbAudioDecoder
    private var isAudioSupported = true
    private var isInitialized = false

    /**
     * 初始化音频解码器
     */
    fun initialize(): Bo<PERSON>an {
        return try {
            if (isNoAdbDevice) {
                // 免adb设备使用noAdbAudioDecoder
                Log.d(TAG, "初始化免adb设备音频解码器")
                noAdbAudioDecoder = noAdbAudioDecoder(context)
                noAdbAudioDecoder.initialize()
                isAudioSupported = true
                Log.d(TAG, "免adb设备音频解码器初始化成功")
            } else {
                // adb设备使用普通AudioDecoder
                Log.d(TAG, "初始化标准adb设备音频解码器")
                audioDecoder = AudioDecoder(context)
                audioDecoder.initialize()
                isAudioSupported = true
                Log.d(TAG, "标准adb设备音频解码器初始化成功")
            }
            
            isInitialized = true
            true
        } catch (e: Exception) {
            Log.e(TAG, "音频解码器初始化失败", e)
            isAudioSupported = false
            isInitialized = false
            false
        }
    }

    /**
     * 处理音频数据
     */
    fun processAudioData(data: ByteArray) {
        if (!isInitialized || !isAudioSupported) {
            return
        }

        try {
            if (isNoAdbDevice) {
                // 免adb设备使用noAdbAudioDecoder
                if (::noAdbAudioDecoder.isInitialized) {
                    noAdbAudioDecoder.processAudioData(data)
                }
            } else {
                // adb设备使用标准AudioDecoder
                if (::audioDecoder.isInitialized) {
                    audioDecoder.processAudioData(data)
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理音频数据失败: ${e.message}")
        }
    }

    /**
     * 增加音量
     */
    fun increaseVolume() {
        if (!isInitialized || !isAudioSupported) {
            return
        }

        try {
            if (isNoAdbDevice) {
                if (::noAdbAudioDecoder.isInitialized) {
                    noAdbAudioDecoder.increaseVolume()
                }
            } else {
                if (::audioDecoder.isInitialized) {
                    audioDecoder.increaseVolume()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "增加音量失败", e)
        }
    }

    /**
     * 减小音量
     */
    fun decreaseVolume() {
        if (!isInitialized || !isAudioSupported) {
            return
        }

        try {
            if (isNoAdbDevice) {
                if (::noAdbAudioDecoder.isInitialized) {
                    noAdbAudioDecoder.decreaseVolume()
                }
            } else {
                if (::audioDecoder.isInitialized) {
                    audioDecoder.decreaseVolume()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "减小音量失败", e)
        }
    }

    /**
     * 是否支持音频
     */
    fun isAudioSupported(): Boolean = isAudioSupported

    /**
     * 是否已初始化
     */
    fun isInitialized(): Boolean = isInitialized

    /**
     * 释放音频解码器资源
     */
    fun release() {
        try {
            if (isNoAdbDevice) {
                if (::noAdbAudioDecoder.isInitialized) {
                    noAdbAudioDecoder.dispose()
                }
            } else {
                if (::audioDecoder.isInitialized) {
                    audioDecoder.dispose()
                }
            }
            
            isInitialized = false
            Log.d(TAG, "音频解码器资源已释放")
        } catch (e: Exception) {
            Log.e(TAG, "释放音频解码器资源失败", e)
        }
    }
}
