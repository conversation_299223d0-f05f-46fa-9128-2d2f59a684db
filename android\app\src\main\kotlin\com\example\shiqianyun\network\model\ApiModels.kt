package com.example.shiqianyun.network.model

/**
 * 基础响应类
 */
data class BaseResponse<T>(
    val code: Int,
    val data: T,
    val msg: String
)

/**
 * 登录请求类
 */
data class LoginRequest(
    val type: String,  // "phone" 或 "Login"
    val phone: String? = null,  // 手机号登录使用
    val code: String? = null,   // 手机号登录使用
    val user_login_name: String? = null,  // 账号密码登录使用
    val password: String? = null  // 账号密码登录使用
)

/**
 * 登录响应类
 */
data class LoginResponse(
    val userid: Int,
    val tokne: String? = null  // API返回的token字段名为tokne（注意拼写）
)

/**
 * 设备列表响应类
 */
data class DeviceListResponse(
    val group: List<GroupInfo>? = null,
    val nonExpiredIDsData: List<DeviceInfo>? = null
)

/**
 * 分组信息
 */
data class GroupInfo(
    val group_name: String,
    val id: Int
)

/**
 * 设备信息实体类
 */
data class DeviceInfo(
    val id: Int = 0,
    val phone_id: Int = 0,
    val user_id: Int = 0,
    val nick_name: String = "",
    val phone_identify: String = "",
    val consul_id: String = "",
    val created_at: String = "",
    val updated_at: String = "",
    val over_time: String = "",
    val android_version: String = "",
    val vip_level: String = "",
    val vip_id: Int = 0,
    val adb_type: Boolean = false,
    val online_status: Boolean = true,
    val device_json: String = "", // 设备配置的JSON字符串
    var configText: String = "", // 设备配置文本，如 "8+128G"
    val device_designation: String? = null // 设备编号
) : android.os.Parcelable {
    constructor(parcel: android.os.Parcel) : this(
        parcel.readInt(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readInt(),
        parcel.readInt() == 1,
        parcel.readInt() == 1,
        parcel.readString() ?: "",
        parcel.readString() ?: "",
        parcel.readString()
    )

    override fun writeToParcel(parcel: android.os.Parcel, flags: Int) {
        parcel.writeInt(id)
        parcel.writeInt(phone_id)
        parcel.writeInt(user_id)
        parcel.writeString(nick_name)
        parcel.writeString(phone_identify)
        parcel.writeString(consul_id)
        parcel.writeString(created_at)
        parcel.writeString(updated_at)
        parcel.writeString(over_time)
        parcel.writeString(android_version)
        parcel.writeString(vip_level)
        parcel.writeInt(vip_id)
        parcel.writeInt(if (adb_type) 1 else 0)
        parcel.writeInt(if (online_status) 1 else 0)
        parcel.writeString(device_json)
        parcel.writeString(configText)
        parcel.writeString(device_designation)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : android.os.Parcelable.Creator<DeviceInfo> {
        override fun createFromParcel(parcel: android.os.Parcel): DeviceInfo {
            return DeviceInfo(parcel)
        }

        override fun newArray(size: Int): Array<DeviceInfo?> {
            return arrayOfNulls(size)
        }
    }
}

/**
 * VIP等级信息
 */
data class VipLevel(
    val id: Int = 0,
    val created_at: String = "",
    val updated_at: String = "",
    val vip_name: String = "",
    val level: Int = 0,
    val device_json: String = ""
)

/**
 * 获取分组设备请求类
 */
data class GroupDevicesRequest(
    val group_id: Int,
    val limit: Int = 16,
    val sort: String = "desc"
)