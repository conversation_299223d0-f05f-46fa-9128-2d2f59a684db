package com.example.shiqianyun.manager

import android.annotation.SuppressLint
import android.util.Log
import android.util.SparseArray
import android.view.MotionEvent
import android.view.SurfaceView
import com.example.shiqianyun.network.WebSocketManager
import org.json.JSONArray
import org.json.JSONObject

class TouchEventHandler(
    private val surfaceView: SurfaceView,
    private val webSocketManager: WebSocketManager,
    private val onUserActivity: () -> Unit
) {
    companion object {
        private const val TAG = "TouchEventHandler"
        
        // 触摸事件常量
        private const val ACTION_DOWN = 0
        private const val ACTION_UP = 1
        private const val ACTION_MOVE = 2
    }

    // 活动触摸点存储
    private val activePointers = SparseArray<Pair<Float, Float>>()
    
    // 设备分辨率
    private var deviceWidth: Int = 0
    private var deviceHeight: Int = 0
    private var realWidth: Int = 0
    private var realHeight: Int = 0

    /**
     * 更新设备分辨率
     */
    fun updateResolution(width: Int, height: Int, realW: Int = 0, realH: Int = 0) {
        deviceWidth = width
        deviceHeight = height
        realWidth = realW
        realHeight = realH
        
        Log.d(TAG, "更新分辨率: 设备=${deviceWidth}x${deviceHeight}, 真实=${realWidth}x${realHeight}")
    }

    /**
     * 更新H264Decoder的分辨率
     */
    fun updateH264Resolution(width: Int, height: Int) {
        realWidth = width
        realHeight = height
        Log.d(TAG, "更新H264分辨率: ${realWidth}x${realHeight}")
    }

    /**
     * 处理触摸事件
     */
    @SuppressLint("ClickableViewAccessibility")
    fun handleTouchEvent(event: MotionEvent): Boolean {
        val action = event.actionMasked
        val pointerIndex = event.actionIndex
        val pointerId = event.getPointerId(pointerIndex)

        when (action) {
            MotionEvent.ACTION_DOWN,
            MotionEvent.ACTION_POINTER_DOWN -> {
                // 处理新的触摸点
                val x = event.getX(pointerIndex)
                val y = event.getY(pointerIndex)
                activePointers.put(pointerId, Pair(x, y))
                processPointerEvent(pointerId, x, y, ACTION_DOWN, event.getPressure(pointerIndex))
            }

            MotionEvent.ACTION_MOVE -> {
                // 处理所有活动指针的移动
                for (i in 0 until event.pointerCount) {
                    val id = event.getPointerId(i)
                    val x = event.getX(i)
                    val y = event.getY(i)
                    activePointers.put(id, Pair(x, y))
                    processPointerEvent(id, x, y, ACTION_MOVE, event.getPressure(i))
                }
            }

            MotionEvent.ACTION_UP,
            MotionEvent.ACTION_POINTER_UP -> {
                // 处理触摸点释放
                val (x, y) = activePointers.get(pointerId, Pair(0f, 0f))
                processPointerEvent(pointerId, x, y, ACTION_UP, 0f)
                activePointers.remove(pointerId)
            }

            MotionEvent.ACTION_CANCEL -> {
                // 处理所有触摸点取消
                for (i in 0 until activePointers.size()) {
                    val id = activePointers.keyAt(i)
                    val (x, y) = activePointers.valueAt(i)
                    processPointerEvent(id, x, y, ACTION_UP, 0f)
                }
                activePointers.clear()
            }
        }

        return true
    }

    /**
     * 处理指针事件
     */
    private fun processPointerEvent(pointerId: Int, rawX: Float, rawY: Float, action: Int, pressure: Float) {
        try {
            // 转换为设备坐标 - 添加安全检查防止除以零
            if (surfaceView.width <= 0 || surfaceView.height <= 0) {
                Log.w(TAG, "无效的surfaceView尺寸: ${surfaceView.width}x${surfaceView.height}")
                return
            }
            
            // 使用缓存的准确分辨率，如果可用的话
            val targetWidth = if (realWidth > 0) realWidth else deviceWidth
            val targetHeight = if (realHeight > 0) realHeight else deviceHeight
            
            if (targetWidth <= 0 || targetHeight <= 0) {
                Log.w(TAG, "无效的目标设备分辨率: ${targetWidth}x${targetHeight}")
                return
            }
            
            // 确保使用裁剪后的实际分辨率计算
            val x = ((rawX * targetWidth) / surfaceView.width).toInt().coerceIn(0, targetWidth - 1)
            val y = ((rawY * targetHeight) / surfaceView.height).toInt().coerceIn(0, targetHeight - 1)
            
            sendTouchEvent(x, y, action, pointerId, pressure)
            onUserActivity()
            
        } catch (e: Exception) {
            Log.e(TAG, "处理触摸坐标转换失败", e)
        }
    }

    /**
     * 发送触摸事件
     */
    private fun sendTouchEvent(x: Int, y: Int, action: Int, pointerId: Int, pressure: Float) {
        try {
            // 使用准确的缓存分辨率
            val targetWidth = if (realWidth > 0) realWidth else deviceWidth
            val targetHeight = if (realHeight > 0) realHeight else deviceHeight
            
            // 完全匹配Flutter中的格式
            val touchMsg = JSONObject().apply {
                put("msg_type", 2) // SC_CONTROL_MSG_TYPE_INJECT_TOUCH_EVENT
                put("action", action)
                put("x", x)
                put("y", y)
                put("touch_id", pointerId)  // 使用MotionEvent的pointerId作为唯一标识
                
                // 使用固定值1.0代替pressure值，以便在模拟器和真机上都能正常工作
                put("pressure", 1.0) 

                // 重要：使用当前最新的设备分辨率
                val resolutionArray = JSONArray()
                resolutionArray.put(targetWidth)
                resolutionArray.put(targetHeight)
                put("resolution", resolutionArray)
            }

            webSocketManager.sendMessage(touchMsg.toString())
            
        } catch (e: Exception) {
            Log.e(TAG, "发送触摸事件失败", e)
        }
    }

    /**
     * 发送按键事件
     */
    fun sendKeyEvent(keyCode: Int) {
        try {
            // 发送按下事件 (ACTION_DOWN = 0)
            val keyMsg = JSONObject().apply {
                put("msg_type", 0) // SC_CONTROL_MSG_TYPE_INJECT_KEYCODE
                put("keycode", keyCode)
                put("action", 0) // ACTION_DOWN
            }
            webSocketManager.sendMessage(keyMsg.toString())
            Log.d(TAG, "发送按键DOWN事件: keyCode=$keyCode")

            // 延迟发送ACTION_UP事件 (ACTION_UP = 1)
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                val upMsg = JSONObject().apply {
                    put("msg_type", 0) // SC_CONTROL_MSG_TYPE_INJECT_KEYCODE
                    put("keycode", keyCode)
                    put("action", 1) // ACTION_UP
                }
                webSocketManager.sendMessage(upMsg.toString())
                Log.d(TAG, "发送按键UP事件: keyCode=$keyCode")
            }, 100)

            onUserActivity()
        } catch (e: Exception) {
            Log.e(TAG, "发送按键事件失败", e)
        }
    }

    /**
     * 获取剪贴板内容
     */
    fun getRemoteClipboardContent() {
        try {
            val message = JSONObject().apply {
                put("msg_type", 8) // SC_CONTROL_MSG_TYPE_GET_CLIPBOARD
            }       
            
            webSocketManager.sendMessage(message.toString())
            Log.d(TAG, "已发送获取剪贴板内容请求")
            
        } catch (e: Exception) {
            Log.e(TAG, "发送获取剪贴板请求失败", e)
        }
    }

    /**
     * 设置剪贴板内容
     */
    fun setRemoteClipboardContent(text: String) {
        try {
            val message = JSONObject().apply {
                put("msg_type", 9) // SC_CONTROL_MSG_TYPE_SET_CLIPBOARD
                put("text", text)
                put("paste", true)
            }
            
            webSocketManager.sendMessage(message.toString())
            Log.d(TAG, "已发送设置剪贴板内容请求: ${text.take(50)}${if (text.length > 50) "..." else ""}")
            
        } catch (e: Exception) {
            Log.e(TAG, "发送设置剪贴板请求失败", e)
        }
    }

    /**
     * 发送用户活跃信号
     */
    fun sendUserActiveSignal() {
        try {
            val activeMessage = JSONObject().apply {
                put("msg_type", 401) // 用户仍然活跃的消息类型
            }
            webSocketManager.sendMessage(activeMessage.toString())
            
            onUserActivity()
            Log.d(TAG, "发送用户活动信号")
        } catch (e: Exception) {
            Log.e(TAG, "发送用户活动信号失败", e)
        }
    }

    /**
     * 发送画质切换消息
     */
    fun sendQualityChangeMessage(fps: Int, bitRate: Int, maxSize: Int) {
        try {
            val qualityMessage = JSONObject().apply {
                put("msg_type", 999)
                put("fps", fps)
                put("bits", bitRate)
                put("size", maxSize)
            }
            
            Log.d(TAG, "发送画质切换消息: ${qualityMessage}")
            webSocketManager.sendMessage(qualityMessage.toString())
            
            Log.d(TAG, "画质切换消息已发送: FPS=${fps}, 码率=${bitRate}kbps, 最大边=${maxSize}")
            
        } catch (e: Exception) {
            Log.e(TAG, "发送画质切换消息失败", e)
        }
    }

    /**
     * 清理资源
     */
    fun release() {
        activePointers.clear()
    }
}
