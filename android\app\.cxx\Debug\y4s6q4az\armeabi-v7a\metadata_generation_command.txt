                        -HD:\idedManger\FlutterSdk\flutter_windows_3.29.0-stable\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=D:\idedManger\AndroidSDK\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=D:\idedManger\AndroidSDK\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=D:\idedManger\AndroidSDK\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=D:\idedManger\AndroidSDK\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\CODE\futter3x\flutter04\build\app\intermediates\cxx\Debug\y4s6q4az\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\CODE\futter3x\flutter04\build\app\intermediates\cxx\Debug\y4s6q4az\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BD:\CODE\futter3x\flutter04\android\app\.cxx\Debug\y4s6q4az\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2