package com.example.shiqianyun

import android.app.AlertDialog
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.DeviceInfo
import com.example.shiqianyun.network.model.DeviceListResponse
import com.example.shiqianyun.network.model.GroupInfo
import com.google.android.material.tabs.TabLayout
import com.google.gson.Gson
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 批量重启设备页面
 */
class BatchRestartActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "BatchRestartActivity"
    }

    // UI组件
    private lateinit var tabLayout: TabLayout
    private lateinit var recyclerView: RecyclerView
    private lateinit var btnConfirm: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var tvSelectedCount: TextView

    // 数据
    private var allDeviceList = mutableListOf<DeviceInfo>()
    private var groupList = mutableListOf<GroupInfo>()
    private var currentGroupDevices = mutableListOf<DeviceInfo>()
    private var selectedDevices = mutableSetOf<DeviceInfo>()
    
    // 适配器
    private lateinit var adapter: BatchRestartAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_batch_restart)

        initViews()
        setupListeners()
        loadDeviceData()
    }

    /**
     * 初始化视图
     */
    private fun initViews() {
        // 返回按钮
        findViewById<ImageView>(R.id.iv_back).setOnClickListener { finish() }
        
        tabLayout = findViewById(R.id.tab_layout)
        recyclerView = findViewById(R.id.recycler_view)
        btnConfirm = findViewById(R.id.btn_confirm)
        progressBar = findViewById(R.id.progress_bar)
        tvSelectedCount = findViewById(R.id.tv_selected_count)

        // 设置RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(this)
        adapter = BatchRestartAdapter(
            devices = currentGroupDevices,
            selectedDevices = selectedDevices,
            onSelectionChanged = { updateSelectedCount() }
        )
        recyclerView.adapter = adapter

        updateSelectedCount()
    }

    /**
     * 设置监听器
     */
    private fun setupListeners() {
        // TabLayout选择监听
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let { filterDevicesByGroup(it.position) }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })

        // 确认按钮
        btnConfirm.setOnClickListener {
            if (selectedDevices.isEmpty()) {
                Toast.makeText(this, "请选择要重启的设备", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            showRestartConfirmDialog()
        }
    }

    /**
     * 加载设备数据
     */
    private fun loadDeviceData() {
        showLoading()
        
        val userId = getUserId()
        RetrofitManager.apiService.getAllPhones(userId).enqueue(object : Callback<BaseResponse<DeviceListResponse>> {
            override fun onResponse(call: Call<BaseResponse<DeviceListResponse>>, response: Response<BaseResponse<DeviceListResponse>>) {
                hideLoading()
                
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        handleDeviceData(baseResponse.data)
                    } else {
                        Toast.makeText(this@BatchRestartActivity, baseResponse?.msg ?: "获取设备列表失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@BatchRestartActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<BaseResponse<DeviceListResponse>>, t: Throwable) {
                hideLoading()
                Log.e(TAG, "获取设备列表失败", t)
                Toast.makeText(this@BatchRestartActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    /**
     * 处理设备数据
     */
    private fun handleDeviceData(data: DeviceListResponse) {
        // 更新分组列表
        groupList.clear()
        groupList.addAll(data.group ?: emptyList())
        
        // 更新设备列表
        allDeviceList.clear()
        data.nonExpiredIDsData?.let { allDeviceList.addAll(it) }
        
        // 设置分组选项卡
        setupTabs()
        
        // 默认显示全部设备
        filterDevicesByGroup(0)
    }

    /**
     * 设置分组选项卡
     */
    private fun setupTabs() {
        tabLayout.removeAllTabs()
        
        // 添加"全部"选项卡
        tabLayout.addTab(tabLayout.newTab().setText("全部"))
        
        // 添加分组选项卡
        groupList.forEach { group ->
            tabLayout.addTab(tabLayout.newTab().setText(group.group_name))
        }
    }

    /**
     * 根据分组筛选设备
     */
    private fun filterDevicesByGroup(tabPosition: Int) {
        currentGroupDevices.clear()
        
        if (tabPosition == 0) {
            // 全部设备
            currentGroupDevices.addAll(allDeviceList)
        } else {
            // 特定分组的设备（需要调用API获取）
            val group = groupList[tabPosition - 1]
            loadGroupDevices(group.id)
            return
        }
        
        adapter.notifyDataSetChanged()
        updateSelectedCount()
    }

    /**
     * 加载分组设备
     */
    private fun loadGroupDevices(groupId: Int) {
        showLoading()
        
        RetrofitManager.apiService.getGroupDevices(
            groupId = groupId,
            limit = 100,
            sort = "desc"
        ).enqueue(object : Callback<BaseResponse<List<DeviceInfo>>> {
            override fun onResponse(call: Call<BaseResponse<List<DeviceInfo>>>, response: Response<BaseResponse<List<DeviceInfo>>>) {
                hideLoading()
                
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        currentGroupDevices.clear()
                        currentGroupDevices.addAll(baseResponse.data ?: emptyList())
                        adapter.notifyDataSetChanged()
                        updateSelectedCount()
                    } else {
                        Toast.makeText(this@BatchRestartActivity, baseResponse?.msg ?: "获取分组设备失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@BatchRestartActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<List<DeviceInfo>>>, t: Throwable) {
                hideLoading()
                Log.e(TAG, "获取分组设备失败", t)
                Toast.makeText(this@BatchRestartActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    /**
     * 更新选中数量显示
     */
    private fun updateSelectedCount() {
        val selectedCount = selectedDevices.size
        val totalCount = currentGroupDevices.size
        tvSelectedCount.text = "已选中 $selectedCount/$totalCount 台设备"
        
        btnConfirm.isEnabled = selectedCount > 0
        btnConfirm.text = if (selectedCount > 0) "重启设备($selectedCount)" else "重启设备"
    }

    /**
     * 显示重启确认对话框
     */
    private fun showRestartConfirmDialog() {
        val selectedCount = selectedDevices.size
        val message = "重启云手机能够完全关闭后台进程，释放内存和CPU占用，恢复手机运行速度\n\n确定要重启选中的 $selectedCount 台设备吗？"
        
        AlertDialog.Builder(this)
            .setTitle("重启云手机")
            .setMessage(message)
            .setPositiveButton("重启") { _, _ ->
                executeRestart()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 执行重启操作
     */
    private fun executeRestart() {
        showLoading()
        
        // 构建设备列表
        val deviceList = selectedDevices.map { it.phone_identify }
        
        // 构建请求体
        val requestMap = mapOf(
            "deviceList" to deviceList,
            "cmd" to "reboot"
        )
        
        // 转换为JSON RequestBody
        val gson = Gson()
        val json = gson.toJson(requestMap)
        val requestBody = json.toRequestBody("application/json".toMediaTypeOrNull())
        
        Log.d(TAG, "执行重启操作，设备数量: ${deviceList.size}")
        Log.d(TAG, "请求JSON: $json")
        
        // 调用重启API  
        RetrofitManager.apiService.executeShellCommandWithBody(requestBody).enqueue(object : Callback<BaseResponse<Any>> {
            override fun onResponse(call: Call<BaseResponse<Any>>, response: Response<BaseResponse<Any>>) {
                hideLoading()
                
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 重启成功
                        Toast.makeText(this@BatchRestartActivity, "云手机将在一分钟内完成重启，期间无法控制云手机", Toast.LENGTH_LONG).show()
                        finish()
                    } else {
                        Toast.makeText(this@BatchRestartActivity, baseResponse?.msg ?: "重启设备失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@BatchRestartActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                hideLoading()
                Log.e(TAG, "重启设备失败", t)
                Toast.makeText(this@BatchRestartActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    /**
     * 获取用户ID
     */
    private fun getUserId(): Int {
        val sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        return sharedPreferences.getInt("user_id", 0)
    }

    /**
     * 显示加载中
     */
    private fun showLoading() {
        progressBar.visibility = View.VISIBLE
    }

    /**
     * 隐藏加载中
     */
    private fun hideLoading() {
        progressBar.visibility = View.GONE
    }

    /**
     * 批量重启设备适配器
     */
    class BatchRestartAdapter(
        private val devices: MutableList<DeviceInfo>,
        private val selectedDevices: MutableSet<DeviceInfo>,
        private val onSelectionChanged: () -> Unit
    ) : RecyclerView.Adapter<BatchRestartAdapter.ViewHolder>() {

        class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val checkBox: CheckBox = view.findViewById(R.id.checkbox)
            val tvDeviceName: TextView = view.findViewById(R.id.tv_device_name)
            val tvDeviceInfo: TextView = view.findViewById(R.id.tv_device_info)
            val tvStatus: TextView = view.findViewById(R.id.tv_status)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_batch_restart_device, parent, false)
            return ViewHolder(view)
        }

        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val device = devices[position]
            
            holder.tvDeviceName.text = device.nick_name
            holder.tvDeviceInfo.text = "${device.vip_level} | ${device.android_version} | ${device.device_designation ?: device.phone_id}"
            holder.tvStatus.text = if (device.online_status) "在线" else "离线"
            holder.tvStatus.setTextColor(
                if (device.online_status) 
                    androidx.core.content.ContextCompat.getColor(holder.itemView.context, android.R.color.holo_green_dark)
                else 
                    androidx.core.content.ContextCompat.getColor(holder.itemView.context, android.R.color.holo_red_dark)
            )
            
            holder.checkBox.isChecked = selectedDevices.contains(device)
            
            // 设置点击事件
            holder.itemView.setOnClickListener {
                toggleSelection(device)
            }
            
            holder.checkBox.setOnClickListener {
                toggleSelection(device)
            }
        }

        override fun getItemCount() = devices.size

        private fun toggleSelection(device: DeviceInfo) {
            if (selectedDevices.contains(device)) {
                selectedDevices.remove(device)
            } else {
                selectedDevices.add(device)
            }
            notifyDataSetChanged()
            onSelectionChanged()
        }
    }
}