package com.example.shiqianyun

import android.graphics.SurfaceTexture
import android.media.MediaCodec
import android.media.MediaFormat
import android.media.MediaCodecInfo
import android.util.Log
import android.view.Surface
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import io.flutter.view.TextureRegistry
import java.nio.ByteBuffer
import java.util.concurrent.LinkedBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import com.example.shiqianyun.network.model.BitrateConfig

class MainActivity : FlutterActivity() {
    private val CHANNEL = "h264_decoder"
    private val SCRCPY_CHANNEL = "scrcpy_native"
    private var mediaCodec: MediaCodec? = null
    private var width = 336
    private var height = 720
    private var sps: ByteArray? = null
    private var pps: ByteArray? = null
    private var isFirstFrame = true
    private var surfaceTexture: SurfaceTexture? = null
    private var surface: Surface? = null
    private var textureEntry: TextureRegistry.SurfaceTextureEntry? = null
    private var frameCount = 0
    
    private var threadPool = ThreadPoolExecutor(
        1, 1, 0L, TimeUnit.MILLISECONDS,
        LinkedBlockingQueue()
    )
    
    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "initialize" -> {
                    try {
                        // 获取传入参数
                        width = call.argument<Int>("width") ?: 336
                        height = call.argument<Int>("height") ?: 720
                        sps = call.argument<ByteArray>("sps")
                        pps = call.argument<ByteArray>("pps")
                        
                        // 验证参数
                        if (sps == null || pps == null) {
                            result.error("INVALID_ARGUMENT", "SPS或PPS为空", null)
                            return@setMethodCallHandler
                        }
                        
                        // 尝试从SPS中解析分辨率
                        parseResolutionFromSps()
                        
                        // 释放旧资源
                        releaseResources()
                        
                        // 创建SurfaceTexture
                        createSurfaceTexture(flutterEngine)
                        
                        // 配置和初始化解码器
                        initializeDecoder(result)
                    } catch (e: Exception) {
                        Log.e("H264Decoder", "初始化失败", e)
                        result.error("INIT_ERROR", e.message, null)
                    }
                }
                "decode" -> {
                    threadPool.execute {
                        try {
                            val frameData = call.argument<ByteArray>("frameData")
                            if (frameData == null) {
                                result.error("INVALID_ARGUMENT", "帧数据为空", null)
                                return@execute
                            }
                            
                            val codec = mediaCodec
                            if (codec == null) {
                                result.error("NOT_INITIALIZED", "解码器未初始化", null)
                                return@execute
                            }
                            
                            decodeFrame(frameData, codec)
                            result.success(true)
                        } catch (e: Exception) {
                            Log.e("H264Decoder", "解码失败", e)
                            result.error("DECODE_ERROR", e.message, null)
                        }
                    }
                }
                "dispose" -> {
                    try {
                        Log.d("H264Decoder", "开始释放解码器资源...")
                        releaseResources()
                        resetThreadPool()
                        result.success(null)
                    } catch (e: Exception) {
                        Log.e("H264Decoder", "释放失败", e)
                        result.error("DISPOSE_ERROR", e.message, null)
                    }
                }
                else -> result.notImplemented()
            }
        }
        
        // 添加ScrcpyActivity通信通道
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, SCRCPY_CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "launchNativeScrcpy" -> {
                    try {
                        val wsUrl = call.argument<String>("wsUrl")
                        var width = call.argument<Int>("width") ?: 0
                        var height = call.argument<Int>("height") ?: 0
                        
                        if (wsUrl.isNullOrEmpty()) {
                            result.error("INVALID_PARAMS", "无效的WebSocket URL", null)
                            return@setMethodCallHandler
                        }
                        
                        // 如果宽度或高度无效，使用默认值
                        if (width <= 0 || height <= 0) {
                            Log.d("ScrcpyNative", "使用默认尺寸 720x1280 替代 ${width}x${height}")
                            width = 720
                            height = 1280
                        }
                        
                        // 从参数中获取设备昵称
                        val deviceName = call.argument<String>("deviceName") ?: "云手机"
                        
                        // 启动原生Scrcpy界面（重构版本）
                        // 创建默认的码率配置列表
                        val bitrateConfigList = ArrayList<BitrateConfig>()
                        
                        ScrcpyActivityRefactored.startActivity(
                            this, 
                            wsUrl, 
                            width, 
                            height,
                            bitrateConfigList,
                            "default_user", // 默认用户ID
                            "default_phone", // 默认手机ID
                            "default_identify", // 默认手机标识
                            deviceName // 设备昵称
                        )
                        result.success(true)
                    } catch (e: Exception) {
                        Log.e("ScrcpyNative", "启动失败", e)
                        result.error("LAUNCH_ERROR", e.message, null)
                    }
                }
                else -> result.notImplemented()
            }
        }
    }

    // 从SPS中解析实际分辨率
    private fun parseResolutionFromSps() {
        try {
            // 创建格式并配置SPS/PPS
            val spsPpsFormat = MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, width, height).apply {
                setByteBuffer("csd-0", ByteBuffer.wrap(sps!!))
                setByteBuffer("csd-1", ByteBuffer.wrap(pps!!))
            }
            
            // 创建临时解码器获取分辨率信息
            val tempDecoder = MediaCodec.createDecoderByType(MediaFormat.MIMETYPE_VIDEO_AVC)
            try {
                tempDecoder.configure(spsPpsFormat, null, null, 0)
                
                // 获取解析后的格式
                val actualFormat = tempDecoder.outputFormat
                
                // 更新宽高
                if (actualFormat.containsKey(MediaFormat.KEY_WIDTH) && actualFormat.containsKey(MediaFormat.KEY_HEIGHT)) {
                    val parsedWidth = actualFormat.getInteger(MediaFormat.KEY_WIDTH)
                    val parsedHeight = actualFormat.getInteger(MediaFormat.KEY_HEIGHT)
                    
                    if (parsedWidth > 0 && parsedHeight > 0 && 
                        (parsedWidth != width || parsedHeight != height)) {
                        Log.d("H264Decoder", "从SPS解析到的分辨率: ${parsedWidth}x${parsedHeight}，覆盖传入的: ${width}x${height}")
                        width = parsedWidth
                        height = parsedHeight
                    }
                }
            } finally {
                // 手动释放临时解码器
                tempDecoder.release()
            }
        } catch (e: Exception) {
            Log.w("H264Decoder", "从SPS解析分辨率失败，使用传入参数: ${width}x${height}", e)
        }
    }

    // 释放资源
    private fun releaseResources() {
        try {
            mediaCodec?.stop()
            mediaCodec?.release()
            mediaCodec = null
            
            surface?.release()
            surface = null
            surfaceTexture?.release()
            surfaceTexture = null
            textureEntry?.release()
            textureEntry = null
        } catch (e: Exception) {
            Log.e("H264Decoder", "释放资源时出错", e)
        }
    }

    // 创建SurfaceTexture
    private fun createSurfaceTexture(flutterEngine: FlutterEngine) {
        textureEntry = flutterEngine.renderer.createSurfaceTexture()
        surfaceTexture = textureEntry?.surfaceTexture()?.apply {
            setDefaultBufferSize(width, height)
        }
        surface = Surface(surfaceTexture)
    }

    // 创建并配置解码格式
    private fun createDecoderFormat(): MediaFormat {
        try {
            return MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, width, height).apply {
                setByteBuffer("csd-0", ByteBuffer.wrap(sps!!))
                setByteBuffer("csd-1", ByteBuffer.wrap(pps!!))
                setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface)
                setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, width * height)
                setInteger(MediaFormat.KEY_FRAME_RATE, 60)
                setInteger(MediaFormat.KEY_LOW_LATENCY, 1)
                setInteger(MediaFormat.KEY_ALLOW_FRAME_DROP, 1)
            }
        } catch (e: Exception) {
            Log.e("H264Decoder", "创建格式失败，尝试使用16的倍数分辨率", e)
            
            // 调整为16的倍数
            val adjustedWidth = (width + 15) / 16 * 16
            val adjustedHeight = (height + 15) / 16 * 16
            
            Log.d("H264Decoder", "调整分辨率: ${width}x${height} -> ${adjustedWidth}x${adjustedHeight}")
            
            return MediaFormat.createVideoFormat(MediaFormat.MIMETYPE_VIDEO_AVC, adjustedWidth, adjustedHeight).apply {
                setByteBuffer("csd-0", ByteBuffer.wrap(sps!!))
                setByteBuffer("csd-1", ByteBuffer.wrap(pps!!))
                setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface)
                setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, adjustedWidth * adjustedHeight)
                setInteger(MediaFormat.KEY_FRAME_RATE, 60)
                setInteger(MediaFormat.KEY_LOW_LATENCY, 1)
                setInteger(MediaFormat.KEY_ALLOW_FRAME_DROP, 1)
            }
        }
    }

    // 初始化解码器
    private fun initializeDecoder(result: MethodChannel.Result) {
        try {
            val format = createDecoderFormat()
            
            // 创建解码器
            mediaCodec = MediaCodec.createDecoderByType(MediaFormat.MIMETYPE_VIDEO_AVC).apply {
                configure(format, surface, null, 0)
                start()
            }
            
            frameCount = 0
            isFirstFrame = true
            Log.d("H264Decoder", "解码器初始化成功，配置：$format")
            
            // 返回结果
            result.success(mapOf(
                "textureId" to textureEntry?.id(),
                "width" to width,
                "height" to height
            ))
        } catch (e: Exception) {
            Log.e("H264Decoder", "配置解码器失败", e)
            
            // 释放资源
            releaseResources()
            throw e
        }
    }

    // 解码帧
    private fun decodeFrame(frameData: ByteArray, codec: MediaCodec) {
        // 检测起始码和NAL类型
        val startCodeLength = if (frameData.size >= 4 && 
                                frameData[0] == 0x00.toByte() && 
                                frameData[1] == 0x00.toByte() && 
                                frameData[2] == 0x00.toByte() && 
                                frameData[3] == 0x01.toByte()) 4 else 3
                                
        val nalType = (frameData[startCodeLength].toInt() and 0x1F)
        frameCount++
        
        // 定期日志
        if (isFirstFrame || frameCount % 300 == 0) {
            Log.d("H264Decoder", "处理第 $frameCount 帧, NAL类型: $nalType")
        }
        
        // 输入缓冲区
        val inputBufferId = codec.dequeueInputBuffer(5000)
        if (inputBufferId >= 0) {
            codec.getInputBuffer(inputBufferId)?.let { inputBuffer ->
                inputBuffer.clear()
                inputBuffer.put(frameData)
                
                // 确定帧标志
                val flags = when {
                    nalType == 5 -> {
                        Log.d("H264Decoder", "收到关键帧，帧计数: $frameCount")
                        MediaCodec.BUFFER_FLAG_KEY_FRAME
                    }
                    nalType == 7 || nalType == 8 -> MediaCodec.BUFFER_FLAG_CODEC_CONFIG
                    else -> 0
                }
                
                codec.queueInputBuffer(inputBufferId, 0, frameData.size, System.nanoTime() / 1000, flags)
            }
        }
        
        // 输出缓冲区
        val bufferInfo = MediaCodec.BufferInfo()
        var outputBufferId = codec.dequeueOutputBuffer(bufferInfo, 5000)
        
        while (outputBufferId >= 0) {
            if (isFirstFrame) {
                Log.d("H264Decoder", "第一帧输出完成，总处理帧数: $frameCount")
                isFirstFrame = false
            }
            codec.releaseOutputBuffer(outputBufferId, true)
            outputBufferId = codec.dequeueOutputBuffer(bufferInfo, 0)
        }
    }

    // 重置线程池
    private fun resetThreadPool() {
        try {
            threadPool.shutdown()
            
            // 等待最多1秒让任务结束
            if (!threadPool.awaitTermination(1, TimeUnit.SECONDS)) {
                threadPool.shutdownNow()
            }
        } catch (e: Exception) {
            Log.e("H264Decoder", "关闭线程池失败", e)
            threadPool.shutdownNow()
        }
        
        // 创建新线程池
        threadPool = ThreadPoolExecutor(
            1, 1, 0L, TimeUnit.MILLISECONDS,
            LinkedBlockingQueue()
        )
        
        Log.d("H264Decoder", "线程池已重置")
    }
}
