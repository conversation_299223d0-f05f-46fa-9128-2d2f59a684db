<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5F5">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 顶部导航栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:background="#FFFFFF"
            android:elevation="2dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingHorizontal="16dp">

            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_arrow_back"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:padding="4dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center"
                android:text="设备转移"
                android:textColor="#333333"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="记录"
                android:textColor="#2196F3"
                android:textSize="14sp"
                android:padding="8dp"
                android:background="?android:attr/selectableItemBackgroundBorderless" />

        </LinearLayout>

        <!-- 选择设备区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginHorizontal="16dp"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="选择设备"
                android:textColor="#333333"
                android:textSize="16sp"
                android:textStyle="bold" />

                         <LinearLayout
                 android:id="@+id/ll_select_device"
                 android:layout_width="match_parent"
                 android:layout_height="48dp"
                 android:layout_marginTop="12dp"
                 android:background="@drawable/input_background"
                 android:gravity="center_vertical"
                 android:orientation="horizontal"
                 android:paddingHorizontal="12dp"
                 android:clickable="true"
                 android:focusable="true"
                 android:foreground="?android:attr/selectableItemBackground">

                 <TextView
                     android:id="@+id/tv_selected_devices"
                     android:layout_width="0dp"
                     android:layout_height="wrap_content"
                     android:layout_weight="1"
                     android:text="请选择要转移的设备"
                     android:textColor="#666666"
                     android:textSize="14sp" />

                 <ImageView
                     android:layout_width="24dp"
                     android:layout_height="24dp"
                     android:src="@drawable/ic_arrow_drop_down"
                     app:tint="#666666" />

             </LinearLayout>

             <TextView
                 android:id="@+id/tv_selected_count"
                 android:layout_width="wrap_content"
                 android:layout_height="wrap_content"
                 android:layout_marginTop="8dp"
                 android:text="已选中 0/0 台设备"
                 android:textColor="#999999"
                 android:textSize="12sp" />

        </LinearLayout>

        <!-- 接收方账号区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginHorizontal="16dp"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="接收方账号"
                android:textColor="#333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <EditText
                android:id="@+id/et_target_phone"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="12dp"
                android:background="@drawable/input_background"
                android:hint="输入对方手机号/用户ID"
                android:inputType="phone"
                android:paddingHorizontal="12dp"
                android:textColor="#333333"
                android:textColorHint="#999999"
                android:textSize="14sp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="提示：需要给你的手机号 177****3839 发送验证码进行身份确认"
                android:textColor="#FF9800"
                android:textSize="12sp"
                android:visibility="gone" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="短信验证码"
                android:textColor="#333333"
                android:textSize="14sp"
                android:visibility="gone" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:layout_marginTop="8dp"
                android:orientation="horizontal"
                android:visibility="gone">

                <EditText
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:background="@drawable/input_background"
                    android:hint="输入验证码"
                    android:inputType="number"
                    android:paddingHorizontal="12dp"
                    android:textColor="#333333"
                    android:textColorHint="#999999"
                    android:textSize="14sp" />

                <Button
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginStart="12dp"
                    android:background="@drawable/button_outlined"
                    android:text="发送验证码"
                    android:textColor="#2196F3"
                    android:textSize="14sp" />

            </LinearLayout>

        </LinearLayout>

        <!-- 转移说明 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginHorizontal="16dp"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="转移说明"
                android:textColor="#333333"
                android:textSize="16sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:lineSpacingExtra="4dp"
                android:text="1:在进行设备转移操作时,设备剩余时间要大于24小时.\n\n2:转移会扣除6小时时间.\n\n3:由于设备转移操作不可逆,在转移前请确认账号信息准确.\n\n4:当通过转移交易时，请走闲鱼这种第三方担保平台，如果私下交易被骗，我们平台没办法帮您挽回任何损失！\n\n注意:转移设备时转移的设备到其他的账号,而不是转移设备的时间!请合理且合法利用设备转移功能."
                android:textColor="#666666"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 确认转移按钮 -->
        <Button
            android:id="@+id/btn_confirm_transfer"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_margin="16dp"
            android:background="@drawable/button_primary"
            android:text="确认转移"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 加载进度条 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:visibility="gone" />

    </LinearLayout>

</ScrollView>