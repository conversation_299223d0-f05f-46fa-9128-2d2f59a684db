package com.example.shiqianyun.decoder

import android.util.Log
import android.view.Surface
import com.example.shiqianyun.decoder.codec.MediaCodecManager
import com.example.shiqianyun.decoder.parser.NALUnitParser
import com.example.shiqianyun.decoder.parser.SPSParser
import com.example.shiqianyun.decoder.utils.DecodingThread

/**
 * H.264解码器主类
 * 整合NAL解析、SPS解析、MediaCodec管理等功能
 */
class H264Decoder(
    private val surface: Surface,
    private val onOrientationChanged: ((Boolean) -> Unit)? = null, // true为横屏，false为竖屏
    private val onResolutionChanged: ((Int, Int) -> Unit)? = null // 分辨率变化回调
) {
    private val nalParser = NALUnitParser()
    private val spsParser = SPSParser()
    private val codecManager = MediaCodecManager(surface)
    private val decodingThread = DecodingThread()
    
    private var sps: ByteArray? = null
    private var pps: ByteArray? = null
    private var currentSPSInfo: SPSParser.SPSInfo? = null
    private var pendingSPSInfo: SPSParser.SPSInfo? = null
    private var videoWidth = DEFAULT_WIDTH
    private var videoHeight = DEFAULT_HEIGHT

    companion object {
        private const val TAG = "H264Decoder"
        private const val DEFAULT_WIDTH = 1920
        private const val DEFAULT_HEIGHT = 1080
    }

    /**
     * 启动解码器
     */
    fun start(): Boolean {
        val started = decodingThread.start()
        if (started) {
            Log.d(TAG, "H264Decoder started, waiting for SPS/PPS from stream...")
        }
        return started
    }
    

    /**
     * 处理接收到的H.264数据帧
     */
    fun onFrame(data: ByteArray) {
        if (!decodingThread.isRunning()) return
        
        decodingThread.post {
            nalParser.processRawData(data) { nalUnit ->
                processNalUnit(nalUnit)
            }
        }
    }

    /**
     * 处理解析出的NAL单元
     */
    private fun processNalUnit(nalUnit: ByteArray) {
        if (nalUnit.isEmpty()) return
        
        val nalUnitType = nalParser.getNalUnitType(nalUnit)
        Log.d(TAG, "Processing NAL unit type: $nalUnitType, size: ${nalUnit.size}, data: ${nalUnit.take(10).joinToString { "%02x".format(it) }}")
        
        when (nalUnitType) {
            NALUnitParser.NAL_UNIT_TYPE_SPS -> {
                Log.d(TAG, "SPS received, size: ${nalUnit.size}")
                // 过滤异常/伪造的 SPS：forbidden_zero_bit 必须为 0，nal_ref_idc 不应为 0，且长度需合理
                val header: Int = nalUnit[0].toInt() and 0xFF
                val forbiddenZeroBitIsOne = (header and 0x80) != 0
                if (forbiddenZeroBitIsOne) {
                    Log.w(
                        TAG,
                        "Skipping invalid SPS: forbidden_zero_bit=1, size=${nalUnit.size}, head=0x${"%02x".format(header)}"
                    )
                    return
                }
                val nalRefIdc = (header and 0x60) shr 5
                if (nalRefIdc == 0) {
                    Log.w(
                        TAG,
                        "Skipping SPS with nal_ref_idc=0 (likely non-reference/invalid): size=${nalUnit.size}, head=0x${"%02x".format(header)}"
                    )
                    return
                }
                if (nalUnit.size < 8) {
                    Log.w(TAG, "Skipping too-short SPS: size=${nalUnit.size}")
                    return
                }
                if (nalUnit.size > 128) {
                    Log.w(TAG, "Skipping overlong SPS: size=${nalUnit.size}")
                    return
                }
                val profileIdc = nalUnit[1].toInt() and 0xFF
                val validProfiles = setOf(66, 77, 88, 100, 110, 122, 244, 44, 83, 86, 118, 128)
                if (profileIdc !in validProfiles) {
                    Log.w(TAG, "Skipping SPS with suspicious profile_idc=$profileIdc, size=${nalUnit.size}")
                    return
                }
                val newSPSInfo = try {
                    spsParser.parseSPS(nalUnit)
                } catch (e: Throwable) {
                    Log.w(TAG, "Skipping SPS due to parse error: ${e.message}")
                    return
                }
                // 解析后再做一次分辨率的基本合理性校验，过滤异常的小分辨率
                if (newSPSInfo.width < 128 || newSPSInfo.height < 128) {
                    Log.w(TAG, "Skipping implausible SPS resolution: ${newSPSInfo.width}x${newSPSInfo.height}")
                    return
                }
                // 不再立即应用；挂起等待 PPS + 下一个 IDR 再应用，避免伪 SPS 造成错误重配
                sps = nalUnit
                pendingSPSInfo = newSPSInfo
                Log.w(
                    TAG,
                    "Queued SPS pending IDR: candidate=${newSPSInfo.getResolutionString()}, current=${currentSPSInfo?.getResolutionString() ?: "unknown"}"
                )
            }
            NALUnitParser.NAL_UNIT_TYPE_PPS -> {
                pps = nalUnit
                Log.d(TAG, "PPS received, size: ${nalUnit.size}")
                checkAndConfigureCodec()
            }
            NALUnitParser.NAL_UNIT_TYPE_IDR, NALUnitParser.NAL_UNIT_TYPE_NON_IDR -> {
                // 在接收到 IDR 时，如果有挂起的 SPS + PPS，则应用分辨率并强制重配
                if (nalUnitType == NALUnitParser.NAL_UNIT_TYPE_IDR && pendingSPSInfo != null && sps != null && pps != null) {
                    val candidate = pendingSPSInfo!!
                    val current = currentSPSInfo
                    val candidateArea = candidate.width * candidate.height
                    val currentArea = (current?.width ?: 0) * (current?.height ?: 0)
                    val plausibleByArea = current == null || candidateArea >= currentArea / 4
                    if (!plausibleByArea) {
                        Log.w(TAG, "Discarding pending SPS due to drastic downscale: ${candidate.getResolutionString()} vs ${current?.getResolutionString()}")
                        pendingSPSInfo = null
                    } else {
                        val orientationChanged = candidate.hasOrientationChanged(current)
                        if (orientationChanged) {
                            Log.w(TAG, "🔄 ORIENTATION CHANGE DETECTED! ${current?.getOrientationString() ?: "unknown"} -> ${candidate.getOrientationString()}")
                        }
                        val resolutionChanged = candidate.hasResolutionChanged(current)
                        if (resolutionChanged || current == null) {
                            Log.w(TAG, "⚡ RESOLUTION CHANGE DETECTED! ${current?.getResolutionString() ?: "unknown"} -> ${candidate.getResolutionString()}")
                            currentSPSInfo = candidate
                            videoWidth = candidate.width
                            videoHeight = candidate.height
                            // 通知
                            onOrientationChanged?.invoke(candidate.isLandscape())
                            onResolutionChanged?.invoke(videoWidth, videoHeight)
                            Log.w(TAG, "🔄 Reinitializing decoder for new resolution: ${videoWidth}x${videoHeight} (${candidate.getOrientationString()})")
                            checkAndConfigureCodec(forceReconfigure = true)
                        }
                        pendingSPSInfo = null
                    }
                }
                if (codecManager.isConfigured()) {
                    codecManager.decode(nalUnit)
                } else {
                    Log.w(TAG, "Received frame data but codec not configured yet")
                }
            }
            else -> {
                Log.d(TAG, "Unknown NAL unit type: $nalUnitType")
            }
        }
    }
    
    /**
     * 检查并配置MediaCodec
     */
    private fun checkAndConfigureCodec(forceReconfigure: Boolean = false) {
        val spsData = sps
        val ppsData = pps
        
        if (spsData != null && ppsData != null) {
            // 如果强制重配置或尚未配置，则进行配置
            if (forceReconfigure || !codecManager.isConfigured()) {
                Log.d(TAG, "Configuring MediaCodec: force=$forceReconfigure, resolution=${videoWidth}x${videoHeight}")
                val success = codecManager.configureCodec(spsData, ppsData, videoWidth, videoHeight, forceReconfigure)
                if (success) {
                    Log.i(TAG, "MediaCodec configured successfully with resolution: ${videoWidth}x${videoHeight}")
                } else {
                    Log.e(TAG, "Failed to configure MediaCodec")
                }
            }
        }
    }

    /**
     * 释放解码器资源
     */
    fun release() {
        if (!decodingThread.isRunning()) return
        
        decodingThread.post {
            nalParser.clear()
            codecManager.release()
        }
        
        decodingThread.stop()
        Log.d(TAG, "H264Decoder released")
    }

    /**
     * 获取当前视频宽度
     */
    fun getVideoWidth(): Int = videoWidth

    /**
     * 获取当前视频高度
     */
    fun getVideoHeight(): Int = videoHeight

    /**
     * 获取当前分辨率
     */
    fun getResolution(): Pair<Int, Int> = Pair(videoWidth, videoHeight)
}
