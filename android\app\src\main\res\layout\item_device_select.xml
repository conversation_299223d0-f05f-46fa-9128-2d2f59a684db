<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical">

        <CheckBox
            android:id="@+id/cb_select"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="12dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/tv_vip_level"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="VIP 4+64G"
                    android:textColor="#FF5722"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    android:background="@drawable/bg_tag_vip"
                    android:paddingHorizontal="8dp"
                    android:paddingVertical="2dp" />

                <TextView
                    android:id="@+id/tv_device_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="8dp"
                    android:text="我的设备"
                    android:textColor="#333333"
                    android:textSize="16sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_device_id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="ID 12345"
                    android:textColor="#666666"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/tv_android_version"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="安卓11"
                    android:textColor="#666666"
                    android:textSize="12sp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1" />

                <TextView
                    android:id="@+id/tv_remaining_time"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="剩余 5天3时"
                    android:textColor="#4CAF50"
                    android:textSize="12sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.cardview.widget.CardView>