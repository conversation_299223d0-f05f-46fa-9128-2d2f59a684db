package com.example.shiqianyun

import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.text.style.ForegroundColorSpan
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.LoginRequest
import com.example.shiqianyun.network.model.LoginResponse
import com.google.android.material.textfield.TextInputEditText
import com.google.android.material.textfield.TextInputLayout
import android.widget.CheckBox
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

/**
 * 登录界面
 */
class LoginActivity : AppCompatActivity() {
    companion object {
        private const val TAG = "LoginActivity"
        private const val COUNTDOWN_TIME = 60000L  // 60秒倒计时
    }
    
    // UI组件
    private lateinit var tilPhone: TextInputLayout
    private lateinit var etPhone: TextInputEditText
    private lateinit var tilCode: TextInputLayout
    private lateinit var etCode: TextInputEditText
    private lateinit var btnGetCode: Button
    private lateinit var btnLogin: Button
    private lateinit var layoutWechat: LinearLayout
    
    // 账号密码登录组件
    private lateinit var tilUsername: TextInputLayout
    private lateinit var etUsername: TextInputEditText
    private lateinit var tilPassword: TextInputLayout
    private lateinit var etPassword: TextInputEditText
    private lateinit var cbRememberPassword: CheckBox
    
    // 登录表单容器
    private lateinit var layoutPhoneForm: LinearLayout
    private lateinit var layoutAccountForm: LinearLayout
    
    // 登录方式切换
    private lateinit var tvPhoneLogin: TextView
    private lateinit var tvAccountLogin: TextView
    
    // 注册入口文本
    private lateinit var tvRegisterPrompt: TextView
    
    // 当前登录方式
    private var isPhoneLogin = true
    
    // 倒计时
    private var countDownTimer: CountDownTimer? = null
    private var isCountingDown = false

    // 注册对话框
    private var registerDialog: AlertDialog? = null
    private var registerCountDownTimer: CountDownTimer? = null
    private var isRegisterCountingDown = false
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)
        
        // 初始化UI组件
        initViews()
        
        // 设置点击事件
        setupListeners()
    }
    
    /**
     * 初始化UI组件
     */
    private fun initViews() {
        // 手机验证码登录组件
        tilPhone = findViewById(R.id.til_phone)
        etPhone = findViewById(R.id.et_phone)
        tilCode = findViewById(R.id.til_code)
        etCode = findViewById(R.id.et_code)
        btnGetCode = findViewById(R.id.btn_get_code)
        btnLogin = findViewById(R.id.btn_login)
        layoutWechat = findViewById(R.id.layout_wechat)
        
        // 账号密码登录组件
        tilUsername = findViewById(R.id.til_username)
        etUsername = findViewById(R.id.et_username)
        tilPassword = findViewById(R.id.til_password)
        etPassword = findViewById(R.id.et_password)
        cbRememberPassword = findViewById(R.id.cb_remember_password)
        
        // 登录表单容器
        layoutPhoneForm = findViewById(R.id.layout_phone_form)
        layoutAccountForm = findViewById(R.id.layout_account_form)
        
        // 登录方式切换
        tvPhoneLogin = findViewById(R.id.tv_phone_login)
        tvAccountLogin = findViewById(R.id.tv_account_login)
        
        // 注册入口文本
        tvRegisterPrompt = findViewById(R.id.tv_register_prompt)
        
        // 设置"立即注册"为蓝色
        val text = "没有账号？立即注册"
        val spannableString = SpannableString(text)
        val blueColor = ContextCompat.getColor(this, android.R.color.holo_blue_dark)
        spannableString.setSpan(
            ForegroundColorSpan(blueColor),
            5, // "立即注册" 开始位置
            text.length, // 结束位置
            Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        tvRegisterPrompt.text = spannableString
        
        // 加载保存的账号密码
        loadSavedCredentials()
    }
    
    /**
     * 设置点击事件
     */
    private fun setupListeners() {
        // 获取验证码
        btnGetCode.setOnClickListener {
            if (validatePhone()) {
                getVerificationCode()
            }
        }
        
        // 登录
        btnLogin.setOnClickListener {
            if (isPhoneLogin) {
                // 手机验证码登录
                if (validatePhone() && validateCode()) {
                    loginWithPhone()
                }
            } else {
                // 账号密码登录
                if (validateUsername() && validatePassword()) {
                    loginWithAccount()
                }
            }
        }
        
        // 切换到手机验证码登录
        tvPhoneLogin.setOnClickListener {
            if (!isPhoneLogin) {
                switchToPhoneLogin()
            }
        }
        
        // 切换到账号密码登录
        tvAccountLogin.setOnClickListener {
            if (isPhoneLogin) {
                switchToAccountLogin()
            }
        }
        
        // 微信登录
        layoutWechat.setOnClickListener {
            Toast.makeText(this, "微信登录功能开发中", Toast.LENGTH_SHORT).show()
        }
        
        // 注册入口
        tvRegisterPrompt.setOnClickListener {
            showRegisterDialog()
        }
    }
    
    /**
     * 切换到手机验证码登录
     */
    private fun switchToPhoneLogin() {
        isPhoneLogin = true
        
        // 更新UI
        layoutPhoneForm.visibility = View.VISIBLE
        layoutAccountForm.visibility = View.GONE
        
        // 更新切换按钮样式
        tvPhoneLogin.setBackgroundResource(R.drawable.toggle_selected)
        tvPhoneLogin.setTextColor(ContextCompat.getColor(this, android.R.color.white))
        tvPhoneLogin.setTypeface(null, android.graphics.Typeface.BOLD)
        
        tvAccountLogin.setBackgroundResource(R.drawable.toggle_unselected)
        tvAccountLogin.setTextColor(ContextCompat.getColor(this, android.R.color.darker_gray))
        tvAccountLogin.setTypeface(null, android.graphics.Typeface.NORMAL)
    }
    
    /**
     * 切换到账号密码登录
     */
    private fun switchToAccountLogin() {
        isPhoneLogin = false
        
        // 更新UI
        layoutPhoneForm.visibility = View.GONE
        layoutAccountForm.visibility = View.VISIBLE
        
        // 更新切换按钮样式
        tvAccountLogin.setBackgroundResource(R.drawable.toggle_selected)
        tvAccountLogin.setTextColor(ContextCompat.getColor(this, android.R.color.white))
        tvAccountLogin.setTypeface(null, android.graphics.Typeface.BOLD)
        
        tvPhoneLogin.setBackgroundResource(R.drawable.toggle_unselected)
        tvPhoneLogin.setTextColor(ContextCompat.getColor(this, android.R.color.darker_gray))
        tvPhoneLogin.setTypeface(null, android.graphics.Typeface.NORMAL)
    }
    
    /**
     * 校验手机号
     */
    private fun validatePhone(): Boolean {
        val phone = etPhone.text.toString().trim()
        
        when {
            TextUtils.isEmpty(phone) -> {
                tilPhone.error = "请输入手机号"
                return false
            }
            phone.length != 11 -> {
                tilPhone.error = "请输入正确的手机号"
                return false
            }
            else -> {
                tilPhone.error = null
                return true
            }
        }
    }
    
    /**
     * 校验验证码
     */
    private fun validateCode(): Boolean {
        val code = etCode.text.toString().trim()
        
        when {
            TextUtils.isEmpty(code) -> {
                tilCode.error = "请输入验证码"
                return false
            }
            code.length != 6 -> {
                tilCode.error = "请输入6位验证码"
                return false
            }
            else -> {
                tilCode.error = null
                return true
            }
        }
    }
    
    /**
     * 校验用户名
     */
    private fun validateUsername(): Boolean {
        val username = etUsername.text.toString().trim()
        
        when {
            TextUtils.isEmpty(username) -> {
                tilUsername.error = "请输入用户名"
                return false
            }
            else -> {
                tilUsername.error = null
                return true
            }
        }
    }
    
    /**
     * 校验密码
     */
    private fun validatePassword(): Boolean {
        val password = etPassword.text.toString().trim()
        
        when {
            TextUtils.isEmpty(password) -> {
                tilPassword.error = "请输入密码"
                return false
            }
            password.length < 6 -> {
                tilPassword.error = "密码长度不能少于6位"
                return false
            }
            else -> {
                tilPassword.error = null
                return true
            }
        }
    }
    
    /**
     * 获取验证码
     */
    private fun getVerificationCode() {
        if (isCountingDown) {
            return
        }
        
        val phone = etPhone.text.toString().trim()
        
        // 先检查手机号是否已注册
        RetrofitManager.apiService.checkPhoneExists(phone).enqueue(object : Callback<BaseResponse<Any>> {
            override fun onResponse(call: Call<BaseResponse<Any>>, response: Response<BaseResponse<Any>>) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null) {
                        if (baseResponse.code == 200) {
                            // 手机号已注册，可以发送验证码
                            sendVerificationCode(phone)
                        } else if (baseResponse.code == 234) {
                            // 用户不存在，提示注册
                            Toast.makeText(this@LoginActivity, "该手机号未注册，请先注册账号", Toast.LENGTH_SHORT).show()
                        } else {
                            // 其他错误
                            Toast.makeText(this@LoginActivity, baseResponse.msg, Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    // 请求失败
                    Toast.makeText(this@LoginActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                // 网络错误
                Log.e(TAG, "检查手机号网络错误", t)
                Toast.makeText(this@LoginActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 发送验证码
     */
    private fun sendVerificationCode(phone: String) {
        // 调用API获取验证码
        RetrofitManager.apiService.generateCode(phone).enqueue(object : Callback<BaseResponse<Any>> {
            override fun onResponse(call: Call<BaseResponse<Any>>, response: Response<BaseResponse<Any>>) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 获取验证码成功
                        Toast.makeText(this@LoginActivity, "验证码已发送", Toast.LENGTH_SHORT).show()
                        
                        // 开始倒计时
                        startCountDown()
                    } else {
                        // 获取验证码失败
                        Toast.makeText(this@LoginActivity, baseResponse?.msg ?: "获取验证码失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    // 请求失败
                    Toast.makeText(this@LoginActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                // 网络错误
                Log.e(TAG, "获取验证码网络错误", t)
                Toast.makeText(this@LoginActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 开始倒计时
     */
    private fun startCountDown() {
        isCountingDown = true
        btnGetCode.isEnabled = false
        
        countDownTimer = object : CountDownTimer(COUNTDOWN_TIME, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val secondsRemaining = millisUntilFinished / 1000
                btnGetCode.text = "${secondsRemaining}秒后重新获取"
            }
            
            override fun onFinish() {
                resetCountDown()
            }
        }.start()
    }
    
    /**
     * 重置倒计时
     */
    private fun resetCountDown() {
        isCountingDown = false
        btnGetCode.isEnabled = true
        btnGetCode.text = "获取验证码"
        countDownTimer?.cancel()
    }
    
    /**
     * 手机验证码登录
     */
    private fun loginWithPhone() {
        val phone = etPhone.text.toString().trim()
        val code = etCode.text.toString().trim()
        
        // 创建登录请求
        val loginRequest = LoginRequest(
            type = "phone", 
            phone = phone, 
            code = code
        )
        
        // 调用登录API
        login(loginRequest)
    }
    
    /**
     * 账号密码登录
     */
    private fun loginWithAccount() {
        val username = etUsername.text.toString().trim()
        val password = etPassword.text.toString().trim()
        
        // 调用API直接进行登录尝试
        accountLogin(username, password)
    }
    
    /**
     * 账号密码登录API调用
     */
    private fun accountLogin(username: String, password: String) {
        // 创建登录请求
        val loginRequest = LoginRequest(
            type = "Login", 
            phone = username,
            password = password
        )
        
        // 调用登录API
        RetrofitManager.apiService.register(loginRequest).enqueue(object : Callback<BaseResponse<LoginResponse>> {
            override fun onResponse(call: Call<BaseResponse<LoginResponse>>, response: Response<BaseResponse<LoginResponse>>) {
                try {
                    if (response.isSuccessful) {
                        val baseResponse = response.body()
                        if (baseResponse != null) {
                            when (baseResponse.code) {
                                200 -> {
                                    // 登录成功，获取用户ID和token
                                    val userId = baseResponse.data.userid
                                    val token = baseResponse.data.tokne
                                    Log.d(TAG, "登录成功: userId=$userId, token=$token")
                                    
                                    // 保存用户ID和token
                                    saveUserInfo(userId, token)
                                }
                                else -> {
                                    // 其他错误
                                    Toast.makeText(this@LoginActivity, baseResponse.msg, Toast.LENGTH_SHORT).show()
                                }
                            }
                        }
                    } else {
                        // 尝试解析错误响应
                        val errorBody = response.errorBody()?.string()
                        Log.d(TAG, "登录失败响应: $errorBody")
                        
                        if (response.code() == 422) {
                            // 账号或密码错误
                            Toast.makeText(this@LoginActivity, "账号或密码错误", Toast.LENGTH_SHORT).show()
                        } else {
                            // 其他HTTP错误
                            Toast.makeText(this@LoginActivity, "登录请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                        }
                    }
                } catch (e: Exception) {
                    // 捕获JSON解析异常
                    Log.e(TAG, "登录响应解析错误", e)
                    
                    if (response.code() == 422) {
                        // 当返回422时，通常是账号或密码错误
                        Toast.makeText(this@LoginActivity, "账号或密码错误", Toast.LENGTH_SHORT).show()
                    } else {
                        Toast.makeText(this@LoginActivity, "登录失败: ${e.message}", Toast.LENGTH_SHORT).show()
                    }
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<LoginResponse>>, t: Throwable) {
                // 网络错误
                Log.e(TAG, "登录网络错误", t)
                
                if (t is com.google.gson.JsonSyntaxException) {
                    // JSON解析错误，可能是账号或密码错误导致返回格式不一致
                    Toast.makeText(this@LoginActivity, "账号或密码错误", Toast.LENGTH_SHORT).show()
                } else {
                    Toast.makeText(this@LoginActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
                }
            }
        })
    }
    
    /**
     * 登录
     */
    private fun login(loginRequest: LoginRequest) {
        if (loginRequest.type == "Login") {
            // 对于账号密码登录，使用特殊处理
            accountLogin(loginRequest.phone ?: "", loginRequest.password ?: "")
            return
        }
        
        // 手机验证码登录
        RetrofitManager.apiService.register(loginRequest).enqueue(object : Callback<BaseResponse<LoginResponse>> {
            override fun onResponse(call: Call<BaseResponse<LoginResponse>>, response: Response<BaseResponse<LoginResponse>>) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null) {
                        when (baseResponse.code) {
                            200 -> {
                                // 登录成功，获取用户ID和token
                                val userId = baseResponse.data.userid
                                val token = baseResponse.data.tokne
                                Log.d(TAG, "登录成功: userId=$userId, token=$token")
                                
                                // 保存用户ID和token
                                saveUserInfo(userId, token)
                            }
                            422 -> {
                                // 验证码错误
                                Toast.makeText(this@LoginActivity, "验证码验证失败，请稍后再试", Toast.LENGTH_SHORT).show()
                            }
                            else -> {
                                // 其他错误
                                Toast.makeText(this@LoginActivity, baseResponse.msg, Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                } else {
                    // 请求失败
                    Toast.makeText(this@LoginActivity, "登录请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<LoginResponse>>, t: Throwable) {
                // 网络错误
                Log.e(TAG, "登录网络错误", t)
                Toast.makeText(this@LoginActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 保存用户信息
     */
    private fun saveUserInfo(userId: Int, token: String?) {
        val sharedPreferences = getSharedPreferences("app_prefs", MODE_PRIVATE)
        val editor = sharedPreferences.edit()
        editor.putInt("user_id", userId)
        if (token != null) {
            editor.putString("token", token)
        }
        // 保存当前登录的手机号
        val phoneNumber = etUsername.text.toString().trim()
        editor.putString("phone", phoneNumber)
        
        // 如果是账号密码登录且勾选了记住密码，则保存账号密码
        if (!isPhoneLogin && cbRememberPassword.isChecked) {
            val username = etUsername.text.toString().trim()
            val password = etPassword.text.toString().trim()
            editor.putString("saved_username", username)
            editor.putString("saved_password", password)
            editor.putBoolean("remember_password", true)
            Log.d(TAG, "保存账号密码: username=$username")
        } else {
            // 清除保存的密码
            editor.remove("saved_password")
            editor.putBoolean("remember_password", false)
        }
        
        editor.apply()
        
        Log.d(TAG, "保存用户信息: userId=$userId, phone=$phoneNumber")
        
        // 跳转到设备列表界面
        startActivity(Intent(this, DeviceListActivity::class.java))
        finish()
    }
    
    /**
     * 显示注册对话框
     */
    private fun showRegisterDialog() {
        // 创建注册对话框布局
        val dialogView = layoutInflater.inflate(R.layout.dialog_register, null)
        
        // 获取对话框中的控件
        val tilRegPhone = dialogView.findViewById<TextInputLayout>(R.id.til_register_phone)
        val etRegPhone = dialogView.findViewById<TextInputEditText>(R.id.et_register_phone)
        val tilRegCode = dialogView.findViewById<TextInputLayout>(R.id.til_register_code)
        val etRegCode = dialogView.findViewById<TextInputEditText>(R.id.et_register_code)
        val btnRegGetCode = dialogView.findViewById<Button>(R.id.btn_register_get_code)
        val tilRegPassword = dialogView.findViewById<TextInputLayout>(R.id.til_register_password)
        val etRegPassword = dialogView.findViewById<TextInputEditText>(R.id.et_register_password)
        val tilRegConfirmPassword = dialogView.findViewById<TextInputLayout>(R.id.til_register_confirm_password)
        val etRegConfirmPassword = dialogView.findViewById<TextInputEditText>(R.id.et_register_confirm_password)
        val btnRegister = dialogView.findViewById<Button>(R.id.btn_register)
        
        // 创建对话框
        val builder = AlertDialog.Builder(this)
        builder.setView(dialogView)
        builder.setCancelable(true)
        
        registerDialog = builder.create()
        registerDialog?.show()
        
        // 获取验证码按钮点击事件
        btnRegGetCode.setOnClickListener {
            val phone = etRegPhone.text.toString().trim()
            
            // 验证手机号
            when {
                TextUtils.isEmpty(phone) -> {
                    tilRegPhone.error = "请输入手机号"
                }
                phone.length != 11 -> {
                    tilRegPhone.error = "请输入正确的手机号"
                }
                else -> {
                    tilRegPhone.error = null
                    
                    if (!isRegisterCountingDown) {
                        // 检查手机号是否已存在
                        checkPhoneExistsForRegister(phone, btnRegGetCode)
                    }
                }
            }
        }
        
        // 注册按钮点击事件
        btnRegister.setOnClickListener {
            val phone = etRegPhone.text.toString().trim()
            val code = etRegCode.text.toString().trim()
            val password = etRegPassword.text.toString().trim()
            val confirmPassword = etRegConfirmPassword.text.toString().trim()
            
            // 验证输入
            var isValid = true
            
            when {
                TextUtils.isEmpty(phone) -> {
                    tilRegPhone.error = "请输入手机号"
                    isValid = false
                }
                phone.length != 11 -> {
                    tilRegPhone.error = "请输入正确的手机号"
                    isValid = false
                }
                else -> {
                    tilRegPhone.error = null
                }
            }
            
            when {
                TextUtils.isEmpty(code) -> {
                    tilRegCode.error = "请输入验证码"
                    isValid = false
                }
                code.length != 6 -> {
                    tilRegCode.error = "请输入6位验证码"
                    isValid = false
                }
                else -> {
                    tilRegCode.error = null
                }
            }
            
            when {
                TextUtils.isEmpty(password) -> {
                    tilRegPassword.error = "请输入密码"
                    isValid = false
                }
                password.length < 6 -> {
                    tilRegPassword.error = "密码长度不能少于6位"
                    isValid = false
                }
                else -> {
                    tilRegPassword.error = null
                }
            }
            
            when {
                TextUtils.isEmpty(confirmPassword) -> {
                    tilRegConfirmPassword.error = "请确认密码"
                    isValid = false
                }
                confirmPassword != password -> {
                    tilRegConfirmPassword.error = "两次输入的密码不一致"
                    isValid = false
                }
                else -> {
                    tilRegConfirmPassword.error = null
                }
            }
            
            if (isValid) {
                // 执行注册
                register(phone, password, code)
            }
        }
    }
    
    /**
     * 检查手机号是否已存在（注册用）
     */
    private fun checkPhoneExistsForRegister(phone: String, btnGetCode: Button) {
        RetrofitManager.apiService.checkPhoneExists(phone).enqueue(object : Callback<BaseResponse<Any>> {
            override fun onResponse(call: Call<BaseResponse<Any>>, response: Response<BaseResponse<Any>>) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null) {
                        if (baseResponse.code == 200) {
                            // 手机号已存在
                            Toast.makeText(this@LoginActivity, "此账号已存在，请前去登录", Toast.LENGTH_SHORT).show()
                            registerDialog?.dismiss()
                        } else if (baseResponse.code == 234) {
                            // 用户不存在，可以注册，发送验证码
                            sendRegisterVerificationCode(phone, btnGetCode)
                        } else {
                            // 其他错误
                            Toast.makeText(this@LoginActivity, baseResponse.msg, Toast.LENGTH_SHORT).show()
                        }
                    }
                } else {
                    // 请求失败
                    Toast.makeText(this@LoginActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                // 网络错误
                Log.e(TAG, "检查手机号网络错误", t)
                Toast.makeText(this@LoginActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 发送注册验证码
     */
    private fun sendRegisterVerificationCode(phone: String, btnGetCode: Button) {
        RetrofitManager.apiService.generateCode(phone).enqueue(object : Callback<BaseResponse<Any>> {
            override fun onResponse(call: Call<BaseResponse<Any>>, response: Response<BaseResponse<Any>>) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 获取验证码成功
                        Toast.makeText(this@LoginActivity, "验证码已发送", Toast.LENGTH_SHORT).show()
                        
                        // 开始注册倒计时
                        startRegisterCountDown(btnGetCode)
                    } else {
                        // 获取验证码失败
                        Toast.makeText(this@LoginActivity, baseResponse?.msg ?: "获取验证码失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    // 请求失败
                    Toast.makeText(this@LoginActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<Any>>, t: Throwable) {
                // 网络错误
                Log.e(TAG, "获取验证码网络错误", t)
                Toast.makeText(this@LoginActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 开始注册倒计时
     */
    private fun startRegisterCountDown(btnGetCode: Button) {
        isRegisterCountingDown = true
        btnGetCode.isEnabled = false
        
        registerCountDownTimer = object : CountDownTimer(COUNTDOWN_TIME, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                val secondsRemaining = millisUntilFinished / 1000
                btnGetCode.text = "${secondsRemaining}秒后重新获取"
            }
            
            override fun onFinish() {
                resetRegisterCountDown(btnGetCode)
            }
        }.start()
    }
    
    /**
     * 重置注册倒计时
     */
    private fun resetRegisterCountDown(btnGetCode: Button) {
        isRegisterCountingDown = false
        btnGetCode.isEnabled = true
        btnGetCode.text = "获取验证码"
        registerCountDownTimer?.cancel()
    }
    
    /**
     * 注册请求
     */
    private fun register(phone: String, password: String, code: String) {
        val registerRequest = LoginRequest(
            type = "password",
            phone = phone,
            password = password,
            code = code
        )
        
        RetrofitManager.apiService.register(registerRequest).enqueue(object : Callback<BaseResponse<LoginResponse>> {
            override fun onResponse(call: Call<BaseResponse<LoginResponse>>, response: Response<BaseResponse<LoginResponse>>) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null) {
                        when (baseResponse.code) {
                            200 -> {
                                // 注册成功，获取用户ID和token
                                val userId = baseResponse.data.userid
                                val token = baseResponse.data.tokne
                                Log.d(TAG, "注册成功: userId=$userId, token=$token")
                                
                                Toast.makeText(this@LoginActivity, "注册成功", Toast.LENGTH_SHORT).show()
                                
                                // 关闭注册对话框
                                registerDialog?.dismiss()
                                
                                // 保存用户ID和token
                                saveUserInfo(userId, token)
                            }
                            422 -> {
                                // 验证码错误
                                Toast.makeText(this@LoginActivity, "验证码验证失败，请稍后再试", Toast.LENGTH_SHORT).show()
                            }
                            else -> {
                                // 其他错误
                                Toast.makeText(this@LoginActivity, baseResponse.msg, Toast.LENGTH_SHORT).show()
                            }
                        }
                    }
                } else {
                    // 请求失败
                    Toast.makeText(this@LoginActivity, "注册请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<LoginResponse>>, t: Throwable) {
                // 网络错误
                Log.e(TAG, "注册网络错误", t)
                Toast.makeText(this@LoginActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    override fun onDestroy() {
        super.onDestroy()
        // 取消倒计时
        countDownTimer?.cancel()
        registerCountDownTimer?.cancel()
    }
    
    /**
     * 加载保存的账号密码
     */
    private fun loadSavedCredentials() {
        val sharedPreferences = getSharedPreferences("app_prefs", MODE_PRIVATE)
        val rememberPassword = sharedPreferences.getBoolean("remember_password", false)
        
        if (rememberPassword) {
            val savedUsername = sharedPreferences.getString("saved_username", "")
            val savedPassword = sharedPreferences.getString("saved_password", "")
            
            if (!savedUsername.isNullOrEmpty() && !savedPassword.isNullOrEmpty()) {
                // 填充账号密码
                etUsername.setText(savedUsername)
                etPassword.setText(savedPassword)
                cbRememberPassword.isChecked = true
                
                Log.d(TAG, "加载保存的账号密码: username=$savedUsername")
            }
        }
    }
} 