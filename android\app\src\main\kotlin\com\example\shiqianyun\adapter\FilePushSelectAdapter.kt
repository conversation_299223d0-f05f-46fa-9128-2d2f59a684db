package com.example.shiqianyun.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.CheckBox
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.network.model.DeviceInfo

class FilePushSelectAdapter(
    private val devices: List<DeviceInfo>,
    private val onSelectionChanged: () -> Unit
) : RecyclerView.Adapter<FilePushSelectAdapter.DeviceViewHolder>() {

    private val selectedDevices = mutableSetOf<DeviceInfo>()

    fun getSelectedDevices(): Set<DeviceInfo> = selectedDevices.toSet()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): DeviceViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(com.example.shiqianyun.R.layout.item_batch_restart_device, parent, false)
        return DeviceViewHolder(view)
    }

    override fun onBindViewHolder(holder: DeviceViewHolder, position: Int) {
        val device = devices[position]
        holder.bind(device)
    }

    override fun getItemCount(): Int = devices.size

    inner class DeviceViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val cbDevice: CheckBox = itemView.findViewById(com.example.shiqianyun.R.id.checkbox)
        private val tvDeviceName: TextView = itemView.findViewById(com.example.shiqianyun.R.id.tv_device_name)
        private val tvDeviceConfig: TextView = itemView.findViewById(com.example.shiqianyun.R.id.tv_device_info)
        private val tvDeviceStatus: TextView = itemView.findViewById(com.example.shiqianyun.R.id.tv_status)

        fun bind(device: DeviceInfo) {
            // 设备名称 - 使用nick_name
            tvDeviceName.text = device.nick_name.takeIf { it.isNotEmpty() } 
                ?: "我的设备"

            // 设备配置信息 - 格式：vip等级 | android版本 | 设备编码
            tvDeviceConfig.text = "${device.vip_level} | ${device.android_version} | ${device.device_designation ?: device.phone_id}"

            // 设备状态
            val statusText = if (device.online_status) "在线" else "离线"
            tvDeviceStatus.text = statusText

            // 设备状态颜色
            val statusColor = if (device.online_status) {
                android.R.color.holo_green_light
            } else {
                android.R.color.darker_gray
            }
            tvDeviceStatus.setTextColor(
                itemView.context.resources.getColor(statusColor, null)
            )

            // 设置选中状态
            cbDevice.isChecked = selectedDevices.contains(device)

            // 设置点击事件
            itemView.setOnClickListener {
                toggleSelection(device)
            }

            cbDevice.setOnClickListener {
                toggleSelection(device)
            }
        }

        private fun toggleSelection(device: DeviceInfo) {
            if (selectedDevices.contains(device)) {
                selectedDevices.remove(device)
                cbDevice.isChecked = false
            } else {
                selectedDevices.add(device)
                cbDevice.isChecked = true
            }
            onSelectionChanged()
        }
    }
}