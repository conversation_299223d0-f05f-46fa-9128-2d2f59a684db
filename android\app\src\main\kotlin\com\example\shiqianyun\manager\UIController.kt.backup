package com.example.shiqianyun.manager

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.DialogInterface
import android.content.res.Configuration
import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.CountDownTimer
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.ViewConfiguration
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.constraintlayout.widget.ConstraintLayout
import com.example.shiqianyun.R
import com.example.shiqianyun.network.model.BitrateConfig
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.*
import android.media.MediaScannerConnection
import android.view.SurfaceView
import android.view.MotionEvent

class UIController(
    private val activity: Activity,
    private val onKeyEvent: (Int) -> Unit,
    private val onVolumeUp: () -> Unit,
    private val onVolumeDown: () -> Unit,
    private val onQualityChange: (BitrateConfig) -> Unit,
    private val onReconnect: () -> Unit,
    private val onExit: () -> Unit,
    private val onGetClipboard: () -> Unit,
    private val onSetClipboard: () -> Unit,
    private val onContinueActivity: () -> Unit,
    private val onExitInactivity: () -> Unit,
    private val onLayoutChanged: () -> Unit = {}
) {
    companion object {
        private const val TAG = "UIController"
        private const val MODE_NORMAL = 0
        private const val MODE_FULLSCREEN = 1
        private const val INACTIVITY_COUNTDOWN_SECONDS = 5
    }

    // UI组件
    private lateinit var surfaceView: SurfaceView
    private var currentMode = MODE_NORMAL
    
    // 比特率配置
    private var bitrateConfigs: List<BitrateConfig> = emptyList()
    private var currentBitrateConfig: BitrateConfig? = null
    
    // 未活动提示相关
    private var inactivityCountdownTimer: CountDownTimer? = null

    /**
     * 初始化UI组件
     */
    fun initViews() {
        surfaceView = activity.findViewById(R.id.surface_view)
        
        setupNavigationButtons()
        setupControlButtons()
        setupModeToggle()
    }

    /**
     * 设置状态栏
     */
    fun setupStatusBar(deviceName: String) {
        activity.findViewById<TextView>(R.id.tv_device_name).text = deviceName
        updateLatencyDisplay(0)
        
        activity.findViewById<LinearLayout>(R.id.status_quality_container).setOnClickListener {
            showResolutionSelector()
        }
        
        val qualityName = currentBitrateConfig?.name ?: "标清"
        activity.findViewById<TextView>(R.id.status_tv_quality).text = qualityName
        
        activity.findViewById<TextView>(R.id.status_tv_quality).setOnClickListener {
            showResolutionSelector()
        }
    }

    /**
     * 更新延迟显示
     */
    fun updateLatencyDisplay(latencyMs: Int) {
        activity.runOnUiThread {
            activity.findViewById<TextView>(R.id.tv_latency).text = "${latencyMs}ms"
            activity.findViewById<TextView>(R.id.overlay_header_latency)?.text = "${latencyMs}ms"
        }
    }

    /**
     * 设置导航按钮
     */
    private fun setupNavigationButtons() {
        // 底部导航栏按钮
        activity.findViewById<ImageButton>(R.id.btn_back).setOnClickListener {
            onKeyEvent(4) // KEYCODE_BACK
        }

        activity.findViewById<ImageButton>(R.id.btn_home).setOnClickListener {
            onKeyEvent(3) // KEYCODE_HOME
        }

        activity.findViewById<ImageButton>(R.id.btn_recent).setOnClickListener {
            onKeyEvent(187) // KEYCODE_APP_SWITCH
        }

        // 左侧导航栏按钮
        activity.findViewById<ImageButton>(R.id.btn_back_left).setOnClickListener {
            onKeyEvent(4) // KEYCODE_BACK
        }

        activity.findViewById<ImageButton>(R.id.btn_home_left).setOnClickListener {
            onKeyEvent(3) // KEYCODE_HOME
        }

        activity.findViewById<ImageButton>(R.id.btn_recent_left).setOnClickListener {
            onKeyEvent(187) // KEYCODE_APP_SWITCH
        }
    }

    /**
     * 设置控制按钮
     */
    private fun setupControlButtons() {
        activity.findViewById<ImageButton>(R.id.btn_volume_up).setOnClickListener {
            onVolumeUp()
        }

        activity.findViewById<ImageButton>(R.id.btn_volume_down).setOnClickListener {
            onVolumeDown()
        }

        activity.findViewById<ImageButton>(R.id.btn_power).setOnClickListener {
            onKeyEvent(26) // KEYCODE_POWER
        }
        
        activity.findViewById<ImageButton>(R.id.btn_screenshot).setOnClickListener {
            captureScreenshot()
        }

        activity.findViewById<ImageButton>(R.id.btn_exit_right).setOnClickListener {
            onExit()
        }

        activity.findViewById<ImageButton>(R.id.btn_reconnect).setOnClickListener {
            onReconnect()
        }

        activity.findViewById<ImageButton>(R.id.btn_clipboard_copy).setOnClickListener {
            onGetClipboard()
        }

        activity.findViewById<ImageButton>(R.id.btn_clipboard_paste).setOnClickListener {
            onSetClipboard()
        }
    }

    /**
     * 设置模式切换功能
     */
    private fun setupModeToggle() {
        activity.findViewById<ImageButton>(R.id.btn_toggle_mode).setOnClickListener {
            toggleDisplayMode()
        }
        
        val floatingBallContainer = activity.findViewById<FrameLayout>(R.id.floating_ball_container)
        floatingBallContainer.setOnClickListener {
            showToolbarOverlay()
        }
        
        // 添加悬浮球拖动功能
        floatingBallContainer.setOnTouchListener(createFloatingBallTouchListener())
        
        activity.findViewById<LinearLayout>(R.id.overlay_btn_exit).setOnClickListener {
            hideToolbarOverlay()
        }
        
        setupToolbarOverlayButtons()
    }

    /**
     * 创建悬浮球触摸监听器
     */
    @SuppressLint("ClickableViewAccessibility")
    private fun createFloatingBallTouchListener(): View.OnTouchListener {
        return object : View.OnTouchListener {
            private var lastX: Float = 0f
            private var lastY: Float = 0f
            private var isDragging = false
            private val touchSlop = ViewConfiguration.get(activity).scaledTouchSlop
            private var initialX = 0f
            private var initialY = 0f

            override fun onTouch(v: View, event: MotionEvent): Boolean {
                when (event.action) {
                    MotionEvent.ACTION_DOWN -> {
                        lastX = event.rawX
                        lastY = event.rawY
                        initialX = event.rawX
                        initialY = event.rawY
                        isDragging = false
                        return true
                    }
                    MotionEvent.ACTION_MOVE -> {
                        val deltaX = event.rawX - lastX
                        val deltaY = event.rawY - lastY
                        
                        if (!isDragging && (Math.abs(event.rawX - initialX) > touchSlop || 
                                           Math.abs(event.rawY - initialY) > touchSlop)) {
                            isDragging = true
                            
                            val layoutParams = v.layoutParams as FrameLayout.LayoutParams
                            val location = IntArray(2)
                            v.getLocationOnScreen(location)
                            
                            val parent = v.parent as View
                            val parentLocation = IntArray(2)
                            parent.getLocationOnScreen(parentLocation)
                            
                            val relativeLeft = location[0] - parentLocation[0]
                            val relativeTop = location[1] - parentLocation[1]
                            
                            layoutParams.leftMargin = relativeLeft
                            layoutParams.topMargin = relativeTop
                            layoutParams.rightMargin = 0
                            layoutParams.bottomMargin = 0
                            layoutParams.gravity = Gravity.NO_GRAVITY
                            
                            v.layoutParams = layoutParams
                        }
                        
                        if (isDragging) {
                            val layoutParams = v.layoutParams as FrameLayout.LayoutParams
                            val parentWidth = (v.parent as View).width
                            val parentHeight = (v.parent as View).height
                            
                            layoutParams.leftMargin += deltaX.toInt()
                            layoutParams.topMargin += deltaY.toInt()
                            
                            layoutParams.leftMargin = layoutParams.leftMargin.coerceIn(0, parentWidth - v.width)
                            layoutParams.topMargin = layoutParams.topMargin.coerceIn(0, parentHeight - v.height)
                            
                            v.layoutParams = layoutParams
                        }
                        
                        lastX = event.rawX
                        lastY = event.rawY
                        return true
                    }
                    MotionEvent.ACTION_UP -> {
                        if (!isDragging) {
                            v.performClick()
                        } else {
                            saveFloatingBallPosition(v)
                        }
                        return true
                    }
                    MotionEvent.ACTION_CANCEL -> {
                        if (isDragging) {
                            saveFloatingBallPosition(v)
                        }
                        return true
                    }
                }
                return false
            }
        }
    }

    /**
     * 设置工具栏蒙版内按钮事件
     */
    private fun setupToolbarOverlayButtons() {
        activity.findViewById<LinearLayout>(R.id.overlay_btn_volume_up)?.setOnClickListener {
            onVolumeUp()
            hideToolbarOverlay(true)
        }
        
        activity.findViewById<LinearLayout>(R.id.overlay_btn_volume_down)?.setOnClickListener {
            onVolumeDown()
            hideToolbarOverlay(true)
        }
        
        activity.findViewById<LinearLayout>(R.id.overlay_btn_clipboard_copy)?.setOnClickListener {
            hideToolbarOverlay()
            onGetClipboard()
        }
        
        activity.findViewById<LinearLayout>(R.id.overlay_btn_screenshot)?.setOnClickListener {
            hideToolbarOverlay()
            captureScreenshot()
        }
        
        activity.findViewById<LinearLayout>(R.id.overlay_btn_upload)?.setOnClickListener {
            hideToolbarOverlay()
            Toast.makeText(activity, "上传功能开发中", Toast.LENGTH_SHORT).show()
        }
        
        activity.findViewById<LinearLayout>(R.id.overlay_btn_restart)?.setOnClickListener {
            hideToolbarOverlay()
            onReconnect()
        }
        
        activity.findViewById<LinearLayout>(R.id.overlay_btn_normal_mode)?.setOnClickListener {
            hideToolbarOverlay()
            toggleDisplayMode()
        }
        
        activity.findViewById<LinearLayout>(R.id.overlay_btn_exit)?.setOnClickListener {
            hideToolbarOverlay()
            onExit()
        }
        
        // 导航按钮
        activity.findViewById<ImageButton>(R.id.overlay_btn_back)?.setOnClickListener {
            hideToolbarOverlay()
            onKeyEvent(4) // KEYCODE_BACK
        }
        
        activity.findViewById<ImageButton>(R.id.overlay_btn_home)?.setOnClickListener {
            hideToolbarOverlay()
            onKeyEvent(3) // KEYCODE_HOME
        }
        
        activity.findViewById<ImageButton>(R.id.overlay_btn_recent)?.setOnClickListener {
            hideToolbarOverlay()
            onKeyEvent(187) // KEYCODE_APP_SWITCH
        }
        
        activity.findViewById<TextView>(R.id.overlay_header_quality)?.setOnClickListener {
            hideToolbarOverlay()
            showResolutionSelector()
        }
        
        activity.findViewById<LinearLayout>(R.id.overlay_btn_quality)?.setOnClickListener {
            hideToolbarOverlay()
            showResolutionSelector()
        }
    }

    /**
     * 显示分辨率选择对话框
     */
    private fun showResolutionSelector() {
        if (bitrateConfigs.isEmpty()) {
            Toast.makeText(activity, "无可用画质配置", Toast.LENGTH_SHORT).show()
            return
        }
        
        val options = bitrateConfigs.map { it.name }.toTypedArray()
        
        AlertDialog.Builder(activity)
            .setTitle("选择清晰度")
            .setItems(options) { _: DialogInterface, which: Int ->
                val selectedConfig = bitrateConfigs[which]
                currentBitrateConfig = selectedConfig
                onQualityChange(selectedConfig)
                updateQualityDisplay(selectedConfig.name)
            }
            .show()
    }

    /**
     * 更新画质显示
     */
    fun updateQualityDisplay(qualityName: String) {
        activity.runOnUiThread {
            activity.findViewById<TextView>(R.id.status_tv_quality)?.text = qualityName
            activity.findViewById<TextView>(R.id.overlay_tv_quality)?.text = qualityName
            activity.findViewById<TextView>(R.id.overlay_header_quality)?.text = qualityName
        }
    }

    /**
     * 设置比特率配置
     */
    fun setBitrateConfigs(configs: List<BitrateConfig>) {
        bitrateConfigs = configs
        currentBitrateConfig = if (configs.size > 1) configs[1] else configs.firstOrNull()
    }

    /**
     * 切换显示模式
     */
    private fun toggleDisplayMode() {
        if (currentMode == MODE_NORMAL) {
            switchToFullscreenMode()
        } else {
            switchToNormalMode()
        }
    }

    /**
     * 切换到全屏模式
     */
    private fun switchToFullscreenMode() {
        currentMode = MODE_FULLSCREEN
        
        Log.d(TAG, "切换到全屏模式开始")
        
        // 隐藏所有UI元素
        activity.findViewById<LinearLayout>(R.id.status_bar).visibility = View.GONE
        activity.findViewById<ScrollView>(R.id.control_scroll).visibility = View.GONE
        activity.findViewById<LinearLayout>(R.id.navigation_layout).visibility = View.GONE
        activity.findViewById<LinearLayout>(R.id.navigation_layout_left).visibility = View.GONE
        
        // 重新设置video_layout为全屏约束
        val videoLayout = activity.findViewById<FrameLayout>(R.id.video_layout)
        val params = videoLayout.layoutParams as ConstraintLayout.LayoutParams
        
        // 清除所有约束
        params.topToTop = ConstraintLayout.LayoutParams.UNSET
        params.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
        params.leftToLeft = ConstraintLayout.LayoutParams.UNSET
        params.rightToRight = ConstraintLayout.LayoutParams.UNSET
        params.leftToRight = ConstraintLayout.LayoutParams.UNSET
        params.rightToLeft = ConstraintLayout.LayoutParams.UNSET
        params.topToBottom = ConstraintLayout.LayoutParams.UNSET
        params.bottomToTop = ConstraintLayout.LayoutParams.UNSET
        
        // 设置全屏约束
        params.topToTop = ConstraintLayout.LayoutParams.PARENT_ID
        params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
        params.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID
        params.rightToRight = ConstraintLayout.LayoutParams.PARENT_ID
        
        videoLayout.layoutParams = params
        videoLayout.requestLayout()
        
        // 显示悬浮球
        activity.findViewById<FrameLayout>(R.id.floating_ball_container).visibility = View.VISIBLE
        
        // 等待布局完成后再调整SurfaceView
        videoLayout.post {
            Log.d(TAG, "全屏布局完成，开始调整SurfaceView")
            
            // 打印当前video_layout的实际位置和尺寸
            val location = IntArray(2)
            videoLayout.getLocationOnScreen(location)
            Log.d(TAG, "全屏video_layout位置: x=${location[0]}, y=${location[1]}, width=${videoLayout.width}, height=${videoLayout.height}")
            
            onLayoutChanged()
        }
    }

    /**
     * 切换到普通模式
     */
    private fun switchToNormalMode() {
        currentMode = MODE_NORMAL
        
        Log.d(TAG, "切换到普通模式开始")
        
        // 显示状态栏
        activity.findViewById<LinearLayout>(R.id.status_bar).visibility = View.VISIBLE
        
        // 显示右侧工具栏
        activity.findViewById<ScrollView>(R.id.control_scroll).visibility = View.VISIBLE
        
        // 根据屏幕方向显示对应的导航栏
        val isLandscape = activity.resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
        if (isLandscape) {
            activity.findViewById<LinearLayout>(R.id.navigation_layout_left).visibility = View.VISIBLE
            activity.findViewById<LinearLayout>(R.id.navigation_layout).visibility = View.GONE
        } else {
            activity.findViewById<LinearLayout>(R.id.navigation_layout).visibility = View.VISIBLE
            activity.findViewById<LinearLayout>(R.id.navigation_layout_left).visibility = View.GONE
        }
        
        // 重新设置video_layout的约束
        val videoLayout = activity.findViewById<FrameLayout>(R.id.video_layout)
        val params = videoLayout.layoutParams as ConstraintLayout.LayoutParams
        
        // 清除所有旧约束
        params.topToTop = ConstraintLayout.LayoutParams.UNSET
        params.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
        params.leftToLeft = ConstraintLayout.LayoutParams.UNSET
        params.rightToRight = ConstraintLayout.LayoutParams.UNSET
        params.leftToRight = ConstraintLayout.LayoutParams.UNSET
        params.rightToLeft = ConstraintLayout.LayoutParams.UNSET
        params.topToBottom = ConstraintLayout.LayoutParams.UNSET
        params.bottomToTop = ConstraintLayout.LayoutParams.UNSET
        
        // 重新设置正确的约束
        if (isLandscape) {
            params.topToBottom = R.id.status_bar
            params.leftToRight = R.id.navigation_layout_left  
            params.rightToLeft = R.id.control_scroll
            params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
        } else {
            params.topToBottom = R.id.status_bar
            params.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID
            params.rightToLeft = R.id.control_scroll
            params.bottomToTop = R.id.navigation_layout
        }
        
        videoLayout.layoutParams = params
        videoLayout.requestLayout()
        
        // 隐藏悬浮球
        activity.findViewById<FrameLayout>(R.id.floating_ball_container).visibility = View.GONE
        
        // 等待布局完成后再调整SurfaceView
        videoLayout.post {
            Log.d(TAG, "普通模式布局完成，开始调整SurfaceView")
            
            // 打印当前video_layout的实际位置和尺寸
            val location = IntArray(2)
            videoLayout.getLocationOnScreen(location)
            Log.d(TAG, "普通模式video_layout位置: x=${location[0]}, y=${location[1]}, width=${videoLayout.width}, height=${videoLayout.height}")
            
            onLayoutChanged()
        }
        hideToolbarOverlay()
    }

    /**
     * 根据屏幕方向切换导航栏
     */
    fun switchNavigationLayout(orientation: Int) {
        activity.runOnUiThread {
            val bottomNavLayout = activity.findViewById<LinearLayout>(R.id.navigation_layout)
            val leftNavLayout = activity.findViewById<LinearLayout>(R.id.navigation_layout_left)
            
            // 全屏模式下，无论如何旋转都隐藏导航栏
            if (currentMode == MODE_FULLSCREEN) {
                leftNavLayout.visibility = View.GONE
                bottomNavLayout.visibility = View.GONE
                Log.d(TAG, "全屏模式：隐藏所有导航栏")
                
                // 全屏模式下旋转后也需要重新调整SurfaceView尺寸
                val videoLayout = activity.findViewById<FrameLayout>(R.id.video_layout)
                videoLayout.post {
                    Log.d(TAG, "全屏模式旋转完成，重新调整SurfaceView")
                    onLayoutChanged()
                }
                return@runOnUiThread
            }
            
            // 普通模式下根据方向显示对应的导航栏
            if (orientation == Configuration.ORIENTATION_LANDSCAPE) {
                // 横屏：显示左侧导航栏，隐藏底部导航栏
                leftNavLayout.visibility = View.VISIBLE
                bottomNavLayout.visibility = View.GONE
                
                val params = leftNavLayout.layoutParams as? ViewGroup.MarginLayoutParams
                if (params != null) {
                    params.topMargin = 0
                    leftNavLayout.layoutParams = params
                }
                
                Log.d(TAG, "普通模式横屏：显示左侧导航栏，隐藏底部导航栏")
                
                // 更新video_layout约束
                updateVideoLayoutConstraintsForLandscape()
            } else {
                // 竖屏：显示底部导航栏，隐藏左侧导航栏
                leftNavLayout.visibility = View.GONE
                bottomNavLayout.visibility = View.VISIBLE
                
                Log.d(TAG, "普通模式竖屏：显示底部导航栏，隐藏左侧导航栏")
                
                // 更新video_layout约束
                updateVideoLayoutConstraintsForPortrait()
            }
        }
    }

    /**
     * 更新video_layout约束为横屏模式
     */
    private fun updateVideoLayoutConstraintsForLandscape() {
        val videoLayout = activity.findViewById<FrameLayout>(R.id.video_layout)
        val params = videoLayout.layoutParams as? ConstraintLayout.LayoutParams
        if (params != null) {
            // 清除旧约束
            params.bottomToTop = ConstraintLayout.LayoutParams.UNSET
            params.leftToLeft = ConstraintLayout.LayoutParams.UNSET
            
            // 设置横屏约束：左边约束到左导航栏右边，底部约束到父容器底部
            params.leftToRight = R.id.navigation_layout_left
            params.bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
            
            videoLayout.layoutParams = params
            videoLayout.requestLayout()
            
            Log.d(TAG, "横屏模式：更新video_layout约束")
            
            // 等待布局完成后回调
            videoLayout.post {
                Log.d(TAG, "横屏模式布局完成")
                onLayoutChanged()
            }
        }
    }

    /**
     * 更新video_layout约束为竖屏模式
     */
    private fun updateVideoLayoutConstraintsForPortrait() {
        val videoLayout = activity.findViewById<FrameLayout>(R.id.video_layout)
        val params = videoLayout.layoutParams as? ConstraintLayout.LayoutParams
        if (params != null) {
            // 清除旧约束
            params.leftToRight = ConstraintLayout.LayoutParams.UNSET
            params.bottomToBottom = ConstraintLayout.LayoutParams.UNSET
            
            // 设置竖屏约束：左边约束到父容器左边，底部约束到底部导航栏顶部
            params.leftToLeft = ConstraintLayout.LayoutParams.PARENT_ID
            params.bottomToTop = R.id.navigation_layout
            
            videoLayout.layoutParams = params
            videoLayout.requestLayout()
            
            Log.d(TAG, "竖屏模式：更新video_layout约束")
            
            // 等待布局完成后回调
            videoLayout.post {
                Log.d(TAG, "竖屏模式布局完成")
                onLayoutChanged()
            }
        }
    }

    /**
     * 显示工具栏蒙版
     */
    private fun showToolbarOverlay() {
        val toolbarOverlay = activity.findViewById<FrameLayout>(R.id.toolbar_overlay)
        toolbarOverlay.setOnClickListener { 
            hideToolbarOverlay()
        }
        
        toolbarOverlay.visibility = View.VISIBLE
        
        // 更新显示内容
        val deviceName = activity.intent.getStringExtra("device_name") ?: "云手机"
        activity.findViewById<TextView>(R.id.overlay_header_device_name)?.text = deviceName
        
        val latencyText = activity.findViewById<TextView>(R.id.tv_latency).text.toString()
        activity.findViewById<TextView>(R.id.overlay_header_latency)?.text = latencyText
        
        val currentQualityName = currentBitrateConfig?.name ?: "标准"
        activity.findViewById<TextView>(R.id.overlay_tv_quality)?.text = currentQualityName
        activity.findViewById<TextView>(R.id.overlay_header_quality)?.text = currentQualityName
    }

    /**
     * 隐藏工具栏蒙版
     */
    private fun hideToolbarOverlay(autoShow: Boolean = false) {
        activity.findViewById<FrameLayout>(R.id.toolbar_overlay).visibility = View.GONE
        
        if (autoShow && currentMode == MODE_FULLSCREEN) {
            Handler(Looper.getMainLooper()).postDelayed({
                showToolbarOverlay()
            }, 2000)
        }
    }

    /**
     * 显示长时间未操作提示
     */
    fun showInactivityAlert() {
        try {
            val inactivityOverlay = activity.findViewById<View>(R.id.inactivity_overlay)
            if (inactivityOverlay.visibility == View.VISIBLE) {
                return
            }
            
            inactivityOverlay.visibility = View.VISIBLE
            val countdownTextView = activity.findViewById<TextView>(R.id.tv_countdown)
            val continueButton = activity.findViewById<Button>(R.id.btn_continue)
            val exitButton = activity.findViewById<Button>(R.id.btn_exit)
            
            countdownTextView.text = "(${INACTIVITY_COUNTDOWN_SECONDS}s)"
            
            continueButton.setOnClickListener {
                inactivityCountdownTimer?.cancel()
                inactivityOverlay.visibility = View.GONE
                onContinueActivity()
            }
            
            exitButton.setOnClickListener {
                inactivityCountdownTimer?.cancel()
                onExitInactivity()
            }
            
            inactivityCountdownTimer = object : CountDownTimer(INACTIVITY_COUNTDOWN_SECONDS * 1000L, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    val secondsLeft = millisUntilFinished / 1000 + 1
                    countdownTextView.text = "(${secondsLeft}s)"
                }
                
                override fun onFinish() {
                    onExitInactivity()
                }
            }.start()
            
        } catch (e: Exception) {
            Log.e(TAG, "显示未活动提示失败", e)
        }
    }

    /**
     * 显示连接信息
     */
    fun showConnectionInfo(message: String) {
        activity.runOnUiThread {
            activity.findViewById<TextView>(R.id.tv_connection_info).apply {
                text = message
                visibility = View.VISIBLE
            }
        }
    }

    /**
     * 隐藏连接信息
     */
    fun hideConnectionInfo() {
        activity.runOnUiThread {
            activity.findViewById<TextView>(R.id.tv_connection_info)?.visibility = View.GONE
            activity.findViewById<ProgressBar>(R.id.progress_bar)?.visibility = View.GONE
        }
    }

    /**
     * 显示画质切换蒙版
     */
    fun showQualityChangeOverlay() {
        activity.runOnUiThread {
            activity.findViewById<View>(R.id.quality_change_overlay).visibility = View.VISIBLE
            activity.findViewById<TextView>(R.id.tv_quality_changing).visibility = View.VISIBLE
        }
    }

    /**
     * 隐藏画质切换蒙版
     */
    fun hideQualityChangeOverlay() {
        activity.runOnUiThread {
            activity.findViewById<View>(R.id.quality_change_overlay).visibility = View.GONE
            activity.findViewById<TextView>(R.id.tv_quality_changing).visibility = View.GONE
        }
    }

    /**
     * 截图功能
     */
    private fun captureScreenshot() {
        try {
            val bitmap = getBitmapFromSurface()
            if (bitmap == null) {
                Toast.makeText(activity, "截图失败", Toast.LENGTH_SHORT).show()
                return
            }

            val storageDir = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), "CloudPhone")
            if (!storageDir.exists()) {
                storageDir.mkdirs()
            }

            val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
            val imageFile = File(storageDir, "Screenshot_$timeStamp.jpg")

            FileOutputStream(imageFile).use { out ->
                bitmap.compress(Bitmap.CompressFormat.JPEG, 100, out)
            }

            MediaScannerConnection.scanFile(activity, arrayOf(imageFile.toString()), null) { path, uri ->
                Log.d(TAG, "图片已保存: $path")
            }

            Toast.makeText(activity, "截图已保存至相册", Toast.LENGTH_SHORT).show()
        } catch (e: Exception) {
            Log.e(TAG, "截图失败", e)
            Toast.makeText(activity, "截图失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    /**
     * 从Surface获取Bitmap
     */
    private fun getBitmapFromSurface(): Bitmap? {
        if (surfaceView.width == 0 || surfaceView.height == 0) {
            return null
        }

        try {
            val bitmap = Bitmap.createBitmap(surfaceView.width, surfaceView.height, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)
            surfaceView.draw(canvas)
            return bitmap
        } catch (e: Exception) {
            Log.e(TAG, "从Surface获取Bitmap失败", e)
            return null
        }
    }

    /**
     * 保存悬浮球位置
     */
    private fun saveFloatingBallPosition(view: View) {
        val layoutParams = view.layoutParams as FrameLayout.LayoutParams
        
        val prefs = activity.getSharedPreferences("floating_ball_prefs", Context.MODE_PRIVATE)
        val orientation = activity.resources.configuration.orientation
        
        prefs.edit().apply {
            putInt("left_margin_${orientation}", layoutParams.leftMargin)
            putInt("top_margin_${orientation}", layoutParams.topMargin)
            apply()
        }
    }

    /**
     * 加载悬浮球保存的位置
     */
    fun loadFloatingBallPosition(view: View): Boolean {
        val prefs = activity.getSharedPreferences("floating_ball_prefs", Context.MODE_PRIVATE)
        val orientation = activity.resources.configuration.orientation
        
        val leftMargin = prefs.getInt("left_margin_${orientation}", -1)
        val topMargin = prefs.getInt("top_margin_${orientation}", -1)
        
        if (leftMargin != -1 && topMargin != -1) {
            val layoutParams = view.layoutParams as FrameLayout.LayoutParams
            layoutParams.gravity = Gravity.NO_GRAVITY
            layoutParams.leftMargin = leftMargin
            layoutParams.topMargin = topMargin
            layoutParams.rightMargin = 0
            layoutParams.bottomMargin = 0
            view.layoutParams = layoutParams
            return true
        }
        
        return false
    }

    /**
     * 获取当前模式
     */
    fun getCurrentMode(): Int = currentMode

    /**
     * 释放资源
     */
    fun release() {
        inactivityCountdownTimer?.cancel()
    }
}