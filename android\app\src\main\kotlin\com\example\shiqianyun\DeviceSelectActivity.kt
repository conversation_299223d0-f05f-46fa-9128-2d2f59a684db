package com.example.shiqianyun

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.CheckBox
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.DeviceInfo
import com.example.shiqianyun.network.model.DeviceListResponse
import com.google.android.material.tabs.TabLayout
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import java.text.SimpleDateFormat
import java.util.*
import com.google.gson.Gson
import com.example.shiqianyun.network.model.DeviceConfig
import com.example.shiqianyun.network.model.VipLevel

class DeviceSelectActivity : AppCompatActivity() {
    companion object {
        const val TAG = "DeviceSelectActivity"
        const val REQUEST_CODE_SELECT_DEVICE = 1001
        const val RESULT_KEY_SELECTED_DEVICES = "selected_devices"
    }

    private lateinit var tabLayout: TabLayout
    private lateinit var androidVersionContainer: LinearLayout
    private lateinit var recyclerView: RecyclerView
    private lateinit var checkBoxSelectAll: CheckBox
    private lateinit var tvSelectedCount: TextView
    private lateinit var btnConfirm: Button

    // 所有设备列表
    private val allDevices = mutableListOf<DeviceInfo>()
    // 当前显示的设备列表
    private val currentDevices = mutableListOf<DeviceInfo>()
    // 选中的设备ID
    private val selectedDevices = mutableSetOf<Int>()
    // VIP等级列表
    private val vipLevels = mutableSetOf<String>()
    // 当前选中的VIP等级
    private var currentVipLevel = ""
    
    // VIP配置列表 (从API获取)
    private val vipConfigList = mutableListOf<VipLevel>()
    
    // VIP名称 -> 配置列表的映射
    private val vipConfigsMap = mutableMapOf<String, MutableList<VipConfigInfo>>()
    
    // 当前选中的VIP配置ID
    private var currentVipId = 0
    
    // 设备ID到对应VIP名称的映射
    private val deviceVipNameMap = mutableMapOf<Int, String>()
    
    private lateinit var adapter: DeviceAdapter
    
    // VIP配置信息类
    data class VipConfigInfo(
        val id: Int,
        val configText: String,
        val memory: String,
        val storage: String
    )

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            val layoutId = resources.getIdentifier("activity_device_select", "layout", packageName)
            setContentView(layoutId)
        } catch (e: Exception) {
            Toast.makeText(this, "无法加载布局: ${e.message}", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        initViews()
        setupListeners()
        
        // 先获取设备列表，再获取VIP等级信息
        loadDevices()
    }

    private fun initViews() {
        tabLayout = findViewById(resources.getIdentifier("tab_layout", "id", packageName))
        androidVersionContainer = findViewById(resources.getIdentifier("android_version_container", "id", packageName))
        recyclerView = findViewById(resources.getIdentifier("rv_devices", "id", packageName))
        checkBoxSelectAll = findViewById(resources.getIdentifier("cb_select_all", "id", packageName))
        tvSelectedCount = findViewById(resources.getIdentifier("tv_selected_count", "id", packageName))
        btnConfirm = findViewById(resources.getIdentifier("btn_confirm", "id", packageName))

        // 设置RecyclerView
        recyclerView.layoutManager = LinearLayoutManager(this)
        adapter = DeviceAdapter()
        recyclerView.adapter = adapter

        // 初始化Tab布局
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab) {
                // 切换VIP等级时，清空已选择的设备
                selectedDevices.clear()
                checkBoxSelectAll.isChecked = false
                currentVipLevel = tab.text.toString()
                updateDeviceConfigButtons()
                filterDevices()
            }

            override fun onTabUnselected(tab: TabLayout.Tab) {}
            override fun onTabReselected(tab: TabLayout.Tab) {}
        })

        // 初始化返回按钮
        findViewById<View>(resources.getIdentifier("btn_back", "id", packageName)).setOnClickListener {
            finish()
        }
        
        // 初始化排序按钮
        findViewById<View>(resources.getIdentifier("btn_sort", "id", packageName)).setOnClickListener {
            showSortOptionsDialog()
        }
    }

    private fun setupListeners() {
        // 全选按钮
        checkBoxSelectAll.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                currentDevices.forEach { selectedDevices.add(it.id) }
            } else {
                selectedDevices.clear()
            }
            adapter.notifyDataSetChanged()
            updateSelectedCount()
        }

        // 确认按钮
        btnConfirm.setOnClickListener {
            if (selectedDevices.isEmpty()) {
                Toast.makeText(this, "请选择至少一个设备", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            // 返回选中的设备
            val selectedList = allDevices.filter { selectedDevices.contains(it.id) }
            val intent = Intent().apply {
                putParcelableArrayListExtra(RESULT_KEY_SELECTED_DEVICES, ArrayList(selectedList))
            }
            setResult(Activity.RESULT_OK, intent)
            finish()
        }
    }

    private fun loadDevices() {
        // 获取用户ID
        val userId = getSharedPreferences("app_prefs", MODE_PRIVATE).getInt("user_id", 0)
        if (userId == 0) {
            Toast.makeText(this, "用户未登录", Toast.LENGTH_SHORT).show()
            finish()
            return
        }

        // 显示加载中
        // TODO: 显示加载动画

        // 发起网络请求获取设备列表
        RetrofitManager.apiService.getAllPhones(userId).enqueue(object : Callback<BaseResponse<DeviceListResponse>> {
            override fun onResponse(call: Call<BaseResponse<DeviceListResponse>>, response: Response<BaseResponse<DeviceListResponse>>) {
                if (response.isSuccessful) {
                    val data = response.body()?.data
                    if (data != null) {
                        // 处理未过期设备
                        allDevices.clear()
                        data.nonExpiredIDsData?.let { 
                            allDevices.addAll(it) 
                        }

                        // 获取VIP等级配置信息
                        loadVipLevels()
                    }
                } else {
                    Toast.makeText(this@DeviceSelectActivity, "获取设备失败: ${response.message()}", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<BaseResponse<DeviceListResponse>>, t: Throwable) {
                Toast.makeText(this@DeviceSelectActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 加载VIP等级信息
     */
    private fun loadVipLevels() {
        RetrofitManager.apiService.getVipLevels().enqueue(object : Callback<BaseResponse<List<VipLevel>>> {
            override fun onResponse(call: Call<BaseResponse<List<VipLevel>>>, response: Response<BaseResponse<List<VipLevel>>>) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        // 保存VIP等级数据
                        vipConfigList.clear()
                        vipConfigList.addAll(baseResponse.data)
                        
                        // 处理VIP配置数据并关联设备配置
                        processVipConfigData()
                        
                        // 提取VIP等级和设备配置信息
                        processDeviceData()
                    } else {
                        Log.e(TAG, "获取VIP等级失败: ${baseResponse?.msg}")
                        Toast.makeText(this@DeviceSelectActivity, "获取VIP等级信息失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Log.e(TAG, "获取VIP等级失败: ${response.code()}")
                    Toast.makeText(this@DeviceSelectActivity, "获取VIP等级信息失败", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<List<VipLevel>>>, t: Throwable) {
                Log.e(TAG, "获取VIP等级失败", t)
                Toast.makeText(this@DeviceSelectActivity, "网络错误，无法获取VIP等级信息", Toast.LENGTH_SHORT).show()
            }
        })
    }
    
    /**
     * 处理VIP配置数据
     */
    private fun processVipConfigData() {
        vipConfigsMap.clear()
        
        // 遍历所有VIP配置
        for (vipConfig in vipConfigList) {
            try {
                // 解析JSON配置
                val config = Gson().fromJson(vipConfig.device_json, DeviceConfig::class.java)
                
                // 生成配置文本
                val configText = "${config.memory}+${config.storage}G"
                
                // 创建配置信息对象
                val configInfo = VipConfigInfo(
                    id = vipConfig.id,
                    configText = configText,
                    memory = config.memory,
                    storage = config.storage
                )
                
                // 将配置添加到对应VIP名称下的列表
                val configsList = vipConfigsMap.getOrPut(vipConfig.vip_name) { mutableListOf() }
                configsList.add(configInfo)
                
                Log.d(TAG, "VIP配置: ${vipConfig.vip_name}, ID: ${vipConfig.id}, 配置: $configText")
            } catch (e: Exception) {
                Log.e(TAG, "解析VIP配置失败: ${vipConfig.device_json}", e)
            }
        }
    }
    
    /**
     * 为设备设置配置信息
     */
    private fun processDeviceData() {
        // 提取所有VIP等级
        vipLevels.clear()
        deviceVipNameMap.clear()
        
        // 为每个设备设置配置信息
        for (device in allDevices) {
            // 获取设备的VIP ID
            val vipId = device.vip_id
            
            // 查找对应的VIP配置
            val matchedVipConfig = vipConfigList.find { it.id == vipId }
            
            if (matchedVipConfig != null) {
                // 保存设备ID和VIP名称的映射，而不是直接修改device.vip_level
                deviceVipNameMap[device.id] = matchedVipConfig.vip_name
                
                // 解析设备配置
                try {
                    val config = Gson().fromJson(matchedVipConfig.device_json, DeviceConfig::class.java)
                    device.configText = "${config.memory}+${config.storage}G"
                } catch (e: Exception) {
                    device.configText = "未知配置"
                    Log.e(TAG, "解析设备配置失败: ${matchedVipConfig.device_json}", e)
                }
                
                // 添加VIP等级到集合
                vipLevels.add(matchedVipConfig.vip_name)
            } else {
                // 未找到匹配的VIP配置
                device.configText = "未知配置"
                Log.e(TAG, "未找到匹配的VIP配置: vipId = $vipId")
            }
        }

        // 设置Tab
        tabLayout.removeAllTabs()
        for (level in vipLevels) {
            tabLayout.addTab(tabLayout.newTab().setText(level))
        }

        // 选中第一个Tab
        if (vipLevels.isNotEmpty()) {
            currentVipLevel = vipLevels.first()
            tabLayout.getTabAt(0)?.select()
        }

        // 更新设备配置按钮
        updateDeviceConfigButtons()
        
        // 过滤设备
        filterDevices()
    }

    private fun updateDeviceConfigButtons() {
        androidVersionContainer.removeAllViews()
        
        // 获取当前VIP等级的所有配置
        val configs = vipConfigsMap[currentVipLevel] ?: return
        
        // 如果配置列表为空，则返回
        if (configs.isEmpty()) return
        
        // 获取第一个配置的ID（默认选中）
        if (currentVipId == 0) {
            currentVipId = configs.firstOrNull()?.id ?: 0
        }
        
        for (config in configs) {
            val button = Button(this).apply {
                text = config.configText
                textSize = 14f
                setPadding(dip2px(16), 0, dip2px(16), 0)
                layoutParams = LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    dip2px(40)
                ).apply {
                    marginEnd = dip2px(8)
                }
                
                // 判断是否是当前选中的配置
                if (config.id == currentVipId) {
                    val primaryButtonDrawableId = resources.getIdentifier("bg_button_primary", "drawable", packageName)
                    background = getDrawable(primaryButtonDrawableId)
                    setTextColor(getColor(android.R.color.white))
                } else {
                    val outlineButtonDrawableId = resources.getIdentifier("bg_button_outline", "drawable", packageName)
                    background = getDrawable(outlineButtonDrawableId)
                    setTextColor(getColor(android.R.color.darker_gray))
                }
                
                // 点击事件
                setOnClickListener {
                    // 如果点击的是不同的配置，则清空已选择的设备
                    if (currentVipId != config.id) {
                        selectedDevices.clear()
                        checkBoxSelectAll.isChecked = false
                    }
                    currentVipId = config.id
                    updateDeviceConfigButtons()
                    filterDevices()
                }
            }
            
            androidVersionContainer.addView(button)
        }
    }
    
    private fun filterDevices() {
        currentDevices.clear()
        
        // 过滤符合当前VIP等级和VIP ID的设备
        for (device in allDevices) {
            val deviceVipName = deviceVipNameMap[device.id] ?: ""
            if (deviceVipName == currentVipLevel && device.vip_id == currentVipId) {
                currentDevices.add(device)
            }
        }
        
        // 更新适配器
        adapter.notifyDataSetChanged()
        
        // 更新选中数量
        updateSelectedCount()
        
        // 更新全选状态
        checkBoxSelectAll.isChecked = currentDevices.isNotEmpty() && 
            currentDevices.all { selectedDevices.contains(it.id) }
    }
    
    private fun updateSelectedCount() {
        val totalSelected = selectedDevices.size
        tvSelectedCount.text = "(${totalSelected}台${currentVipLevel}设备)"
    }
    
    private fun dip2px(dp: Int): Int {
        val scale = resources.displayMetrics.density
        return (dp * scale + 0.5f).toInt()
    }
    
    /**
     * 设备列表适配器 - 只允许选择单个设备（续费用）
     */
    inner class DeviceAdapter : RecyclerView.Adapter<DeviceAdapter.ViewHolder>() {
        
        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
            val layoutId = parent.resources.getIdentifier("item_device_select", "layout", parent.context.packageName)
            val view = LayoutInflater.from(parent.context).inflate(layoutId, parent, false)
            return ViewHolder(view)
        }
        
        override fun onBindViewHolder(holder: ViewHolder, position: Int) {
            val device = currentDevices[position]
            
            // 获取设备对应的VIP名称
            val vipName = deviceVipNameMap[device.id] ?: device.vip_level
            
            // 设置VIP等级和配置信息
            holder.tvVipLevel.text = "$vipName ${device.configText}"
            
            // 根据VIP等级设置不同的颜色
            when (vipName.toLowerCase(Locale.getDefault())) {
                "vip" -> holder.tvVipLevel.setTextColor(resources.getColor(android.R.color.holo_orange_light))
                "svip" -> holder.tvVipLevel.setTextColor(resources.getColor(android.R.color.holo_purple))
                "gvip" -> holder.tvVipLevel.setTextColor(resources.getColor(android.R.color.holo_green_light))
                "xvip" -> holder.tvVipLevel.setTextColor(resources.getColor(android.R.color.holo_blue_bright))
                else -> holder.tvVipLevel.setTextColor(resources.getColor(android.R.color.holo_blue_light))
            }
            
            // 设置设备名称
            holder.tvDeviceName.text = device.nick_name
            
            // 设置设备ID
            holder.tvDeviceId.text = "ID ${device.phone_id}"

            // 设置安卓版本
            holder.tvAndroidVersion.text = "安卓${device.android_version}"
            
            // 计算剩余时间
            try {
                val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
                val overTime = dateFormat.parse(device.over_time)
                val currentTime = Date()
                
                if (overTime != null && overTime.after(currentTime)) {
                    val diffMillis = overTime.time - currentTime.time
                    val diffDays = diffMillis / (24 * 60 * 60 * 1000)
                    val diffHours = (diffMillis % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
                    val diffMinutes = (diffMillis % (60 * 60 * 1000)) / (60 * 1000)
                    
                    if (diffDays > 0) {
                        holder.tvRemainingTime.text = "${diffDays}天${diffHours}时"
                    } else {
                        holder.tvRemainingTime.text = "${diffHours}时${diffMinutes}分"
                    }
                } else {
                    holder.tvRemainingTime.text = "已过期"
                }
            } catch (e: Exception) {
                holder.tvRemainingTime.text = "未知"
            }
            
            // 设置选中状态
            holder.cbSelect.isChecked = selectedDevices.contains(device.id)
            
            // 设置点击事件 - 只允许选择一个设备
            holder.cbSelect.setOnClickListener {
                if (holder.cbSelect.isChecked) {
                    // 清空其他选择，只保留当前选择
                    selectedDevices.clear()
                    selectedDevices.add(device.id)
                } else {
                    selectedDevices.remove(device.id)
                }
                updateSelectedCount()
                
                // 更新全选状态
                checkBoxSelectAll.isChecked = false
                
                // 刷新所有条目的选中状态
                notifyDataSetChanged()
            }
            
            // 设置整个条目的点击事件
            holder.itemView.setOnClickListener {
                holder.cbSelect.performClick()
            }
        }
        
        override fun getItemCount(): Int = currentDevices.size
        
        inner class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
            val cbSelect: CheckBox = view.findViewById(view.resources.getIdentifier("cb_select", "id", view.context.packageName))
            val tvVipLevel: TextView = view.findViewById(view.resources.getIdentifier("tv_vip_level", "id", view.context.packageName))
            val tvDeviceName: TextView = view.findViewById(view.resources.getIdentifier("tv_device_name", "id", view.context.packageName))
            val tvDeviceId: TextView = view.findViewById(view.resources.getIdentifier("tv_device_id", "id", view.context.packageName))
            val tvRemainingTime: TextView = view.findViewById(view.resources.getIdentifier("tv_remaining_time", "id", view.context.packageName))
            val tvAndroidVersion: TextView = view.findViewById(view.resources.getIdentifier("tv_android_version", "id", view.context.packageName))
        }
    }

    /**
     * 显示排序选项对话框
     */
    private fun showSortOptionsDialog() {
        val sortOptions = arrayOf("到期时间升序", "到期时间降序", "设备ID升序", "设备ID降序")
        
        AlertDialog.Builder(this)
            .setTitle("选择排序方式")
            .setItems(sortOptions) { dialog, which ->
                when (which) {
                    0 -> sortDevicesByExpirationAsc()
                    1 -> sortDevicesByExpirationDesc()
                    2 -> sortDevicesByIdAsc()
                    3 -> sortDevicesByIdDesc()
                }
            }
            .show()
    }
    
    /**
     * 按到期时间升序排序
     */
    private fun sortDevicesByExpirationAsc() {
        currentDevices.sortBy { 
            try {
                SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).parse(it.over_time)?.time ?: 0
            } catch (e: Exception) {
                0L
            }
        }
        adapter.notifyDataSetChanged()
        Toast.makeText(this, "已按到期时间升序排列", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 按到期时间降序排序
     */
    private fun sortDevicesByExpirationDesc() {
        currentDevices.sortByDescending { 
            try {
                SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).parse(it.over_time)?.time ?: 0
            } catch (e: Exception) {
                0L
            }
        }
        adapter.notifyDataSetChanged()
        Toast.makeText(this, "已按到期时间降序排列", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 按设备ID升序排序
     */
    private fun sortDevicesByIdAsc() {
        currentDevices.sortBy { it.phone_id }
        adapter.notifyDataSetChanged()
        Toast.makeText(this, "已按设备ID升序排列", Toast.LENGTH_SHORT).show()
    }
    
    /**
     * 按设备ID降序排序
     */
    private fun sortDevicesByIdDesc() {
        currentDevices.sortByDescending { it.phone_id }
        adapter.notifyDataSetChanged()
        Toast.makeText(this, "已按设备ID降序排列", Toast.LENGTH_SHORT).show()
    }
}