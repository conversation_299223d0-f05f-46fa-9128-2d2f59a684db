package com.example.shiqianyun.decoder.parser

import android.util.Log

/**
 * NAL单元解析器
 * 负责从H.264字节流中解析出独立的NAL单元
 */
class NALUnitParser {
    private val nalUnitBuffer = mutableListOf<Byte>()
    
    companion object {
        private const val TAG = "NALUnitParser"
        
        // NAL unit types
        const val NAL_UNIT_TYPE_SPS = 7
        const val NAL_UNIT_TYPE_PPS = 8
        const val NAL_UNIT_TYPE_IDR = 5
        const val NAL_UNIT_TYPE_NON_IDR = 1
    }
    
    /**
     * 处理原始H.264数据，提取NAL单元
     */
    fun processRawData(data: ByteArray, onNalUnitFound: (ByteArray) -> Unit) {
        nalUnitBuffer.addAll(data.toList())
        
        while (nalUnitBuffer.size >= 4) {
            val nalStartIndex = findNextNalUnit()
            if (nalStartIndex >= 0) {
                if (nalStartIndex > 0) {
                    val nalUnit = nalUnitBuffer.take(nalStartIndex).toByteArray()
                    if (nalUnit.isNotEmpty()) {
                        onNalUnitFound(nalUnit)
                    }
                    repeat(nalStartIndex) { nalUnitBuffer.removeAt(0) }
                }
                
                val startCodeLength = getStartCodeLength()
                repeat(startCodeLength) { nalUnitBuffer.removeAt(0) }
            } else {
                break
            }
        }
        
        // 处理缓冲区末尾可能剩余的完整NAL单元
        if (nalUnitBuffer.isNotEmpty() && !hasStartCode()) {
            val remainingUnit = nalUnitBuffer.toByteArray()
            if (remainingUnit.isNotEmpty()) {
                onNalUnitFound(remainingUnit)
                nalUnitBuffer.clear()
            }
        }
    }
    
    /**
     * 获取NAL单元类型
     */
    fun getNalUnitType(nalUnit: ByteArray): Int {
        return if (nalUnit.isNotEmpty()) {
            nalUnit[0].toInt() and 0x1F
        } else {
            -1
        }
    }
    
    /**
     * 清空缓冲区
     */
    fun clear() {
        nalUnitBuffer.clear()
    }
    
    private fun getStartCodeLength(): Int {
        return if (nalUnitBuffer.size >= 4 && 
            nalUnitBuffer[0] == 0.toByte() && nalUnitBuffer[1] == 0.toByte() && 
            nalUnitBuffer[2] == 0.toByte() && nalUnitBuffer[3] == 1.toByte()) {
            4
        } else if (nalUnitBuffer.size >= 3 &&
            nalUnitBuffer[0] == 0.toByte() && nalUnitBuffer[1] == 0.toByte() && 
            nalUnitBuffer[2] == 1.toByte()) {
            3
        } else {
            0
        }
    }
    
    private fun hasStartCode(): Boolean {
        if (nalUnitBuffer.size < 3) return false
        for (i in 0..nalUnitBuffer.size - 3) {
            if (nalUnitBuffer[i] == 0.toByte() && nalUnitBuffer[i + 1] == 0.toByte()) {
                if (nalUnitBuffer[i + 2] == 1.toByte()) {
                    return true
                } else if (i < nalUnitBuffer.size - 3 && 
                    nalUnitBuffer[i + 2] == 0.toByte() && nalUnitBuffer[i + 3] == 1.toByte()) {
                    return true
                }
            }
        }
        return false
    }
    
    private fun findNextNalUnit(): Int {
        for (i in 3 until nalUnitBuffer.size) {
            if (nalUnitBuffer[i-3] == 0.toByte() && nalUnitBuffer[i-2] == 0.toByte()) {
                if (nalUnitBuffer[i-1] == 1.toByte()) {
                    return i - 3
                } else if (i < nalUnitBuffer.size - 1 && nalUnitBuffer[i-1] == 0.toByte() && nalUnitBuffer[i] == 1.toByte()) {
                    return i - 3
                }
            }
        }
        return -1
    }
}