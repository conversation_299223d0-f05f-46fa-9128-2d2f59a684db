package com.example.shiqianyun

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.adapter.DeviceTransferSelectAdapter
import com.example.shiqianyun.network.ApiService
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.DeviceInfo
import com.example.shiqianyun.network.model.DeviceListResponse
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.GroupInfo
import com.google.android.material.tabs.TabLayout
import java.text.SimpleDateFormat
import java.util.Locale
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class DeviceTransferSelectActivity : AppCompatActivity() {

    private lateinit var ivBack: ImageView
    private lateinit var tabLayout: TabLayout
    private lateinit var recyclerView: RecyclerView
    private lateinit var tvSelectedCount: TextView
    private lateinit var btnConfirm: Button
    private lateinit var progressBar: ProgressBar

    private lateinit var adapter: DeviceTransferSelectAdapter
    private val allDeviceList = mutableListOf<DeviceInfo>()
    private val currentGroupDevices = mutableListOf<DeviceInfo>()
    private val groupList = mutableListOf<GroupInfo>()
    private val selectedDevices = mutableListOf<DeviceInfo>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_device_transfer_select)

        initViews()
        setupRecyclerView()
        loadDeviceData()
    }

    private fun initViews() {
        ivBack = findViewById(R.id.iv_back)
        tabLayout = findViewById(R.id.tab_layout)
        recyclerView = findViewById(R.id.recycler_view)
        tvSelectedCount = findViewById(R.id.tv_selected_count)
        btnConfirm = findViewById(R.id.btn_confirm)
        progressBar = findViewById(R.id.progress_bar)

        // 返回按钮点击事件
        ivBack.setOnClickListener {
            finish()
        }

        // 确认选择按钮点击事件
        btnConfirm.setOnClickListener {
            confirmSelection()
        }

        // TabLayout选择监听
        tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let { filterDevicesByGroup(it.position) }
            }
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }

    private fun setupRecyclerView() {
        adapter = DeviceTransferSelectAdapter(currentGroupDevices) { device, isSelected ->
            if (isSelected) {
                if (!selectedDevices.contains(device)) {
                    selectedDevices.add(device)
                }
            } else {
                selectedDevices.remove(device)
            }
            updateSelectedCount()
            updateConfirmButtonState()
        }

        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter
    }

    private fun loadDeviceData() {
        showLoading(true)

        val apiService = RetrofitManager.apiService
        val userId = getSharedPreferences("app_prefs", MODE_PRIVATE).getInt("user_id", 0)

        val call = apiService.getAllPhones(userId)
        call.enqueue(object : Callback<BaseResponse<DeviceListResponse>> {
            override fun onResponse(
                call: Call<BaseResponse<DeviceListResponse>>,
                response: Response<BaseResponse<DeviceListResponse>>
            ) {
                showLoading(false)
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse?.code == 200) {
                        handleDeviceData(baseResponse.data)
                    } else {
                        Toast.makeText(this@DeviceTransferSelectActivity, baseResponse?.msg ?: "获取设备列表失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@DeviceTransferSelectActivity, "网络请求失败", Toast.LENGTH_SHORT).show()
                }
            }

            override fun onFailure(call: Call<BaseResponse<DeviceListResponse>>, t: Throwable) {
                showLoading(false)
                Log.e("DeviceTransferSelect", "获取设备列表失败", t)
                Toast.makeText(this@DeviceTransferSelectActivity, "网络连接失败", Toast.LENGTH_SHORT).show()
            }
        })
    }

    /**
     * 处理设备数据
     */
    private fun handleDeviceData(data: DeviceListResponse?) {
        data?.let {
            // 更新分组列表
            groupList.clear()
            groupList.addAll(it.group ?: emptyList())
            
            // 更新设备列表
            allDeviceList.clear()
            it.nonExpiredIDsData?.let { devices -> allDeviceList.addAll(devices) }
            
            // 设置分组选项卡
            setupTabs()
            
            // 默认显示全部设备
            filterDevicesByGroup(0)
        }
    }

    /**
     * 设置分组选项卡
     */
    private fun setupTabs() {
        tabLayout.removeAllTabs()
        
        // 添加"全部"选项卡
        tabLayout.addTab(tabLayout.newTab().setText("全部"))
        
        // 添加分组选项卡
        groupList.forEach { group ->
            tabLayout.addTab(tabLayout.newTab().setText(group.group_name))
        }
    }

    /**
     * 根据分组筛选设备
     */
    private fun filterDevicesByGroup(tabPosition: Int) {
        currentGroupDevices.clear()
        
        if (tabPosition == 0) {
            // 全部设备，显示所有设备（不过滤）
            currentGroupDevices.addAll(allDeviceList)
        } else {
            // 特定分组的设备（需要调用API获取）
            val group = groupList[tabPosition - 1]
            loadGroupDevices(group.id)
            return
        }
        
        adapter.notifyDataSetChanged()
        updateSelectedCount()
    }

    /**
     * 加载分组设备
     */
    private fun loadGroupDevices(groupId: Int) {
        showLoading(true)
        
        RetrofitManager.apiService.getGroupDevices(
            groupId = groupId,
            limit = 100,
            sort = "desc"
        ).enqueue(object : Callback<BaseResponse<List<DeviceInfo>>> {
            override fun onResponse(call: Call<BaseResponse<List<DeviceInfo>>>, response: Response<BaseResponse<List<DeviceInfo>>>) {
                showLoading(false)
                
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        currentGroupDevices.clear()
                        // 显示所有分组设备（不过滤）
                        currentGroupDevices.addAll(baseResponse.data ?: emptyList())
                        adapter.notifyDataSetChanged()
                        updateSelectedCount()
                    } else {
                        Toast.makeText(this@DeviceTransferSelectActivity, baseResponse?.msg ?: "获取分组设备失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Toast.makeText(this@DeviceTransferSelectActivity, "网络请求失败: ${response.code()}", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<List<DeviceInfo>>>, t: Throwable) {
                showLoading(false)
                Log.e("DeviceTransferSelect", "获取分组设备失败", t)
                Toast.makeText(this@DeviceTransferSelectActivity, "网络连接失败: ${t.message}", Toast.LENGTH_SHORT).show()
            }
        })
    }

    private fun updateSelectedCount() {
        val totalCount = currentGroupDevices.size
        val selectedCount = selectedDevices.size
        tvSelectedCount.text = "已选中 $selectedCount/$totalCount 台设备"
    }

    private fun updateConfirmButtonState() {
        btnConfirm.isEnabled = selectedDevices.isNotEmpty()
    }

    private fun confirmSelection() {
        if (selectedDevices.isEmpty()) {
            Toast.makeText(this, "请选择要转移的设备", Toast.LENGTH_SHORT).show()
            return
        }

        // 返回选中的设备列表
        val intent = Intent().apply {
            putParcelableArrayListExtra("selected_devices", ArrayList<DeviceInfo>(selectedDevices))
        }
        setResult(Activity.RESULT_OK, intent)
        finish()
    }

    private fun showLoading(show: Boolean) {
        progressBar.visibility = if (show) View.VISIBLE else View.GONE
        recyclerView.visibility = if (show) View.GONE else View.VISIBLE
    }

    /**
     * 计算剩余天数
     */
    private fun calculateRemainingDays(overTime: String): Int {
        return try {
            // 处理时间格式
            val timeStr = when {
                overTime.contains("T") -> {
                    // ISO格式: 2025-08-09T10:16:42+08:00
                    overTime.replace("T", " ").substringBefore("+").substringBefore("Z")
                }
                else -> {
                    // 普通格式: 2025-08-09 10:16:42
                    overTime
                }
            }
            
            val format = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault())
            val overTimeDate = format.parse(timeStr)
            val currentTime = System.currentTimeMillis()
            val remainingTime = (overTimeDate?.time ?: 0) - currentTime
            val remainingDays = remainingTime / (24 * 60 * 60 * 1000)
            kotlin.math.max(0, remainingDays.toInt())
        } catch (e: Exception) {
            // 如果解析失败，默认返回0天
            0
        }
    }
}