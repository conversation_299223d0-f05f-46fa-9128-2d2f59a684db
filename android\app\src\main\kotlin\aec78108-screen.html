<!-- saved from url=(0881)http://localhost:8000/api/v1/general/mobile/aec78108/screen/?config=%7B%22recorder_enable%22%3A%20false%2C%20%22recorder_format%22%3A%20%22mp4%22%2C%20%22audio%22%3A%20true%2C%20%22video_codec%22%3A%20%22h264%22%2C%20%22audio_codec%22%3A%20%22aac%22%2C%20%22audio_source%22%3A%20%22output%22%2C%20%22max_size%22%3A%20720%2C%20%22video_bit_rate%22%3A%20800000%2C%20%22audio_bit_rate%22%3A%20128000%2C%20%22max_fps%22%3A%2025%2C%20%22tunnel_forward%22%3A%20true%2C%20%22crop%22%3A%20%22%22%2C%20%22control%22%3A%20true%2C%20%22show_touches%22%3A%20false%2C%20%22stay_awake%22%3A%20true%2C%20%22video_codec_options%22%3A%20%22profile%3D1%2Clevel%3D2%22%2C%20%22audio_codec_options%22%3A%20%22%22%2C%20%22video_encoder%22%3A%20%22%22%2C%20%22audio_encoder%22%3A%20%22%22%2C%20%22power_off_on_close%22%3A%20false%2C%20%22clipboard_autosync%22%3A%20false%2C%20%22power_on%22%3A%20true%7D -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <title>aec78108-screen</title>
  <link rel="stylesheet" href="static/css/control-bar.css">
</head>
<body onselectstart="return false;" unselectable="on">
<div id="container" style="display: inline-flex;">
  <canvas width="336" height="720" style="background-color: rgb(13, 14, 27);"></canvas>
  
  <div class="control-bar">
    <button class="control-button" id="refresh_button" title="刷新">
      <svg viewBox="0 0 24 24">
        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
      </svg>
    </button>

    <button class="control-button" id="capture_button" title="截图">
      <svg viewBox="0 0 24 24"><path d="M12 8c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4-1.79-4-4-4zm0 6c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2z"/></svg>
    </button>
    
    <button class="control-button" id="record_button" title="录像">
      <svg viewBox="0 0 24 24"><path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/></svg>
    </button>

    <button class="control-button" id="quality_button" title="切换画质">
      <svg viewBox="0 0 24 24">
        <path d="M12 4a8 8 0 100 16 8 8 0 000-16zm0 14.5A6.5 6.5 0 1112 5.5a6.5 6.5 0 010 13zm-1-10h2v6h-2zm0 8h2v2h-2z"/>
      </svg>
    </button>

    <button class="control-button" id="clipboard_get_button" title="获取剪贴板">
      <svg viewBox="0 0 24 24"><path d="M16 9v10H8V9h8m-1.5-6h-5l-1 1H5v2h14V4h-4.5l-1-1M18 7H6c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V9c0-1.1-.9-2-2-2z"/></svg>
    </button>
    <button class="control-button" id="clipboard_set_button" title="设置剪贴板">
      <svg viewBox="0 0 24 24"><path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c0-.55.45-1 1-1s1 .45 1 1v1h-2V3zm7 18H5V5h14v16zm-7-3c-2.21 0-4-1.79-4-4h2c0 1.1.9 2 2 2s2-.9 2-2-.9-2-2-2c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2h2c0-2.21-1.79-4-4-4s-4 1.79-4 4c0 1.66 1.34 3 3 3.72V17h2v-1.28c1.66-.72 3-2.06 3-3.72 0-2.21-1.79-4-4-4z"/></svg>
    </button>

    <div class="divider"></div>

    <button class="control-button" id="v+_button" title="音量加">
      <svg viewBox="0 0 24 24"><path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/></svg>
    </button>
    
    <button class="control-button" id="v-_button" title="音量减">
      <svg viewBox="0 0 24 24"><path d="M18.5 12c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM5 9v6h4l5 5V4L9 9H5z"/></svg>
    </button>

    <div class="divider"></div>

    <button class="control-button" id="power_button" title="电源">
      <svg viewBox="0 0 24 24"><path d="M13 3h-2v10h2V3zm4.83 2.17l-1.42 1.42C17.99 7.86 19 9.81 19 12c0 3.87-3.13 7-7 7s-7-3.13-7-7c0-2.19 1.01-4.14 2.58-5.42L6.17 5.17C4.23 6.82 3 9.26 3 12c0 4.97 4.03 9 9 9s9-4.03 9-9c0-2.74-1.23-5.18-3.17-6.83z"/></svg>
    </button>

    <button class="control-button" id="back_button" title="返回">
      <svg viewBox="0 0 24 24"><path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/></svg>
    </button>

    <button class="control-button" id="home_button" title="主页">
      <svg viewBox="0 0 24 24"><path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/></svg>
    </button>

    <button class="control-button" id="menu_button" title="菜单">
      <svg viewBox="0 0 24 24"><path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/></svg>
    </button>
  </div>
</div>

<div style="display: none;">
  <div>duration: <em id="player_duration">0</em></div>
  <div id="error_msg1" style="color: red;display: none;">none</div>
  <div id="error_msg2" style="color: red; display: block;">Client disconnected</div>
  <div>
    <select id="video_play_select" style="width:100px;height:30px">
        <option value="broardway">webcodecs</option>
        <option value="broardway2">broardway2</option>
        <option value="webcodecs">broardway</option>
    </select>
  </div>
  <a id="capture"></a>
</div>

<div id="quality-popup" style="display:none; position: absolute; right: 60px; top: 80px; background: #222; color: #fff; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.2); z-index: 1000;">
  <div style="padding: 10px; cursor: pointer;" data-quality='{"name":"高","fps":60,"bits":5000000,"size":2160}'>高画质</div>
  <div style="padding: 10px; cursor: pointer;" data-quality='{"name":"中","fps":25,"bits":2000000,"size":1080}'>中画质</div>
  <div style="padding: 10px; cursor: pointer;" data-quality='{"name":"低","fps":15,"bits":2000000,"size":720}'>低画质</div>
</div>

<script src="js/Decoder.js"></script>
<script src="js/YUVCanvas.js"></script>
<script src="js/Player.js"></script>
<script src="js/pcm-player.min.js"></script>
<script type="module">
  import { h264ParseConfiguration} from "./js/h264_parse.js";
  import { h265ParseConfiguration} from "./js/h265_parse.js";
  window.h264ParseConfiguration = h264ParseConfiguration
  window.h265ParseConfiguration = h265ParseConfiguration
</script>

<script>
// ============ 消息注入函数 ============
function inject_keycode(keycode, action){
  const msg = {
    msg_type: 0,
    keycode: keycode,
    action: action
  }
  ws.send(JSON.stringify(msg))
}

function inject_text(text){
  const msg = {
    msg_type: 1,
    text: text,
  }
  ws.send(JSON.stringify(msg))
}

function inject_touch_event(pix_data, action){
  const msg = {
    msg_type: 2,
    action: action,
    resolution: window.canvas_resolution,
    x: pix_data[0],
    y: pix_data[1],
  }
  ws.send(JSON.stringify(msg))
}

function inject_scroll_event(pix_data){
  const msg = {
    msg_type: 3,
    x: pix_data[0],
    y: pix_data[1],
    resolution: window.canvas_resolution,
    h_scroll: pix_data[2],
    v_scroll: pix_data[3],
  }
  ws.send(JSON.stringify(msg))
}

function get_clipboard(copy_key=1){
  const msg = {
    msg_type: 8,
    copy_key: copy_key
  }
  ws.send(JSON.stringify(msg))
}

function set_clipboard(text, sequence=1, paste=true){
  const msg = {
    msg_type: 9,
    text: text,
    sequence: sequence,
    paste: paste
  }
  ws.send(JSON.stringify(msg))
}

function toggle_sw(){
  if (this.textContent=='sw-on'){
    screen_power_mode=0
    this.textContent='sw-off'
  }
  else{
    screen_power_mode=2
    this.textContent='sw-on'
  }
  const msg = {
    msg_type: 10,
    screen_power_mode: window.screen_power_mode,
  }
  ws.send(JSON.stringify(msg))
}

function swipe(pix_data, delay=0, unit=13){
  delay = parseFloat(delay.toFixed(2))
  if (delay <= 3 && delay >=0){
    const msg = {
      msg_type: 30,
      x: pix_data[0],
      y: pix_data[1],
      resolution: window.canvas_resolution,
      end_x: pix_data[2],
      end_y: pix_data[3],
      unit: unit,
      delay: delay,
    }
    ws.send(JSON.stringify(msg))
  }
}

function switch_quality(quality) {
  const msg = {
    msg_type: 999,
    fps: quality.fps,
    bits: quality.bits,
    size: quality.size
  };
  console.log(`切换画质: ${quality.name}, FPS=${quality.fps}, 码率=${quality.bits}kbps, 最大边=${quality.size}`);
  ws.send(JSON.stringify(msg));
}
</script>

<script>
// ============ 工具函数 ============
function throttle(fn,during) {
  let t = null
  return function(e){
    if(!t){
      t = setTimeout(()=>{
        fn.call(this,e)
        t = null
      },during)
    }
  }
}

function get_pointer_position(event, ele){
  let x = event.clientX - ele.offsetLeft + window.scrollX;
  x = parseInt(x);
  x = Math.min(x, ele.width);
  x = Math.max(x, 0);
  let y = event.clientY - ele.offsetTop + window.scrollY;
  y = parseInt(y);
  y = Math.min(y, ele.height);
  y = Math.max(y, 0);
  return [x, y]
}

function canvas_mouse_move(event) {
  const pix_data = get_pointer_position(event, this)
  inject_touch_event(pix_data, 2)
}

function canvas_mouse_scroll(event) {
  const pix_data = get_pointer_position(event, this)
  const distance_x = event.deltaX > 0 ? -5 : 5
  const distance_y = event.deltaY > 0 ? -5 : 5
  pix_data[2] = distance_x
  pix_data[3] = distance_y
  inject_scroll_event(pix_data)
}
</script>

<script>
// ============ 事件处理函数 ============
function add_canvas_touch_event(ele){
  window.touch_start = null
  const efficient_canvas_mouse_move = throttle(canvas_mouse_move, 15);
  
  ele.addEventListener('mousedown', function (event) {
    if(event.buttons == 1){
      window.touch_start = true
      this.removeEventListener("mousemove", efficient_canvas_mouse_move)
      const pix_data = get_pointer_position(event, this)
      inject_touch_event(pix_data, 0)
      this.addEventListener('mousemove', efficient_canvas_mouse_move)
    }
  })
  
  ele.addEventListener('mouseup', function (event) {
    if (window.touch_start){
      window.touch_start = false
      const pix_data = get_pointer_position(event, this)
      inject_touch_event(pix_data, 1)
      this.removeEventListener("mousemove", efficient_canvas_mouse_move)
    }
  })
  
  ele.addEventListener('mouseout', function (event) {
    if (window.touch_start){
      window.touch_start = false
      const pix_data = get_pointer_position(event, this)
      inject_touch_event(pix_data, 1)
      this.removeEventListener("mousemove", efficient_canvas_mouse_move)
    }
  })
}

function add_canvas_swipe_event(ele){
  window.swipe_start = null
  window.swipe_start_pix_data = null
  
  ele.addEventListener('mousedown', function (event) {
    if(event.buttons == 4){
      window.swipe_start = Date.now()
      window.swipe_start_pix_data = get_pointer_position(event, this)
    }
  })
  
  ele.addEventListener('mouseup', function (event) {
    if (window.swipe_start){
      const swipe_end = Date.now()
      const delay = (swipe_end - window.swipe_start)/1000
      window.swipe_start = null
      const swipe_end_pix_data = get_pointer_position(event, this)
      const pix_data = window.swipe_start_pix_data.concat(swipe_end_pix_data)
      window.swipe_start_pix_data = null
      swipe(pix_data, delay)
    }
  })
  
  ele.addEventListener('mouseout', function (event) {
    if (window.swipe_start){
      const swipe_end = Date.now()
      const delay = (swipe_end - window.swipe_start)/1000
      window.swipe_start = null
      const swipe_end_pix_data = get_pointer_position(event, this)
      const pix_data = window.swipe_start_pix_data.concat(swipe_end_pix_data)
      window.swipe_start_pix_data = null
      swipe(pix_data, delay)
    }
  })
}

function add_canvas_scroll_event(ele){
  const efficient_canvas_mouse_scroll = throttle(canvas_mouse_scroll, 100);
  ele.addEventListener("wheel", efficient_canvas_mouse_scroll)
}

function add_button_mouse_event(ele, keycode){
  const button_mouse_up_down_keycode = 'add_button_mouse_event' + keycode
  window[button_mouse_up_down_keycode] = null
  
  ele.addEventListener('mousedown', function (event) {
    if(event.buttons == 1){
      inject_keycode(keycode, 0)
      window[button_mouse_up_down_keycode] = true
    }
  })
  
  ele.addEventListener('mouseup', function (event) {
    if (window[button_mouse_up_down_keycode]){
      inject_keycode(keycode, 1)
      window[button_mouse_up_down_keycode] = null
    }
  })
  
  ele.addEventListener('mouseout', function (event) {
    if (window[button_mouse_up_down_keycode]){
      inject_keycode(keycode, 1)
      window[button_mouse_up_down_keycode] = null
    }
  })
}
</script>

<script>
// ============ 按钮处理函数 ============
function button_handle_set_clipboard(){
  const text = prompt('请输入要设置到剪贴板的内容:');
  if (text !== null && text !== undefined && text !== '') {
    set_clipboard(text);
  }
}

function button_handle_get_clipboard(){
  get_clipboard(1)
}

const xhr = new XMLHttpRequest();
function button_handle_capture(){
  const ele = document.getElementById('capture')
  ele.href = window.video_renderer_canvas.toDataURL()
  ele.download = window.device_id + '_' + (new Date().formatCode()) + '.png'
  ele.click()
  xhr.open('POST', '/api/v1/general/picture/upload_base64/', true)
  xhr.setRequestHeader('content-type', 'application/json');
  xhr.withCredentials = true;
  xhr.send(JSON.stringify({'img':ele.href, "device_id": window.device_id}))
}
</script>

<script>
// ============ 系统初始化 ============
function load_utils(){
  Date.prototype.formatCode = function (formatStr = "yyyy-MM-DD HH:mm:ss") {
    const paddingZero = num => num >= 10 ? num : '0' + num;
    let str = formatStr;
    str = str.replace(/yyyy|YYYY/, this.getFullYear());
    str = str.replace(/MM/, paddingZero(this.getMonth() + 1));
    str = str.replace(/dd|DD/, paddingZero(this.getDate()));
    str = str.replace(/hh|HH/, paddingZero(this.getHours()));
    str = str.replace(/mm/, paddingZero(this.getMinutes()));
    str = str.replace(/ss/, paddingZero(this.getSeconds()));
    str = str.replace(/SS/, paddingZero(this.getMilliseconds()));
    return str;
  };
  
  window.device_id = "********:5555";
  window.query_param = "";
  
  const videoConfig = {
    recorder_enable: false,
    recorder_format: "mp4",
    audio: true,
    video_codec: "h264",
    audio_codec: "aac",
    audio_source: "output",
    max_size: 720,
    video_bit_rate: 800000,
    audio_bit_rate: 128000,
    max_fps: 25,
    tunnel_forward: true,
    crop: "",
    control: true,
    show_touches: false,
    stay_awake: true,
    video_codec_options: "profile=1,level=2",
    audio_codec_options: "",
    video_encoder: "",
    audio_encoder: "",
    power_off_on_close: false,
    clipboard_autosync: false,
    power_on: true
  };
  
  const encodedConfig = encodeURIComponent(JSON.stringify(videoConfig));
  window.query_param = `?config=${encodedConfig}`;
  window.query_param_dict = JSON.parse(decodeURIComponent(window.query_param.split('=')[1]))
  
  console.log("scrcpy_kwargs:", window.query_param_dict)
  
  document.getElementById('refresh_button').addEventListener('click', reload);
  document.getElementById('capture_button').addEventListener('click', button_handle_capture);
  
  if (window.query_param_dict['control'] != false){
    add_button_mouse_event(document.getElementById('menu_button'), 187);
    add_button_mouse_event(document.getElementById('home_button'), 3);
    add_button_mouse_event(document.getElementById('back_button'), 4);
    add_button_mouse_event(document.getElementById('v+_button'), 24);
    add_button_mouse_event(document.getElementById('v-_button'), 25);
    add_button_mouse_event(document.getElementById('power_button'), 26);
  }
}
</script>

<script>
// ============ 音频播放器 ============
function load_audio_player(){
  window.audio_player = null
  if (window.query_param_dict['audio'] != false){
    if (window.query_param_dict['audio_codec'] == 'raw'){
      window.audio_player = new PCMPlayer({encoding: '16bitInt', channels: 2, sampleRate: 48000, flushingTime: 70 });
      window.audio_player_feed = function(data){
        window.audio_player.feed(data)
      };
    }
    else if (window.query_param_dict['audio_codec'] == 'opus'){
      try{
        window.audio_decoder = new AudioDecoder({
          error(error) { console.log("audio decoder error: ", error); },
          output(output) {
            const options = { format: "f32", planeIndex: 0, };
            const buffer = new Float32Array( output.allocationSize(options) / Float32Array.BYTES_PER_ELEMENT );
            output.copyTo(buffer, options);
            window.audio_player.feed(buffer)
          },
        });
        window.audio_player = new PCMPlayer({encoding: '32bitFloat', channels: 2, sampleRate: 48000, flushingTime: 30});
        window.audio_decoder.configure({codec: 'opus', numberOfChannels: 2, sampleRate: 48000, })
        window.audio_player_feed = function(data){
          if (data[0] == 252){
            const chunk = new EncodedAudioChunk({ type: "key", timestamp: 0, data: data })
            window.audio_decoder.decode(chunk)
          }
        }
      } catch (e) {
        document.getElementById("error_msg1").style.display='block';
        document.getElementById("error_msg1").innerHTML = "Error: no audio webcodecs support!";
        console.log(e)
      }
    }
    else if (window.query_param_dict['audio_codec'] == 'aac'){
      try{
        window.audio_decoder = new AudioDecoder({
          error(error) { console.log("audio decoder error: ", error); },
          output(output) {
            const options = { format: "f32-planar", planeIndex: 0, };
            const planar_buffer = new Float32Array(output.allocationSize(options) / Float32Array.BYTES_PER_ELEMENT);
            output.copyTo(planar_buffer, options);
            const buffer = new Float32Array(planar_buffer.length*2)
            for (let i = 0; i < planar_buffer.length; i++){
              buffer[2*i] = buffer[2*i+1] = planar_buffer[i]
            }
            window.audio_player.feed(buffer)
          },
        });
        window.audio_player = new PCMPlayer({encoding: '32bitFloat', channels: 2, sampleRate: 48000, flushingTime: 20});
        window.audio_player_feed = function(data){
          if (data[0]==17){
            window.audio_decoder.configure({codec: 'mp4a.66', numberOfChannels: 2, sampleRate: 48000, description:data})
          }
          else {
            const chunk = new EncodedAudioChunk({ type: "key", timestamp: 0, data: data})
            window.audio_decoder.decode(chunk)
          }
        }
      } catch (e) {
        console.log(e)
        document.getElementById("error_msg1").style.display='block';
        document.getElementById("error_msg1").innerHTML = "Error: no audio webcodecs support!";
      }
    }
  }
}
</script>

<script>
// ============ 视频处理函数 ============
function toHex(value) {
  return value.toString(16).padStart(2, "0").toUpperCase();
}

function toUint32Le(data, offset) {
  return (
    data[offset] |
    (data[offset + 1] << 8) |
    (data[offset + 2] << 16) |
    (data[offset + 3] << 24)
  );
}

function handle_config_data(data){
  if (window.video_config_data !== undefined){
    const fix_data = new Uint8Array(window.video_config_data.byteLength + data.byteLength)
    fix_data.set(window.video_config_data, 0)
    fix_data.set(data, window.video_config_data.byteLength)
    data = fix_data
    window.video_config_data = undefined
  }
  return data
}

function attach_canvas(canvas){
  const playerElement = document.getElementById('container');
  const controlBar = playerElement.querySelector('.control-bar');
  playerElement.innerHTML = "";
  playerElement.appendChild(canvas);
  if(controlBar) {
    playerElement.appendChild(controlBar);
  }
  window.video_renderer_canvas = canvas;
  
  if (window.query_param_dict['control'] != false){
    add_canvas_touch_event(canvas);
    add_canvas_swipe_event(canvas);
    add_canvas_scroll_event(canvas);
  }
}

function load_video_player(){
  window.canvas_resolution = [0, 0];
  if (document.getElementById('video_play_select').value.startsWith('webcodecs')){
    window.video_player = new Player({ useWorker: true, webgl: 'auto', size: { width: 336, height: 720 }, workerFile: "/js/Decoder.js", preserveDrawingBuffer: true});
    window.video_player_feed = function(data){
      if (data[4]==103){
        const { profileIndex, constraintSet, levelIndex, croppedWidth, croppedHeight,} = window.h264ParseConfiguration(data);
        window.canvas_resolution = [croppedWidth, croppedHeight];
      }
      window.video_player.decode(data);
    }
    attach_canvas(window.video_player.canvas)
  }
  else{
    try{
      window.video_decoder = new VideoDecoder({
        output: function(frame){ window.video_renderer_context.drawImage(frame, 0, 0); frame.close(); },
        error: function(e) { console.log(e) }
      })
    } catch (e) {
      document.getElementById("error_msg1").style.display='block';
      document.getElementById("error_msg1").innerHTML = "Error: no video webcodecs support!";
      throw e;
    }
    
    if (window.query_param_dict['video_codec'] == 'h264'){
      window.video_player_feed = function(data){
        if (data[4]==103){
          const { profileIndex, constraintSet, levelIndex, croppedWidth, croppedHeight,} = window.h264ParseConfiguration(data);
          window.canvas_resolution = [croppedWidth, croppedHeight];
          const codec = `avc1.${[profileIndex, constraintSet, levelIndex].map(toHex).join("")}`
          window.video_decoder.configure({ codec: codec, optimizeForLatency: true })
          video_renderer_canvas = document.createElement("canvas")
          video_renderer_canvas.width = croppedWidth;
          video_renderer_canvas.height = croppedHeight;
          attach_canvas(video_renderer_canvas)
          window.video_config_data = data
          window.video_renderer_context = video_renderer_canvas.getContext("2d")
        }
        else{
          data = handle_config_data(data)
          chunk = new EncodedVideoChunk({ type: data[4] in [101, 69, 37] ? "key" : "delta", timestamp: 0, data:data})
          window.video_decoder.decode(chunk)
        }
      }
    }
    else if (window.query_param_dict['video_codec'] == 'h265'){
      window.video_player_feed = function(data){
        if (data[4]==64){
          const {
            generalProfileSpace,
            generalProfileIndex,
            generalProfileCompatibilitySet,
            generalTierFlag,
            generalLevelIndex,
            generalConstraintSet,
            croppedWidth,
            croppedHeight,
          } = window.h265ParseConfiguration(data);
          window.canvas_resolution = [croppedWidth, croppedHeight];
          const codec = [ "hev1",
            ["", "A", "B", "C"][generalProfileSpace] +
            generalProfileIndex.toString(),
            toUint32Le(generalProfileCompatibilitySet, 0).toString(16),
            (generalTierFlag ? "H" : "L") +
            generalLevelIndex.toString(),
            toUint32Le(generalConstraintSet, 0)
            .toString(16)
            .toUpperCase(),
            toUint32Le(generalConstraintSet, 4)
            .toString(16)
            .toUpperCase(),
          ].join(".")
          window.video_decoder.configure({ codec: codec, optimizeForLatency: true })
          video_renderer_canvas = document.createElement("canvas")
          video_renderer_canvas.width = croppedWidth;
          video_renderer_canvas.height = croppedHeight;
          attach_canvas(video_renderer_canvas)
          window.video_config_data = data
          window.video_renderer_context = video_renderer_canvas.getContext("2d")
        }
        else{
          data = handle_config_data(data)
          chunk = new EncodedVideoChunk({ type: data[4] in [38] ? "key" : "delta", timestamp: 0, data:data})
          window.video_decoder.decode(chunk)
        }
      }
    }
  }
}
</script>

<script>
// ============ WebSocket连接 ============
function load_websocket(){
  const ws_url = "ws://**************:8081/ws/" + window.device_id ;
  window.ws = new WebSocket(ws_url)
  window.ws.binaryType = 'arraybuffer'
  
  window.ws.onopen = function (e) {
    document.getElementById("error_msg2").style.display='none';
    window.player_start_date = new Date();
    window.data_size = 0
    window.frame_cnt = 0
    window.previous_data_size = 0
    window.previous_frame_cnt = 0
    console.log('ws:Client connected')
  }

  window.ws.onerror = function(error) {
    console.error('WebSocket 错误:', error);
  }
  
  window.ws.onmessage = function (msg) {
    unit8_data = new Uint8Array(msg.data)
    window.data_size += unit8_data.length
    start_code = unit8_data.slice(0, 5).join('')
    
    if (start_code.startsWith('0001')){
      window.frame_cnt += 1
      window.video_player_feed(unit8_data)
    }
    else if(start_code.startsWith('0002')){
      data = unit8_data.slice(5)
      if (start_code.endsWith('0')){
        string_data = new TextDecoder("utf-8").decode(data);
        console.log("获取剪贴板内容:", string_data)
      }
      else if(start_code.endsWith('1')){
        console.log("paste_sequence:", data)
      }
      else if(start_code.endsWith('2')){
        recorder_filename = String.fromCharCode.apply(null, data)
        console.log("recorder_filename-->: ", recorder_filename)
      }
    }
    else if(start_code.startsWith('0003')){
      if (window.audio_player){
        window.audio_player_feed(unit8_data.slice(4))
      }
    }
  }
  
  window.ws.onclose = function (e) {
    window.player_start_date = null;
    window.data_size = 0
    window.frame_cnt = 0 
    window.previous_data_size = 0
    window.previous_frame_cnt = 0
    console.log('ws: Client disconnected')
    document.getElementById("error_msg2").style.display='block';
    document.getElementById("error_msg2").innerHTML = "Client disconnected";
  }
}
</script>

<script>
// ============ 系统控制函数 ============
function reload(){
  window.ws.onclose = function (e) {
    console.log('ws: Client disconnected')
  }
  window.ws.close()
  document.getElementById("error_msg1").style.display='none';
  load_audio_player()
  load_video_player()
  load_websocket()
}

function reload_audio_player(){
  if (window.audio_player != null){
    window.audio_player.init(window.audio_player.option)
  }
}

function flush_duration(){
  if (window.player_start_date){
    date_now = new Date();
    document.getElementById("player_duration").innerHTML = parseInt((date_now - window.player_start_date)/1000);
  }else{
    document.getElementById("player_duration").innerHTML = 0;
  }
  
  fps = "fps: " + (window.frame_cnt-window.previous_frame_cnt).toString() + '; '
  window.previous_frame_cnt = window.frame_cnt
  rate = "bit_rate: " + ((window.data_size-window.previous_data_size)/1024).toFixed(1).toString() + 'KB/s; '
  window.previous_data_size = window.data_size
  size = "data_size: " + (window.data_size/(1024*1024)).toFixed(1).toString() + 'Mb'
  console.log("Player_state --> ", fps+rate+size)
}

window.onload = function(){
  setInterval(flush_duration, 1000);
  load_utils()
  load_audio_player()
  load_video_player()
  load_websocket()
}
</script>

<script>
// ============ 录像功能 ============
let mediaRecorder = null;
let recordedChunks = [];

function startRecording() {
  const canvas = window.video_renderer_canvas;
  const stream = canvas.captureStream(30);
  recordedChunks = [];
  
  try {
    mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'video/webm;codecs=vp9',
      videoBitsPerSecond: 2500000
    });
  } catch (e) {
    try {
      mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'video/webm;codecs=vp8',
      });
    } catch (e) {
      console.error('录制功能不支持:', e);
      return;
    }
  }

  mediaRecorder.ondataavailable = (event) => {
    if (event.data.size > 0) {
      recordedChunks.push(event.data);
    }
  };

  mediaRecorder.onstop = () => {
    const blob = new Blob(recordedChunks, {
      type: 'video/webm'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = `screen-record-${new Date().toISOString()}.webm`;
    document.body.appendChild(a);
    a.click();
    setTimeout(() => {
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    }, 100);
  };

  mediaRecorder.start();
  
  const recordButton = document.getElementById('record_button');
  recordButton.classList.add('recording');
  recordButton.title = "停止录像";
}

function stopRecording() {
  if (mediaRecorder && mediaRecorder.state !== 'inactive') {
    mediaRecorder.stop();
    const recordButton = document.getElementById('record_button');
    recordButton.classList.remove('recording');
    recordButton.title = "开始录像";
  }
}

document.addEventListener('DOMContentLoaded', () => {
  const recordButton = document.getElementById('record_button');
  recordButton.addEventListener('click', () => {
    if (!mediaRecorder || mediaRecorder.state === 'inactive') {
      startRecording();
    } else {
      stopRecording();
    }
  });
  
  const qualityBtn = document.getElementById('quality_button');
  const popup = document.getElementById('quality-popup');
  
  qualityBtn.addEventListener('click', function(e) {
    e.stopPropagation();
    popup.style.display = popup.style.display === 'none' ? 'block' : 'none';
  });
  
  popup.addEventListener('click', function(e) {
    if (e.target && e.target.dataset.quality) {
      const quality = JSON.parse(e.target.dataset.quality);
      switch_quality(quality);
      popup.style.display = 'none';
    }
  });
  
  document.body.addEventListener('click', function() {
    popup.style.display = 'none';
  });
  
  const clipboardGetBtn = document.getElementById('clipboard_get_button');
  clipboardGetBtn.addEventListener('click', button_handle_get_clipboard);
  
  const clipboardSetBtn = document.getElementById('clipboard_set_button');
  clipboardSetBtn.addEventListener('click', button_handle_set_clipboard);
});
</script>

</body></html>