package com.example.shiqianyun.ui.components

import android.app.Activity
import android.content.pm.ActivityInfo
import android.util.Log
import android.view.Surface
import android.view.SurfaceHolder
import android.view.SurfaceView
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import com.example.shiqianyun.common.AppConstants
import com.example.shiqianyun.decoder.H264Decoder
import com.example.shiqianyun.network.WebSocketManager
import com.example.shiqianyun.manager.TouchEventHandler
import kotlinx.coroutines.launch

@Composable
fun VideoStreamingScreen(wsUrl: String = "") {
    var isConnected by remember { mutableStateOf(false) }
    val webSocketManager = remember { WebSocketManager() }
    val decoderRef = remember { mutableStateOf<H264Decoder?>(null) }
    var surfaceRef by remember { mutableStateOf<Surface?>(null) }
    var isLandscape by remember { mutableStateOf(false) }
    var touchEventHandler by remember { mutableStateOf<TouchEventHandler?>(null) }

    val scope = rememberCoroutineScope()
    val lifecycleOwner = LocalLifecycleOwner.current
    val context = LocalContext.current
    
    // 屏幕方向变化处理函数
    val handleOrientationChange: (Boolean) -> Unit = { landscape: Boolean ->
        Log.d("VideoStreamingScreen", "Orientation change requested: ${if (landscape) "横屏" else "竖屏"}")
        isLandscape = landscape
        
        // 设置Activity的屏幕方向
        (context as? Activity)?.let { activity ->
            activity.requestedOrientation = if (landscape) {
                ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
            } else {
                ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
            }
        }
    }
    
    // 自动连接函数
    val connectToStream = {
        surfaceRef?.let { surf ->
            val decoder = H264Decoder(
                surface = surf, 
                onOrientationChanged = handleOrientationChange,
                onResolutionChanged = { width, height ->
                    Log.d("VideoStreamingScreen", "Resolution changed: ${width}x${height}")
                    touchEventHandler?.updateH264Resolution(width, height)
                }
            )
            decoderRef.value = decoder
            if (decoder.start()) {
                if (wsUrl.isNotEmpty()) {
                    // 如果传递了完整的 wsUrl，直接使用它连接
                    Log.d("VideoStreamingScreen", "Connecting to full URL: $wsUrl")
                    webSocketManager.connectWithFullUrl(
                        fullUrl = wsUrl,
                        decoder = decoder,
                        scope = scope,
                        onConnectionStateChanged = { connected ->
                            isConnected = connected
                            if (!connected) {
                                decoderRef.value?.release()
                            }
                        }
                    )
                } else {
                    // 使用默认地址，通过拼接方式连接
                    Log.d("VideoStreamingScreen", "Connecting to default: ${AppConstants.ConnectAddress}")
                    webSocketManager.connect(
                        urlParam = AppConstants.ConnectAddress,
                        decoder = decoder,
                        scope = scope,
                        onConnectionStateChanged = { connected ->
                            isConnected = connected
                            if (!connected) {
                                decoderRef.value?.release()
                            }
                        }
                    )
                }
            } else {
                Log.e("VideoStreamingScreen", "Decoder failed to start")
                decoderRef.value?.release()
            }
        } ?: run {
            Log.e("VideoStreamingScreen", "Connect failed: Surface is not ready yet.")
        }
    }
    
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_START -> {
                    // 应用启动时自动连接
                    Log.d("Lifecycle", "ON_START: Auto-connecting to WebSocket.")
                    connectToStream()
                }
                Lifecycle.Event.ON_DESTROY -> {
                    Log.d("Lifecycle", "ON_DESTROY: Disconnecting WebSocket.")
                    webSocketManager.disconnect()
                    touchEventHandler?.release()
                }
                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    // 全屏显示，黑色背景，撑满整个屏幕包括状态栏区域
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        AndroidView(
            factory = { context ->
                Log.d("AndroidView", "Factory: Creating SurfaceView.")
                SurfaceView(context).apply {
                    holder.addCallback(object : SurfaceHolder.Callback {
                        override fun surfaceCreated(holder: SurfaceHolder) {
                            Log.d("SurfaceView", "surfaceCreated: Surface is ready.")
                            surfaceRef = holder.surface
                            
                            // 初始化TouchEventHandler
                            touchEventHandler = TouchEventHandler(
                                surfaceView = this@apply,
                                webSocketManager = webSocketManager,
                                onUserActivity = {
                                    Log.d("TouchEventHandler", "User activity detected")
                                }
                            )
                            
                            // 设置触摸事件监听器
                            setOnTouchListener { _, event ->
                                touchEventHandler?.handleTouchEvent(event) ?: false
                            }
                            
                            // Surface创建后自动连接
                            connectToStream()
                        }

                        override fun surfaceChanged(
                            holder: SurfaceHolder,
                            format: Int,
                            width: Int,
                            height: Int
                        ) {
                            Log.d(
                                "SurfaceView",
                                "surfaceChanged: format=$format, width=$width, height=$height"
                            )
                        }

                        override fun surfaceDestroyed(holder: SurfaceHolder) {
                            Log.d("SurfaceView", "surfaceDestroyed: Disconnecting WebSocket.")
                            webSocketManager.disconnect()
                            surfaceRef = null
                        }
                    })
                }
            },
            modifier = Modifier.fillMaxSize()
        )
    }
}