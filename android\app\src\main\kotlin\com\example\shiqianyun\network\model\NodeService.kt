package com.example.shiqianyun.network.model

import com.google.gson.annotations.SerializedName

/**
 * 节点服务信息
 */
data class NodeService(
    @SerializedName("service_id") val serviceId: String,
    @SerializedName("consul_addr") val consulAddr: String,
    @SerializedName("consul_port") val consulPort: Int,
    @SerializedName("service_name") val serviceName: String,
    @SerializedName("service_addr") val serviceAddr: String,
    @SerializedName("service_port") val servicePort: Int,
    @SerializedName("network_usage") val networkUsage: Double,
    @SerializedName("cpu_usage") val cpuUsage: Double,
    @SerializedName("memory_usage") val memoryUsage: Double,
    @SerializedName("disk_usage") val diskUsage: Double,
    @SerializedName("connection_count") val connectionCount: Int,
    @SerializedName("user_count") val userCount: Int,
    @SerializedName("max_connections") val maxConnections: Int,
    @SerializedName("max_conn_time") val maxConnTime: String,
    @SerializedName("max_network_usage") val maxNetworkUsage: Double,
    @SerializedName("temperature") val temperature: Double,
    @SerializedName("status") val status: String,
    @SerializedName("tags") val tags: List<String>
)