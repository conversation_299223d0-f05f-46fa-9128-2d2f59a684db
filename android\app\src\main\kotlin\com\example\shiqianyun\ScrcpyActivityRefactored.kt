package com.example.shiqianyun

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.example.shiqianyun.ui.components.VideoStreamingScreen
import com.example.shiqianyun.ui.theme.WsMyAndroidTheme
import com.example.shiqianyun.network.model.BitrateConfig

class ScrcpyActivityRefactored : ComponentActivity() {
    companion object {
        private const val EXTRA_WS_URL = "extra_ws_url"
        private const val EXTRA_WIDTH = "extra_width"
        private const val EXTRA_HEIGHT = "extra_height"
        private const val EXTRA_BITRATE_CONFIG = "extra_bitrate_config"
        private const val EXTRA_USER_ID = "extra_user_id"
        private const val EXTRA_PHONE_ID = "extra_phone_id"
        private const val EXTRA_PHONE_IDENTIFY = "extra_phone_identify"
        private const val EXTRA_NICK_NAME = "extra_nick_name"

        fun startActivity(
            context: Context,
            wsUrl: String,
            width: Int,
            height: Int,
            bitrateConfigList: ArrayList<BitrateConfig>,
            userId: String,
            phoneId: String,
            phoneIdentify: String,
            nickName: String
        ) {
            val intent = Intent(context, ScrcpyActivityRefactored::class.java).apply {
                putExtra(EXTRA_WS_URL, wsUrl)
                putExtra(EXTRA_WIDTH, width)
                putExtra(EXTRA_HEIGHT, height)
                putExtra(EXTRA_BITRATE_CONFIG, bitrateConfigList)
                putExtra(EXTRA_USER_ID, userId)
                putExtra(EXTRA_PHONE_ID, phoneId)
                putExtra(EXTRA_PHONE_IDENTIFY, phoneIdentify)
                putExtra(EXTRA_NICK_NAME, nickName)
            }
            context.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置全屏模式
        setupFullScreen()
        
        // 获取传递过来的参数
        val wsUrl = intent.getStringExtra(EXTRA_WS_URL) ?: ""
        val width = intent.getIntExtra(EXTRA_WIDTH, 1920)
        val height = intent.getIntExtra(EXTRA_HEIGHT, 1080)
        
        setContent {
            WsMyAndroidTheme {
                // 直接使用 VideoStreamingScreen，不包装在 Surface 中
                VideoStreamingScreen(wsUrl = wsUrl)
            }
        }
    }
    
    private fun setupFullScreen() {
        // 启用边到边显示
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        // 设置窗口标志，强制全屏
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN or 
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN or 
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN
        )
        
        // 获取窗口控制器
        val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
        
        // 隐藏系统栏（状态栏和导航栏）
        windowInsetsController.hide(WindowInsetsCompat.Type.systemBars())
        
        // 设置系统栏行为：滑动时显示，自动隐藏
        windowInsetsController.systemBarsBehavior = 
            WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
        
        // 保持屏幕常亮
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
    }
}