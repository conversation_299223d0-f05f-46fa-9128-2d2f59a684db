{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/idedManger/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/idedManger/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/idedManger/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/idedManger/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-a21e1a602ff0ba311047.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-b90f2c3eb40b90275e4a.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-8bceb83e425b8142e5d1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-b90f2c3eb40b90275e4a.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-8bceb83e425b8142e5d1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-a21e1a602ff0ba311047.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}