package com.example.shiqianyun.decoder.codec

import android.media.MediaCodec
import android.media.MediaCodecInfo
import android.media.MediaFormat
import android.util.Log
import android.view.Surface
import java.nio.ByteBuffer

/**
 * MediaCodec管理器
 * 负责H.264硬件解码器的配置和管理
 */
class MediaCodecManager(private val surface: Surface) {
    private var mediaCodec: MediaCodec? = null
    private var isConfigured = false
    private var frameIndex = 0L
    
    companion object {
        private const val TAG = "MediaCodecManager"
        private const val MIME_TYPE = "video/avc"
    }
    
    /**
     * 配置MediaCodec解码器
     */
    fun configureCodec(
        sps: ByteArray, 
        pps: ByteArray, 
        width: Int, 
        height: Int,
        forceReconfigure: Boolean = false
    ): Bo<PERSON>an {
        return try {
            // 如果强制重新配置或未配置，则重置现有的MediaCodec
            if (forceReconfigure || !isConfigured) {
                release()
            } else if (isConfigured) {
                Log.d(TAG, "MediaCodec already configured, skipping...")
                return true
            }
            
            Log.d(TAG, "Configuring MediaCodec with resolution: ${width}x${height}")
            
            // 创建 CSD buffers
            val csd0 = ByteBuffer.allocate(sps.size + 4)
            csd0.put(byteArrayOf(0, 0, 0, 1))
            csd0.put(sps)
            csd0.flip()
            
            val csd1 = ByteBuffer.allocate(pps.size + 4)
            csd1.put(byteArrayOf(0, 0, 0, 1))
            csd1.put(pps)
            csd1.flip()

            // 创建并配置 MediaCodec
            mediaCodec = MediaCodec.createDecoderByType(MIME_TYPE).apply {
                val format = MediaFormat.createVideoFormat(MIME_TYPE, width, height).apply {
                    setByteBuffer("csd-0", csd0)
                    setByteBuffer("csd-1", csd1)
                    // 设置颜色格式
                    setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatSurface)
                    // 设置最大输入尺寸
                    setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 1024 * 1024)
                }
                
                Log.d(TAG, "MediaFormat: $format")
                configure(format, surface, null, 0)
                start()
            }
            
            isConfigured = true
            frameIndex = 0
            Log.d(TAG, "MediaCodec configured successfully with SPS/PPS, resolution: ${width}x${height}")
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "Failed to configure MediaCodec", e)
            isConfigured = false
            release()
            false
        }
    }
    
    /**
     * 解码NAL单元
     */
    fun decode(nalUnit: ByteArray): Boolean {
        if (!isConfigured) {
            Log.w(TAG, "MediaCodec not configured")
            return false
        }
        
        return try {
            val codec = mediaCodec ?: return false
            
            // 创建带启动码的NAL单元
            val nalWithStartCode = ByteArray(nalUnit.size + 4)
            nalWithStartCode[0] = 0
            nalWithStartCode[1] = 0
            nalWithStartCode[2] = 0
            nalWithStartCode[3] = 1
            System.arraycopy(nalUnit, 0, nalWithStartCode, 4, nalUnit.size)
            
            // 输入数据到解码器
            val inputBufferIndex = codec.dequeueInputBuffer(10000) // 10ms timeout
            if (inputBufferIndex >= 0) {
                val inputBuffer = codec.getInputBuffer(inputBufferIndex)
                inputBuffer?.clear()
                inputBuffer?.put(nalWithStartCode)
                
                // 使用递增的时间戳，避免时间戳问题
                val presentationTimeUs = frameIndex * 33333 // 30fps: 1/30 * 1000000
                
                codec.queueInputBuffer(
                    inputBufferIndex,
                    0,
                    nalWithStartCode.size,
                    presentationTimeUs,
                    0
                )
                
                frameIndex++
                // Log.d(TAG, "Queued ${nalWithStartCode.size} bytes to input buffer $inputBufferIndex, frame: $frameIndex")
            } else {
                Log.w(TAG, "No input buffer available")
                return false
            }

            // 处理输出
            val bufferInfo = MediaCodec.BufferInfo()
            var outputBufferIndex = codec.dequeueOutputBuffer(bufferInfo, 0)
            while (outputBufferIndex >= 0) {
                when (outputBufferIndex) {
                    MediaCodec.INFO_OUTPUT_FORMAT_CHANGED -> {
                        val format = codec.outputFormat
                        Log.d(TAG, "Output format changed: $format")
                    }
                    MediaCodec.INFO_TRY_AGAIN_LATER -> {
                        Log.d(TAG, "dequeueOutputBuffer returned INFO_TRY_AGAIN_LATER")
                        break
                    }
                    else -> {
                        // 渲染帧到Surface
                        codec.releaseOutputBuffer(outputBufferIndex, true)
                        // Log.d(TAG, "Rendered frame $frameIndex to surface")
                    }
                }
                outputBufferIndex = codec.dequeueOutputBuffer(bufferInfo, 0)
            }
            
            true
            
        } catch (e: Exception) {
            Log.e(TAG, "Decoding error", e)
            false
        }
    }
    
    /**
     * 检查是否已配置
     */
    fun isConfigured(): Boolean = isConfigured
    
    /**
     * 释放MediaCodec资源
     */
    fun release() {
        try {
            mediaCodec?.stop()
            mediaCodec?.release()
            mediaCodec = null
            isConfigured = false
            frameIndex = 0
            Log.d(TAG, "MediaCodec released")
        } catch (e: Exception) {
            Log.e(TAG, "Error releasing MediaCodec", e)
        }
    }
}