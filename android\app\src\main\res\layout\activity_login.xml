<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/login_background"
    android:padding="20dp"
    tools:context=".LoginActivity">

    <!-- 顶部Logo -->
    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:layout_marginTop="20dp"
        android:src="@drawable/yunshouji"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 应用名称 -->
    <TextView
        android:id="@+id/tv_app_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:text="史前云手机"
        android:textColor="#1A1A1A"
        android:textSize="18sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/iv_logo" />

    <!-- 登录方式切换 -->
    <LinearLayout
        android:id="@+id/layout_login_toggle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:background="@drawable/toggle_background"
        android:orientation="horizontal"
        android:padding="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_app_name">

        <TextView
            android:id="@+id/tv_phone_login"
            android:layout_width="110dp"
            android:layout_height="32dp"
            android:background="@drawable/toggle_selected"
            android:gravity="center"
            android:text="手机号登录"
            android:textColor="#FFFFFF"
            android:textSize="13sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tv_account_login"
            android:layout_width="110dp"
            android:layout_height="32dp"
            android:gravity="center"
            android:text="账号密码登录"
            android:textColor="#6B7280"
            android:textSize="13sp" />
    </LinearLayout>

    <!-- 主要登录卡片 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_login"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="6dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_login_toggle">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- 手机号登录表单 -->
            <LinearLayout
                android:id="@+id/layout_phone_form"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="visible">

                <!-- 手机号输入框 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_phone"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="手机号"
                    app:boxCornerRadiusBottomEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxStrokeColor="@color/input_stroke_color"
                    app:errorEnabled="true"
                    app:hintTextColor="#6B7280">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_phone"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="phone"
                        android:maxLines="1"
                        android:textColor="#1A1A1A"
                        android:textSize="16sp" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 验证码输入框和获取验证码按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/til_code"
                        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="12dp"
                        android:layout_weight="1"
                        android:hint="验证码"
                        app:boxCornerRadiusBottomEnd="12dp"
                        app:boxCornerRadiusBottomStart="12dp"
                        app:boxCornerRadiusTopEnd="12dp"
                        app:boxCornerRadiusTopStart="12dp"
                        app:boxStrokeColor="@color/input_stroke_color"
                        app:errorEnabled="true"
                        app:hintTextColor="#6B7280">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/et_code"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:maxLines="1"
                            android:textColor="#1A1A1A"
                            android:textSize="16sp" />
                    </com.google.android.material.textfield.TextInputLayout>

                    <Button
                        android:id="@+id/btn_get_code"
                        style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                        android:layout_width="wrap_content"
                        android:layout_height="56dp"
                        android:background="@drawable/button_outlined"
                        android:text="获取验证码"
                        android:textColor="#3B82F6"
                        android:textSize="13sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- 账号密码登录表单 -->
            <LinearLayout
                android:id="@+id/layout_account_form"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- 用户名输入框 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_username"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="12dp"
                    android:hint="用户名/手机号"
                    app:boxCornerRadiusBottomEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxStrokeColor="@color/input_stroke_color"
                    app:errorEnabled="true"
                    app:hintTextColor="#6B7280">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_username"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text"
                        android:maxLines="1"
                        android:textColor="#1A1A1A"
                        android:textSize="16sp" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 密码输入框 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/til_password"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="密码"
                    app:boxCornerRadiusBottomEnd="12dp"
                    app:boxCornerRadiusBottomStart="12dp"
                    app:boxCornerRadiusTopEnd="12dp"
                    app:boxCornerRadiusTopStart="12dp"
                    app:boxStrokeColor="@color/input_stroke_color"
                    app:errorEnabled="true"
                    app:hintTextColor="#6B7280"
                    app:passwordToggleEnabled="true">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/et_password"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textPassword"
                        android:maxLines="1"
                        android:textColor="#1A1A1A"
                        android:textSize="16sp" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 记住密码和注册 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <CheckBox
                        android:id="@+id/cb_remember_password"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:buttonTint="#3B82F6"
                        android:text="记住密码"
                        android:textColor="#6B7280"
                        android:textSize="13sp" />

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1" />

                    <TextView
                        android:id="@+id/tv_register_prompt"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="没有账号？立即注册"
                        android:textColor="#3B82F6"
                        android:textSize="13sp" />
                </LinearLayout>
            </LinearLayout>

            <!-- 登录按钮 -->
            <Button
                android:id="@+id/btn_login"
                android:layout_width="match_parent"
                android:layout_height="44dp"
                android:background="@drawable/button_primary"
                android:text="登录"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- 用户协议 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:gravity="center"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/iv_agreement"
                    android:layout_width="16dp"
                    android:layout_height="16dp"
                    android:src="@drawable/checkbox_checked" />

                <TextView
                    android:id="@+id/tv_agreement"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="6dp"
                    android:text="我已阅读并同意《用户协议》和《隐私政策》"
                    android:textColor="#9CA3AF"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- 其他登录方式 -->
    <LinearLayout
        android:id="@+id/layout_other_login"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:gravity="center"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/card_login"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintVertical_bias="0.3">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:text="其他登录方式"
            android:textColor="#9CA3AF"
            android:textSize="12sp" />

        <LinearLayout
            android:id="@+id/layout_wechat"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="@drawable/wechat_login_bg"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:src="@drawable/wechat_icon" />
        </LinearLayout>
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>