package com.example.shiqianyun.network.model

import com.google.gson.annotations.SerializedName

// 消费记录响应
data class ConsumptionRecordResponse(
    val code: Int,
    val data: ConsumptionData,
    val msg: String
)

// 消费记录数据
data class ConsumptionData(
    val list: List<ConsumptionRecord>,
    val page: Int,
    @SerializedName("pageSize") val pageSize: Int,
    val total: Int,
    val user: String
)

// 单条消费记录
data class ConsumptionRecord(
    val id: Int,
    val title: String? = null, // 标题可能为空，需要根据pay_way生成
    @SerializedName("created_at") val createTime: String,
    val content: String? = null, // 内容可能为空，需要根据activate_codes生成
    @SerializedName("device_designation") val deviceDesignation: String? = null,
    @SerializedName("device_id") val deviceId: Int? = null,
    val duration: String? = null, // 时长可能为空，需要根据buy_day和time_type生成
    @SerializedName("pay_way") val payWay: String,
    @SerializedName("total_price") val totalPrice: Int = 0,
    @SerializedName("buy_day") val buyDay: Int = 0,
    @SerializedName("buy_num") val buyNum: Int = 1,
    @SerializedName("is_renew") val isRenew: Boolean? = false,
    val vip: Int = 0,
    @SerializedName("order_num") val orderNum: String = "",
    @SerializedName("activate_codes") val activateCodes: List<ActivationCode>? = emptyList(),
    val devices: List<ConsumptionDeviceInfo>? = emptyList(),
    @SerializedName("device_id_list") val deviceIdList: List<Int>? = emptyList(),
    @SerializedName("target_translate_id") val targetTranslateId: Int? = null
)

// 激活码信息
data class ActivationCode(
    val id: Int,
    val uuid: String,
    @SerializedName("buy_day") val buyDay: Int,
    @SerializedName("time_type") val timeType: Int // 0是小时，1是天
)

// 设备信息
data class ConsumptionDeviceInfo(
    val id: Int,
    @SerializedName("device_designation") val deviceDesignation: String? = null,
    @SerializedName("phone_id") val phone_id: Int? = null
) 