package com.example.shiqianyun

import android.annotation.SuppressLint
import android.content.*
import android.content.pm.ActivityInfo
import android.content.pm.PackageManager
import android.content.res.Configuration
import android.os.*
import android.util.Log
import android.view.View
import android.view.SurfaceHolder
import android.view.SurfaceView
import android.view.WindowManager
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import com.example.shiqianyun.manager.*
import com.example.shiqianyun.network.model.BitrateConfig
import org.json.JSONObject
import java.nio.charset.StandardCharsets
import android.Manifest

class ScrcpyActivityRefactored : AppCompatActivity(), SurfaceHolder.Callback {
    companion object {
        private const val TAG = "ScrcpyActivityRefactored"
        
        // WebSocket常量
        private const val WS_URL_KEY = "ws_url"
        private const val DEVICE_WIDTH_KEY = "device_width"
        private const val DEVICE_HEIGHT_KEY = "device_height"
        private const val BITRATE_CONFIGS_KEY = "bitrate_configs"
        private const val DEVICE_NAME_KEY = "device_name"
        
        // 启动Activity的工厂方法
        fun startActivity(
            context: Context, 
            wsUrl: String, 
            width: Int, 
            height: Int, 
            bitrateConfigs: ArrayList<BitrateConfig>? = null,
            userId: Int = 0,
            deviceId: Int = 0,
            deviceOnlyCode: String = "",
            deviceName: String = ""
        ) {
            val intent = Intent(context, ScrcpyActivityRefactored::class.java).apply {
                putExtra(WS_URL_KEY, wsUrl)
                putExtra(DEVICE_WIDTH_KEY, width)
                putExtra(DEVICE_HEIGHT_KEY, height)
                putParcelableArrayListExtra(BITRATE_CONFIGS_KEY, bitrateConfigs)
                putExtra("user_id", userId)
                putExtra("device_id", deviceId)
                putExtra("device_only_code", deviceOnlyCode)
                putExtra(DEVICE_NAME_KEY, deviceName)
            }
            context.startActivity(intent)
        }
    }
    
    // 管理器实例
    private lateinit var webSocketManager: WebSocketManager
    private lateinit var videoDecoderManager: VideoDecoderManager
    private lateinit var touchEventHandler: TouchEventHandler
    private lateinit var uiController: UIController
    private lateinit var audioManager: AudioManager
    private lateinit var trafficStatsManager: TrafficStatsManager
    
    // UI组件
    private lateinit var surfaceView: SurfaceView
    
    // 参数
    private var wsUrl: String = ""
    private var deviceWidth: Int = 0
    private var deviceHeight: Int = 0
    private var realWidth: Int = 0
    private var realHeight: Int = 0
    private var gotRealResolution: Boolean = false
    
    // 比特率配置
    private var bitrateConfigs: List<BitrateConfig> = emptyList()
    private var currentBitrateConfig: BitrateConfig? = null
    
    // 流量统计参数
    private var userId: Int = 0
    private var deviceId: Int = 0
    private var deviceOnlyCode: String = ""
    
    // 设备类型
    private var isNoAdbDevice = false
    
    // 状态管理
    private var wasInBackground = false
    private var isChangingQuality = false
    private var qualityChangeTimeoutHandler: Handler? = null
    private val QUALITY_CHANGE_TIMEOUT_MS = 2000L
    
    // 用户活动记录
    private var lastUserActivityTime: Long = 0
    
    // 剪贴板管理器
    private lateinit var clipboardManager: ClipboardManager

    @SuppressLint("ClickableViewAccessibility")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 设置全屏和沉浸式模式
        setupFullscreenMode()
        
        setContentView(R.layout.activity_scrcpy)
        
        // 请求存储权限
        requestStoragePermissions()
        
        // 获取传递的参数
        getIntentParameters()
        
        if (wsUrl.isEmpty()) {
            Toast.makeText(this, "参数错误，无法启动", Toast.LENGTH_SHORT).show()
            finish()
            return
        }
        
        // 判断设备类型
        isNoAdbDevice = wsUrl.contains("/session/")
        Log.d(TAG, "检测到设备类型: ${if (isNoAdbDevice) "免adb设备" else "adb设备"}")
        
        Log.d(TAG, "启动参数: wsUrl=$wsUrl, 分辨率=${deviceWidth}x${deviceHeight}")
        Log.d(TAG, "比特率配置: ${bitrateConfigs.size}个, 当前使用: ${currentBitrateConfig?.name}")
        
        // 初始化管理器
        initializeManagers()
        
        // 初始化UI
        initViews()
        
        // 初始化剪贴板管理器
        clipboardManager = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    }

    /**
     * 设置全屏和沉浸式模式
     */
    private fun setupFullscreenMode() {
        window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
        
        window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
    }

    /**
     * 请求存储权限
     */
    private fun requestStoragePermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (checkSelfPermission(Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(
                    arrayOf(
                        Manifest.permission.WRITE_EXTERNAL_STORAGE,
                        Manifest.permission.READ_EXTERNAL_STORAGE
                    ),
                    100
                )
            }
        }
    }

    /**
     * 获取Intent参数
     */
    private fun getIntentParameters() {
        wsUrl = intent.getStringExtra(WS_URL_KEY) ?: ""
        deviceWidth = intent.getIntExtra(DEVICE_WIDTH_KEY, 0)
        deviceHeight = intent.getIntExtra(DEVICE_HEIGHT_KEY, 0)
        
        @Suppress("DEPRECATION")
        bitrateConfigs = intent.getParcelableArrayListExtra<BitrateConfig>(BITRATE_CONFIGS_KEY) ?: emptyList()
        
        userId = intent.getIntExtra("user_id", 0)
        deviceId = intent.getIntExtra("device_id", 0)
        deviceOnlyCode = intent.getStringExtra("device_only_code") ?: ""
        
        currentBitrateConfig = if (bitrateConfigs.size > 1) bitrateConfigs[1] else bitrateConfigs.firstOrNull()
    }

    /**
     * 初始化所有管理器
     */
    private fun initializeManagers() {
        // 初始化WebSocket管理器
        webSocketManager = WebSocketManager(
            onConnected = ::onWebSocketConnected,
            onDisconnected = ::onWebSocketDisconnected,
            onError = ::onWebSocketError,
            onTextMessage = ::onWebSocketTextMessage,
            onBinaryMessage = ::onWebSocketBinaryMessage
        )
        
        // 初始化视频解码器管理器
        videoDecoderManager = VideoDecoderManager(
            surfaceView = findViewById(R.id.surface_view),
            activity = this,
            onDecoderConfigured = ::onDecoderConfigured,
            onDecodeSuccess = ::onDecodeSuccess,
            onDecodeFailure = ::onDecodeFailure,
            onResolutionChanged = ::onResolutionChanged
        )
        
        // 初始化触摸事件处理器
        touchEventHandler = TouchEventHandler(
            surfaceView = findViewById(R.id.surface_view),
            sendMessage = webSocketManager::sendMessage,
            onUserActivity = ::onUserActivity
        )
        
        // 初始化UI控制器
        uiController = UIController(
            activity = this,
            onKeyEvent = touchEventHandler::sendKeyEvent,
            onVolumeUp = ::increaseVolume,
            onVolumeDown = ::decreaseVolume,
            onQualityChange = ::changeResolution,
            onReconnect = ::reconnect,
            onExit = ::exitActivity,
            onGetClipboard = ::getRemoteClipboardContent,
            onSetClipboard = ::setRemoteClipboardContent,
            onContinueActivity = ::continueActivity,
            onExitInactivity = ::exitActivity,
            onLayoutChanged = ::onLayoutChanged
        )
        
        // 初始化音频管理器
        audioManager = AudioManager(this, isNoAdbDevice)
        audioManager.initialize()
        
        // 初始化流量统计管理器
        trafficStatsManager = TrafficStatsManager(
            userId = userId,
            deviceId = deviceId,
            deviceOnlyCode = deviceOnlyCode,
            onComplete = ::onTrafficStatsSaved
        )
    }

    /**
     * 初始化UI
     */
    private fun initViews() {
        surfaceView = findViewById(R.id.surface_view)
        
        // 初始化UI控制器
        uiController.initViews()
        uiController.setBitrateConfigs(bitrateConfigs)
        uiController.setupStatusBar(intent.getStringExtra(DEVICE_NAME_KEY) ?: "云手机")
        
        // 设置Surface回调
        surfaceView.holder.addCallback(this)
        
        // 设置触摸事件处理
        surfaceView.setOnTouchListener { _, event ->
            touchEventHandler.handleTouchEvent(event)
            true
        }
        
        // 更新触摸处理器的分辨率
        touchEventHandler.updateResolution(deviceWidth, deviceHeight)
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        super.onWindowFocusChanged(hasFocus)
        if (hasFocus) {
            // 重新应用沉浸式模式
            window.decorView.systemUiVisibility = (View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_FULLSCREEN
                or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY)
        }
    }

    // Surface回调实现
    override fun surfaceCreated(holder: SurfaceHolder) {
        Log.d(TAG, "Surface已创建")
        
        if (wasInBackground) {
            Log.d(TAG, "从后台恢复，重新连接WebSocket")
            webSocketManager.connect(wsUrl)
        } else if (!webSocketManager.isConnected()) {
            webSocketManager.connect(wsUrl)
        }
    }

    override fun surfaceChanged(holder: SurfaceHolder, format: Int, width: Int, height: Int) {
        Log.d(TAG, "Surface changed: width=$width, height=$height")
        
        // 只在Surface真正可用且尺寸有效时才通知解码器
        if (width > 0 && height > 0) {
            videoDecoderManager.notifySurfaceChanged()
        }
        
        // 调整SurfaceView大小
        if (gotRealResolution && realWidth > 0 && realHeight > 0) {
            Log.d(TAG, "调整Surface尺寸到: $realWidth x $realHeight")
            deviceWidth = realWidth
            deviceHeight = realHeight
            touchEventHandler.updateResolution(deviceWidth, deviceHeight, realWidth, realHeight)
            adjustSurfaceViewSize()
        }
    }

    override fun surfaceDestroyed(holder: SurfaceHolder) {
        Log.d(TAG, "Surface已销毁")
        videoDecoderManager.resetDecoder()
        webSocketManager.disconnect()
    }

    // WebSocket回调
    private fun onWebSocketConnected() {
        runOnUiThread {
            uiController.hideConnectionInfo()
            
            if (isChangingQuality && currentBitrateConfig != null) {
                Log.d(TAG, "画质切换连接成功")
                onQualityChangeSuccess(currentBitrateConfig!!)
            }
        }
    }

    private fun onWebSocketDisconnected(code: Int, reason: String?, remote: Boolean) {
        if (remote && !isFinishing && !wasInBackground) {
            Log.d(TAG, "WebSocket连接被远程关闭，退出页面")
            runOnUiThread {
                finish()
            }
        } else if (wasInBackground) {
            Log.d(TAG, "应用在后台，WebSocket关闭不触发退出")
        } else {
            Log.d(TAG, "本地主动关闭WebSocket连接")
        }
    }

    private fun onWebSocketError(ex: Exception?) {
        Log.e(TAG, "WebSocket连接错误", ex)
        runOnUiThread {
            uiController.showConnectionInfo("连接失败，请重试")
            Toast.makeText(this, "连接失败，请检查网络和设备状态", Toast.LENGTH_SHORT).show()
            finish()
        }
    }

    private fun onWebSocketTextMessage(message: String) {
        try {
            val jsonObject = JSONObject(message)
            val msgType = jsonObject.optInt("msg_type", -1)
            
            // 处理延迟测量响应
            if (message.contains("\"now\":")) {
                try {
                    val jsonData = JSONObject(message)
                    val serverTimestamp = jsonData.optLong("now", 0)
                    val latency = webSocketManager.handleLatencyResponse(serverTimestamp)
                    latency?.let { uiController.updateLatencyDisplay(it) }
                } catch (e: Exception) {
                    Log.e(TAG, "解析延迟测量响应失败", e)
                }
            }
            // 处理长时间未操作的消息
            else if (msgType == 400) {
                Log.d(TAG, "收到长时间未操作消息: $message")
                runOnUiThread {
                    uiController.showInactivityAlert()
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理WebSocket文本消息失败", e)
        }
    }

    private fun onWebSocketBinaryMessage(data: ByteArray) {
        try {
            // 检查是否是剪贴板响应数据
            if (data.size > 5 && 
                data[0] == 0x00.toByte() && data[1] == 0x00.toByte() && 
                data[2] == 0x00.toByte() && data[3] == 0x02.toByte()) {
                
                if (data[4] == 0x00.toByte()) {
                    handleGetClipboardResponse(data)
                    return
                } else if (data[4] == 0x01.toByte()) {
                    handleSetClipboardResponse(data)
                    return
                }
            }
            
            // 检测NAL单元
            if (data.size >= 5 &&
                data[0] == 0x00.toByte() &&
                data[1] == 0x00.toByte() &&
                data[2] == 0x00.toByte() &&
                data[3] == 0x01.toByte()) {
                
                decodeFrame(data)
            }
            // 检测ADTS音频头
            else if (data.size >= 7 &&
                    (data[0].toInt() and 0xFF) == 0xFF &&
                    (data[1].toInt() and 0xF0) == 0xF0) {
                
                audioManager.processAudioData(data)
            }
            // 自定义音频格式
            else if (data.size >= 4 &&
                    data[0] == 0x00.toByte() &&
                    data[1] == 0x00.toByte() &&
                    data[2] == 0x00.toByte() &&
                    data[3] == 0x03.toByte()) {
                
                val audioData = if (data.size > 4) {
                    data.copyOfRange(4, data.size)
                } else {
                    ByteArray(0)
                }
                audioManager.processAudioData(audioData)
            }
            else {
                audioManager.processAudioData(data)
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理二进制消息失败: ${e.message}")
        }
    }

    // 视频解码相关
    private fun decodeFrame(data: ByteArray) {
        if (data.size <= 4) return
        
        val nalType = getNalType(data)
        
        when (nalType) {
            7 -> {
                videoDecoderManager.handleSPS(data)
                updateResolutionFromSPS(data)
            }
            8 -> videoDecoderManager.handlePPS(data)
            5 -> videoDecoderManager.handleKeyFrame(data)
            else -> videoDecoderManager.handleRegularFrame(data)
        }
    }

    private fun getNalType(data: ByteArray): Int {
        return when {
            data.size >= 5 && data[0] == 0x00.toByte() && data[1] == 0x00.toByte() &&
            data[2] == 0x00.toByte() && data[3] == 0x01.toByte() -> 
                (data[4].toInt() and 0x1F)
            data.size >= 4 && data[0] == 0x00.toByte() && data[1] == 0x00.toByte() &&
            data[2] == 0x01.toByte() -> 
                (data[3].toInt() and 0x1F)
            else -> (data[0].toInt() and 0x1F)
        }
    }

    private fun updateResolutionFromSPS(spsData: ByteArray) {
        // 这个函数现在由VideoDecoderManager内部处理，通过onResolutionChanged回调通知
        // 保留这个函数以维持兼容性，但实际逻辑已转移到onResolutionChanged
    }

    // 解码器回调
    private fun onDecoderConfigured() {
        runOnUiThread {
            uiController.hideConnectionInfo()
        }
    }

    private fun onDecodeSuccess() {
        // 解码成功处理
    }

    private fun onDecodeFailure() {
        // 解码失败处理
    }

    private fun onResolutionChanged(width: Int, height: Int) {
        realWidth = width
        realHeight = height
        gotRealResolution = true
        
        Log.d(TAG, "分辨率变化: ${width}x${height}")
        
        runOnUiThread {
            adjustSurfaceViewSize()
        }
        
        touchEventHandler.updateResolution(deviceWidth, deviceHeight, realWidth, realHeight)
    }

    /**
     * 处理运行时分辨率变化（例如从视频流检测到的变化）
     */
    fun handleRuntimeResolutionChange(newWidth: Int, newHeight: Int) {
        videoDecoderManager.handleRuntimeResolutionChange(newWidth, newHeight)
        onResolutionChanged(newWidth, newHeight)
    }

    /**
     * 布局变化回调 - 当UI模式切换时调用
     */
    private fun onLayoutChanged() {
        Log.d(TAG, "UI布局已变化，重新调整SurfaceView尺寸")
        adjustSurfaceViewSize()
    }

    // 音频控制
    private fun increaseVolume() {
        touchEventHandler.sendKeyEvent(24) // KEYCODE_VOLUME_UP
        audioManager.increaseVolume()
    }

    private fun decreaseVolume() {
        touchEventHandler.sendKeyEvent(25) // KEYCODE_VOLUME_DOWN
        audioManager.decreaseVolume()
    }

    // 画质切换
    private fun changeResolution(config: BitrateConfig) {
        try {
            if (isChangingQuality) {
                return
            }
            
            if (!webSocketManager.isConnected()) {
                Toast.makeText(this, "未连接到设备，无法切换画质", Toast.LENGTH_SHORT).show()
                return
            }
            
            isChangingQuality = true
            uiController.showQualityChangeOverlay()
            uiController.updateQualityDisplay(config.name)
            currentBitrateConfig = config
            
            Toast.makeText(this, "正在切换到${config.name}", Toast.LENGTH_SHORT).show()
            
            qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
            qualityChangeTimeoutHandler = Handler(Looper.getMainLooper())
            qualityChangeTimeoutHandler?.postDelayed({
                if (isChangingQuality) {
                    Log.w(TAG, "画质切换超时，强制关闭蒙版")
                    runOnUiThread {
                        uiController.hideQualityChangeOverlay()
                        isChangingQuality = false
                    }
                }
            }, QUALITY_CHANGE_TIMEOUT_MS)
            
            touchEventHandler.sendQualityChangeMessage(config.fps, config.videoBitRate, config.maxSize)
            
        } catch (e: Exception) {
            Log.e(TAG, "更改画质失败", e)
            Toast.makeText(this, "更改画质失败: ${e.message}", Toast.LENGTH_SHORT).show()
            
            isChangingQuality = false
            qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
            runOnUiThread {
                uiController.hideQualityChangeOverlay()
            }
        }
    }

    private fun onQualityChangeSuccess(config: BitrateConfig) {
        currentBitrateConfig = config
        uiController.updateQualityDisplay(config.name)
        uiController.hideQualityChangeOverlay()
        isChangingQuality = false
        qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
        Toast.makeText(this, "画质切换完成", Toast.LENGTH_SHORT).show()
    }

    // 剪贴板处理
    private fun getRemoteClipboardContent() {
        touchEventHandler.getRemoteClipboardContent()
        Toast.makeText(this, "正在获取远程剪贴板...", Toast.LENGTH_SHORT).show()
    }

    private fun setRemoteClipboardContent() {
        if (!clipboardManager.hasPrimaryClip()) {
            Toast.makeText(this, "本地剪贴板为空", Toast.LENGTH_SHORT).show()
            return
        }
        
        val clipData = clipboardManager.primaryClip
        if (clipData == null || clipData.itemCount == 0) {
            Toast.makeText(this, "无法获取剪贴板内容", Toast.LENGTH_SHORT).show()
            return
        }
        
        val item = clipData.getItemAt(0)
        val text = item.text?.toString() ?: ""
        
        if (text.isEmpty()) {
            Toast.makeText(this, "剪贴板内容为空", Toast.LENGTH_SHORT).show() 
            return
        }
        
        touchEventHandler.setRemoteClipboardContent(text)
        Toast.makeText(this, "正在发送到远程剪贴板...", Toast.LENGTH_SHORT).show()
    }

    private fun handleGetClipboardResponse(data: ByteArray) {
        try {
            if (data.size <= 5) {
                Log.w(TAG, "剪贴板响应数据为空")
                runOnUiThread {
                    Toast.makeText(this, "远程剪贴板为空", Toast.LENGTH_SHORT).show()
                }
                return
            }
            
            val clipboardData = ByteArray(data.size - 5)
            System.arraycopy(data, 5, clipboardData, 0, clipboardData.size)
            
            val clipboardText = String(clipboardData, StandardCharsets.UTF_8)
            
            Log.d(TAG, "收到远程剪贴板内容: ${clipboardText.take(50)}${if (clipboardText.length > 50) "..." else ""}")
            
            runOnUiThread {
                val clip = ClipData.newPlainText("远程设备剪贴板", clipboardText)
                clipboardManager.setPrimaryClip(clip)
                Toast.makeText(this, "已复制到本地剪贴板", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理获取剪贴板响应失败", e)
            runOnUiThread {
                Toast.makeText(this, "处理剪贴板内容失败", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun handleSetClipboardResponse(data: ByteArray) {
        try {
            Log.d(TAG, "收到设置剪贴板响应")
            runOnUiThread {
                Toast.makeText(this, "远程剪贴板已更新", Toast.LENGTH_SHORT).show()
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理设置剪贴板响应失败", e)
        }
    }

    // 用户活动
    private fun onUserActivity() {
        lastUserActivityTime = System.currentTimeMillis()
    }

    private fun continueActivity() {
        touchEventHandler.sendUserActiveSignal()
    }

    // 重连
    private fun reconnect() {
        runOnUiThread {
            uiController.showConnectionInfo("正在重新连接...")
            Toast.makeText(this, "正在重新连接...", Toast.LENGTH_SHORT).show()
        }
        
        webSocketManager.reconnect()
    }

    // 退出
    private fun exitActivity() {
        saveTrafficStatsAndDisconnect()
    }

    private fun saveTrafficStatsAndDisconnect() {
        try {
            val (totalTimeSeconds, totalTrafficKB, totalReceivedBytes) = webSocketManager.getTrafficStats()
            
            if (totalTimeSeconds > 0 && userId != 0 && deviceId != 0 && deviceOnlyCode.isNotEmpty()) {
                trafficStatsManager.saveTrafficStats(totalTimeSeconds, totalTrafficKB, totalReceivedBytes)
            } else {
                onTrafficStatsSaved()
            }
        } catch (e: Exception) {
            Log.e(TAG, "保存流量统计失败", e)
            onTrafficStatsSaved()
        }
    }

    private fun onTrafficStatsSaved() {
        webSocketManager.disconnect()
        finish()
    }

    // SurfaceView尺寸调整
    private fun adjustSurfaceViewSize() {
        try {
            if (!gotRealResolution) {
                Log.d(TAG, "尚未获取到准确分辨率，跳过调整SurfaceView尺寸")
                return
            }
            
            if (realWidth <= 0 || realHeight <= 0) {
                Log.e(TAG, "无效的设备分辨率: ${realWidth}x${realHeight}")
                return
            }
            
            Log.d(TAG, "开始调整SurfaceView大小，使用分辨率: ${realWidth}x${realHeight}")
            
            val displayMetrics = resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val screenHeight = displayMetrics.heightPixels
            
            var availableWidth: Int
            var availableHeight: Int
            
            // 全屏模式始终使用屏幕尺寸，不依赖video_layout的测量值
            if (uiController.getCurrentMode() == 1) { // MODE_FULLSCREEN
                availableWidth = screenWidth
                availableHeight = screenHeight
                Log.d(TAG, "全屏模式：使用屏幕尺寸: ${availableWidth}x${availableHeight}")
            } else {
                // 普通模式：优先使用video_layout的实际尺寸
                val videoLayout = findViewById<FrameLayout>(R.id.video_layout)
                availableWidth = videoLayout.width
                availableHeight = videoLayout.height
                
                // 如果video_layout尺寸无效，使用估算尺寸
                if (availableWidth <= 0 || availableHeight <= 0) {
                    val isLandscape = resources.configuration.orientation == Configuration.ORIENTATION_LANDSCAPE
                    
                    if (isLandscape) {
                        val estimatedStatusBarHeight = 120
                        val estimatedLeftNavWidth = 150
                        val estimatedRightControlWidth = 200
                        
                        availableWidth = screenWidth - estimatedLeftNavWidth - estimatedRightControlWidth
                        availableHeight = screenHeight - estimatedStatusBarHeight
                        
                        Log.d(TAG, "横屏普通模式：估算可用区域 = ${availableWidth}x${availableHeight}")
                    } else {
                        val estimatedStatusBarHeight = 120
                        val estimatedBottomNavHeight = 150
                        val estimatedRightControlWidth = 200
                        
                        availableWidth = screenWidth - estimatedRightControlWidth
                        availableHeight = screenHeight - estimatedStatusBarHeight - estimatedBottomNavHeight
                        
                        Log.d(TAG, "竖屏普通模式：估算可用区域 = ${availableWidth}x${availableHeight}")
                    }
                } else {
                    Log.d(TAG, "使用video_layout实际尺寸: ${availableWidth}x${availableHeight}")
                }
            }
            
            if (availableWidth <= 0 || availableHeight <= 0) {
                Log.e(TAG, "计算出的可用区域无效: ${availableWidth}x${availableHeight}")
                return
            }
            
            val videoRatio = realWidth.toFloat() / realHeight.toFloat()
            val screenRatio = availableWidth.toFloat() / availableHeight.toFloat()
            
            var finalWidth: Int
            var finalHeight: Int
            
            if (videoRatio > screenRatio) {
                finalWidth = availableWidth
                finalHeight = (availableWidth / videoRatio).toInt()
            } else {
                finalHeight = availableHeight
                finalWidth = (availableHeight * videoRatio).toInt()
            }
            
            // 全屏模式不缩小，普通模式稍微缩小
            if (uiController.getCurrentMode() == 0) { // MODE_NORMAL
                finalWidth = (finalWidth * 0.98f).toInt()
                finalHeight = (finalHeight * 0.98f).toInt()
                Log.d(TAG, "普通模式缩小2%确保不超出边界")
            } else {
                Log.d(TAG, "全屏模式使用完整尺寸")
            }
            
            Log.d(TAG, "调整SurfaceView尺寸: ${realWidth}x${realHeight} -> ${finalWidth}x${finalHeight}")
            
            val layoutParams = surfaceView.layoutParams
            layoutParams.width = finalWidth
            layoutParams.height = finalHeight
            surfaceView.layoutParams = layoutParams
            surfaceView.requestLayout()
            
        } catch (e: Exception) {
            Log.e(TAG, "调整SurfaceView尺寸失败", e)
        }
    }

    // 配置变更处理
    override fun onConfigurationChanged(newConfig: Configuration) {
        super.onConfigurationChanged(newConfig)
        
        Log.d(TAG, "配置变更: orientation=${newConfig.orientation}")
        
        // 传递正确的Configuration方向常量
        uiController.switchNavigationLayout(newConfig.orientation)
        
        // 不需要额外的延迟调用，因为switchNavigationLayout内部已经有异步回调
        // Handler(Looper.getMainLooper()).postDelayed({
        //     adjustSurfaceViewSize()
        // }, 200)
    }

    // 生命周期管理
    override fun onPause() {
        super.onPause()
        Log.d(TAG, "Activity暂停")
        wasInBackground = true
    }
    
    override fun onStop() {
        super.onStop()
        Log.d(TAG, "Activity停止")
        
        if (!isFinishing) {
            webSocketManager.disconnect()
        }
        
        wasInBackground = true
    }
    
    override fun onResume() {
        super.onResume()
        
        if (wasInBackground) {
            Log.d(TAG, "应用从后台切回前台，启动重连")
            webSocketManager.resetConnectionAttempts()
            wasInBackground = false
        } else {
            if (!webSocketManager.isConnected()) {
                Log.d(TAG, "检测到WebSocket连接异常，尝试重连")
                webSocketManager.reconnect()
            }
        }
    }

    override fun onDestroy() {
        Log.d(TAG, "Activity销毁")
        
        if (webSocketManager.isConnected()) {
            saveTrafficStatsAndDisconnect()
        } else {
            webSocketManager.disconnect()
        }
        
        videoDecoderManager.release()
        touchEventHandler.release()
        audioManager.release()
        uiController.release()
        
        qualityChangeTimeoutHandler?.removeCallbacksAndMessages(null)
        
        super.onDestroy()
    }

    // 权限请求结果处理
    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 100) {
            if (grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "存储权限已授予")
            } else {
                Toast.makeText(this, "截图功能需要存储权限", Toast.LENGTH_SHORT).show()
            }
        }
    }
}
