<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#F5F5F5">

    <!-- 顶部导航栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="#FFFFFF"
        android:elevation="2dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_arrow_back"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:padding="4dp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="选择云手机"
            android:textColor="#333333"
            android:textSize="18sp"
            android:textStyle="bold" />

        <View
            android:layout_width="24dp"
            android:layout_height="24dp" />

    </LinearLayout>

    <!-- 分组选项卡 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="#FFFFFF"
        app:tabGravity="fill"
        app:tabMode="scrollable"
        app:tabSelectedTextColor="#2196F3"
        app:tabTextColor="#666666"
        app:tabIndicatorColor="#2196F3" />

    <!-- 设备列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="8dp"
        android:paddingHorizontal="16dp" />

    <!-- 底部操作栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#FFFFFF"
        android:elevation="4dp"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/tv_selected_count"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="已选中 0/0 台设备"
            android:textColor="#666666"
            android:textSize="14sp"
            android:layout_marginBottom="12dp" />

        <Button
            android:id="@+id/btn_confirm"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/button_primary"
            android:text="重启设备"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold"
            android:enabled="false" />

    </LinearLayout>

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

</LinearLayout>