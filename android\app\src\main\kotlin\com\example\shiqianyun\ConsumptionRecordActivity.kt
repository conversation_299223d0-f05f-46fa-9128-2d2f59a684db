package com.example.shiqianyun

import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.example.shiqianyun.adapter.ConsumptionRecordAdapter
import com.example.shiqianyun.network.RetrofitManager
import com.example.shiqianyun.network.model.ConsumptionRecord
import com.example.shiqianyun.network.model.ConsumptionRecordRequest
import com.example.shiqianyun.network.model.ConsumptionRecordResponse
import com.example.shiqianyun.network.model.SearchCondition
import com.example.shiqianyun.network.model.BaseResponse
import com.example.shiqianyun.network.model.VipLevel
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class ConsumptionRecordActivity : AppCompatActivity() {
    
    private lateinit var recyclerView: RecyclerView
    private lateinit var adapter: ConsumptionRecordAdapter
    private lateinit var emptyStateView: TextView
    private lateinit var spinnerFilter: Spinner
    private lateinit var etSearch: EditText
    private lateinit var btnSearch: Button
    private lateinit var paginationLayout: LinearLayout
    private lateinit var btnPrevPage: TextView
    private lateinit var btnNextPage: TextView
    private lateinit var pageNumbersLayout: LinearLayout
    
    private var recordsList = mutableListOf<ConsumptionRecord>()
    private var currentPage = 1
    private var totalPages = 1
    private var pageSize = 10
    private var currentPayWay: String? = null
    private var currentSearchText: String? = null
    
    // 添加VIP等级列表
    private var vipLevelsList = mutableListOf<VipLevel>()

    companion object {
        private const val TAG = "ConsumptionRecord"
        private const val MAX_VISIBLE_PAGES = 5 // 最多显示5个页码按钮
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val layoutId = resources.getIdentifier("activity_consumption_record", "layout", packageName)
        setContentView(layoutId)
        
        initViews()
        setupListeners()
        setupRecyclerView()
        
        // 先获取VIP等级，然后再获取消费记录
        fetchVipLevels()
    }
    
    private fun initViews() {
        recyclerView = findViewById(resources.getIdentifier("rv_consumption_records", "id", packageName))
        emptyStateView = findViewById(resources.getIdentifier("tv_empty_state", "id", packageName))
        spinnerFilter = findViewById(resources.getIdentifier("spinner_filter", "id", packageName))
        etSearch = findViewById(resources.getIdentifier("et_search", "id", packageName))
        btnSearch = findViewById(resources.getIdentifier("btn_search", "id", packageName))
        paginationLayout = findViewById(resources.getIdentifier("pagination_layout", "id", packageName))
        btnPrevPage = findViewById(resources.getIdentifier("btn_prev_page", "id", packageName))
        btnNextPage = findViewById(resources.getIdentifier("btn_next_page", "id", packageName))
        pageNumbersLayout = findViewById(resources.getIdentifier("page_numbers_layout", "id", packageName))
        
        // 初始化返回按钮
        findViewById<ImageView>(resources.getIdentifier("iv_back", "id", packageName)).setOnClickListener {
            finish()
        }

        // 设置分页按钮点击事件
        btnPrevPage.setOnClickListener {
            if (currentPage > 1) {
                currentPage--
                fetchConsumptionRecords()
            }
        }
        
        btnNextPage.setOnClickListener {
            if (currentPage < totalPages) {
                currentPage++
                fetchConsumptionRecords()
            }
        }
    }
    
    private fun setupListeners() {
        // 筛选下拉框监听
        spinnerFilter.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                currentPayWay = when(position) {
                    0 -> null // 全部
                    1 -> "activateCode" // 激活码新增
                    2 -> "activateRenewPay" // 激活码续费
                    3 -> "translatePhone" // 转移云机
                    4 -> "wechat" // 微信支付
                    5 -> "zhifubao" // 支付宝
                    else -> null
                }
                currentPage = 1 // 重置为第一页
                fetchConsumptionRecords()
            }
            
            override fun onNothingSelected(parent: AdapterView<*>?) {
                // 不处理
            }
        }
        
        // 搜索按钮监听
        btnSearch.setOnClickListener {
            val searchText = etSearch.text.toString().trim()
            currentSearchText = if (searchText.isEmpty()) null else searchText
            currentPage = 1 // 重置为第一页
            fetchConsumptionRecords()
        }
    }
    
    private fun setupRecyclerView() {
        adapter = ConsumptionRecordAdapter(recordsList, vipLevelsList)
        recyclerView.layoutManager = LinearLayoutManager(this)
        recyclerView.adapter = adapter
    }

    // 添加获取VIP等级的方法
    private fun fetchVipLevels() {
        RetrofitManager.apiService.getVipLevels().enqueue(object : Callback<BaseResponse<List<VipLevel>>> {
            override fun onResponse(call: Call<BaseResponse<List<VipLevel>>>, response: Response<BaseResponse<List<VipLevel>>>) {
                if (response.isSuccessful) {
                    val vipResponse = response.body()
                    if (vipResponse != null && vipResponse.code == 200) {
                        vipLevelsList.clear()
                        vipLevelsList.addAll(vipResponse.data)
                        // 获取VIP等级后再获取消费记录
                        fetchConsumptionRecords()
                    } else {
                        // VIP等级获取失败，仍然获取消费记录
                        fetchConsumptionRecords()
                    }
                } else {
                    // 请求失败，仍然获取消费记录
                    fetchConsumptionRecords()
                }
            }

            override fun onFailure(call: Call<BaseResponse<List<VipLevel>>>, t: Throwable) {
                Log.e(TAG, "获取VIP等级失败", t)
                // 请求失败，仍然获取消费记录
                fetchConsumptionRecords()
            }
        })
    }
    
    private fun createPageButton(pageNum: Int): TextView {
        val pageButton = TextView(this)
        
        // 创建无缝的按钮
        val params = LinearLayout.LayoutParams(80, ViewGroup.LayoutParams.MATCH_PARENT)
        // 添加1px的负边距，确保按钮之间无缝连接
        if (pageNum > 0) {
            params.marginStart = -1
        }
        pageButton.layoutParams = params
        
        pageButton.text = pageNum.toString()
        pageButton.textSize = 18f
        pageButton.gravity = android.view.Gravity.CENTER
        pageButton.setPadding(0, 0, 0, 0)
        
        // 设置当前页高亮
        if (pageNum == currentPage) {
            pageButton.setTextColor(android.graphics.Color.WHITE)
            pageButton.setBackgroundColor(android.graphics.Color.parseColor("#007BFF"))
        } else {
            pageButton.setTextColor(android.graphics.Color.parseColor("#007BFF"))
            pageButton.setBackgroundColor(android.graphics.Color.parseColor("#F2F2F2"))
        }
        
        // 设置点击事件
        pageButton.setOnClickListener {
            if (pageNum != currentPage) {
                currentPage = pageNum
                fetchConsumptionRecords()
            }
        }
        
        return pageButton
    }
    
    // 确保两个按钮相邻时没有缝隙
    private fun setupPagination(currentPage: Int, totalPages: Int) {
        this.currentPage = currentPage
        this.totalPages = totalPages
        
        // 更新按钮状态
        btnPrevPage.isEnabled = currentPage > 1
        btnNextPage.isEnabled = currentPage < totalPages
        
        // 设置按钮透明度来表示可用状态
        btnPrevPage.alpha = if (currentPage > 1) 1.0f else 0.5f
        btnNextPage.alpha = if (currentPage < totalPages) 1.0f else 0.5f
        
        // 清除现有的页码按钮
        pageNumbersLayout.removeAllViews()
        
        // 计算要显示的页码范围
        val pageRange = calculateVisiblePageRange(currentPage, totalPages)
        
        // 创建并添加页码按钮
        for (i in pageRange.first..pageRange.second) {
            val pageButton = createPageButton(i)
            pageNumbersLayout.addView(pageButton)
        }
    }
    
    private fun calculateVisiblePageRange(currentPage: Int, totalPages: Int): Pair<Int, Int> {
        // 如果总页数小于等于最大显示页数，则全部显示
        if (totalPages <= MAX_VISIBLE_PAGES) {
            return Pair(1, totalPages)
        }
        
        // 当前页在前半部分
        if (currentPage <= MAX_VISIBLE_PAGES / 2 + 1) {
            return Pair(1, MAX_VISIBLE_PAGES)
        }
        
        // 当前页在后半部分
        if (currentPage >= totalPages - MAX_VISIBLE_PAGES / 2) {
            return Pair(totalPages - MAX_VISIBLE_PAGES + 1, totalPages)
        }
        
        // 当前页在中间
        val start = currentPage - MAX_VISIBLE_PAGES / 2
        val end = currentPage + MAX_VISIBLE_PAGES / 2
        return Pair(start, end)
    }
    
    private fun fetchConsumptionRecords() {
        val userId = getUserId()
        
        // 构建搜索条件
        val searchCondition = if (currentSearchText != null) {
            SearchCondition(currentSearchText)
        } else {
            null
        }
        
        // 构建请求体
        val request = ConsumptionRecordRequest(
            userId = userId,
            page = currentPage,
            pageSize = pageSize,
            payWay = currentPayWay,
            where = searchCondition
        )
        
        // 显示加载中
        showLoading()
        
        RetrofitManager.apiService.getUserConsumptionRecords(request).enqueue(object : Callback<ConsumptionRecordResponse> {
            override fun onResponse(call: Call<ConsumptionRecordResponse>, response: Response<ConsumptionRecordResponse>) {
                hideLoading()
                
                if (response.isSuccessful) {
                    val recordResponse = response.body()
                    if (recordResponse != null && recordResponse.code == 200) {
                        recordsList.clear()
                        
                        // 检查列表是否为 null，为 null 则显示空状态
                        if (recordResponse.data.list != null) {
                            recordsList.addAll(recordResponse.data.list)
                        }
                        
                        adapter.notifyDataSetChanged()
                        
                        // 更新分页信息
                        currentPage = recordResponse.data.page
                        pageSize = recordResponse.data.pageSize
                        // 计算总页数（向上取整）
                        val total = recordResponse.data.total
                        totalPages = if (total == 0) 1 else (total + pageSize - 1) / pageSize
                        
                        // 设置分页控件
                        setupPagination(currentPage, totalPages)
                        
                        // 如果列表为空，显示空状态
                        if (recordsList.isEmpty()) {
                            showEmptyState()
                            paginationLayout.visibility = View.GONE
                        } else {
                            hideEmptyState()
                            paginationLayout.visibility = View.VISIBLE
                        }
                    } else {
                        Log.e(TAG, "获取消费记录失败: ${recordResponse?.msg}")
                        Toast.makeText(this@ConsumptionRecordActivity, "获取消费记录失败", Toast.LENGTH_SHORT).show()
                        showEmptyState()
                        paginationLayout.visibility = View.GONE
                    }
                } else {
                    Log.e(TAG, "获取消费记录失败: ${response.code()}")
                    Toast.makeText(this@ConsumptionRecordActivity, "获取消费记录失败", Toast.LENGTH_SHORT).show()
                    showEmptyState()
                    paginationLayout.visibility = View.GONE
                }
            }

            override fun onFailure(call: Call<ConsumptionRecordResponse>, t: Throwable) {
                hideLoading()
                Log.e(TAG, "获取消费记录失败", t)
                Toast.makeText(this@ConsumptionRecordActivity, "网络错误，请重试", Toast.LENGTH_SHORT).show()
                showEmptyState()
                paginationLayout.visibility = View.GONE
            }
        })
    }
    
    private fun getUserId(): Int {
        val sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        return sharedPreferences.getInt("user_id", 0)
    }
    
    private fun showEmptyState() {
        emptyStateView.visibility = View.VISIBLE
        recyclerView.visibility = View.GONE
    }
    
    private fun hideEmptyState() {
        emptyStateView.visibility = View.GONE
        recyclerView.visibility = View.VISIBLE
    }
    
    private fun showLoading() {
        // 这里可以添加加载动画
    }
    
    private fun hideLoading() {
        // 这里可以隐藏加载动画
    }
} 