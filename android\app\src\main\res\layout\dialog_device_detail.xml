<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_round_background">

    <!-- 标题栏 -->
    <LinearLayout
        android:id="@+id/layout_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="设备详情"
            android:textColor="#333333"
            android:textSize="16sp"
            android:textStyle="bold"/>

        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_close"
            android:contentDescription="关闭"/>
    </LinearLayout>

    <!-- 设备即将到期警告 -->
    <LinearLayout
        android:id="@+id/layout_expire_warning"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="#FF5252"
        android:padding="16dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/layout_title">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="设备即将过期"
            android:textColor="#FFFFFF"
            android:textSize="14sp"/>

        <TextView
            android:id="@+id/btn_renew_warning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="去续费 >"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:padding="4dp"/>
    </LinearLayout>

    <!-- 设备信息区域 -->
    <LinearLayout
        android:id="@+id/layout_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintTop_toBottomOf="@id/layout_expire_warning">

        <!-- 设备名称 - 等级 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/tv_device_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="我的设备"
                android:textColor="#333333"
                android:textSize="18sp"
                android:textStyle="bold"/>

            <TextView
                android:id="@+id/tv_device_level"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="V7"
                android:textColor="#FFB300"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginStart="8dp"/>
        </LinearLayout>

        <!-- 设备ID -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="ID"
                android:textColor="#999999"
                android:textSize="14sp"
                android:background="#EEEEEE"
                android:paddingHorizontal="8dp"
                android:paddingVertical="4dp"/>

            <TextView
                android:id="@+id/tv_device_id"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="1713469"
                android:textColor="#666666"
                android:textSize="14sp"
                android:layout_marginStart="8dp"/>

            <ImageView
                android:id="@+id/btn_copy_id"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_copy"
                android:layout_marginStart="8dp"
                android:contentDescription="复制"/>
        </LinearLayout>

        <!-- 设备剩余时间 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设备剩余时长："
                android:textColor="#666666"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/tv_remaining_time"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="23时59分"
                android:textColor="#666666"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/btn_renew_now"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="立即续费"
                android:textColor="#FF5252"
                android:textSize="14sp"
                android:padding="4dp"/>
        </LinearLayout>

        <!-- 节点信息 -->
        <LinearLayout
            android:id="@+id/layout_node_info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="16dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="机房位置："
                android:textColor="#666666"
                android:textSize="14sp"/>

            <TextView
                android:id="@+id/tv_node_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="加载中..."
                android:textColor="#666666"
                android:textSize="14sp"/>
        </LinearLayout>
    </LinearLayout>

    <View
        android:id="@+id/divider"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#EEEEEE"
        app:layout_constraintTop_toBottomOf="@id/layout_info"/>

    <!-- 功能按钮区域 -->
    <LinearLayout
        android:id="@+id/layout_functions"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        app:layout_constraintTop_toBottomOf="@id/divider">

        <!-- 上传文件 -->
        <LinearLayout
            android:id="@+id/btn_upload"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_upload"
                android:contentDescription="上传文件"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="上传文件"
                android:textColor="#666666"
                android:textSize="12sp"
                android:layout_marginTop="4dp"/>
        </LinearLayout>

        <!-- 修改名称 -->
        <LinearLayout
            android:id="@+id/btn_rename"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_edit"
                android:contentDescription="修改名称"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="修改名称"
                android:textColor="#666666"
                android:textSize="12sp"
                android:layout_marginTop="4dp"/>
        </LinearLayout>

        <!-- 重启设备 -->
        <LinearLayout
            android:id="@+id/btn_restart"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_power"
                android:contentDescription="重启设备"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="重启设备"
                android:textColor="#666666"
                android:textSize="12sp"
                android:layout_marginTop="4dp"/>
        </LinearLayout>

        <!-- 恢复出厂 -->
        <LinearLayout
            android:id="@+id/btn_restore"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center"
            android:padding="8dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_restore"
                android:contentDescription="恢复出厂"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="恢复出厂"
                android:textColor="#666666"
                android:textSize="12sp"
                android:layout_marginTop="4dp"/>
        </LinearLayout>
    </LinearLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 