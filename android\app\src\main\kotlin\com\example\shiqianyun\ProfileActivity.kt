package com.example.shiqianyun

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import com.example.shiqianyun.R
import com.example.shiqianyun.network.ApiService
import com.example.shiqianyun.network.model.UserProfile
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response

class ProfileActivity : AppCompatActivity() {
    
    private lateinit var tvUsername: TextView
    private lateinit var tvUserId: TextView
    private lateinit var tvBalance: TextView
    
    companion object {
        private const val TAG = "ProfileActivity"
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_profile)
        
        // 初始化视图
        initViews()
        
        // 设置点击事件
        setupClickListeners()
        
        // 设置底部导航点击
        setupBottomNavigation()
        
        // 加载个人信息
        loadUserProfile()
    }
    
    private fun initViews() {
        tvUsername = findViewById(R.id.tv_username)
        tvUserId = findViewById(R.id.tv_user_id)
        tvBalance = findViewById(R.id.tv_balance)
    }
    
    private fun setupClickListeners() {
        // 购买设备
        findViewById<android.view.View>(R.id.btn_buy_device).setOnClickListener {
            val intent = Intent(this, PurchaseActivity::class.java)
            startActivity(intent)
        }
        
        // 续费设备
        findViewById<android.view.View>(R.id.btn_renew_device).setOnClickListener {
            val intent = Intent(this, BatchRenewActivity::class.java)
            startActivity(intent)
        }
        
        // 升级设备
        // findViewById<android.view.View>(R.id.btn_upgrade_device).setOnClickListener {
        //     Toast.makeText(this, "升级设备功能开发中", Toast.LENGTH_SHORT).show()
        // }
        
        // 个人网盘
        findViewById<android.view.View>(R.id.btn_cloud_disk).setOnClickListener {
            val intent = Intent(this, CloudDiskActivity::class.java)
            startActivity(intent)
        }
        
        // 消费记录
        findViewById<android.view.View>(R.id.layout_consumption_record).setOnClickListener {
            val intent = Intent(this, ConsumptionRecordActivity::class.java)
            startActivity(intent)
        }
        
        // 激活码
        findViewById<android.view.View>(R.id.layout_activation_code).setOnClickListener {
            val intent = Intent(this, ActivationCodeActivity::class.java)
            startActivity(intent)
        }
        
        // 转移云手机
        findViewById<android.view.View>(R.id.layout_transfer_cloud_phone).setOnClickListener {
            val intent = Intent(this, DeviceTransferActivity::class.java)
            startActivity(intent)
        }
        
        // 获取邀请链接
        // findViewById<android.view.View>(R.id.layout_invitation_link).setOnClickListener {
        //     Toast.makeText(this, "获取邀请链接功能开发中", Toast.LENGTH_SHORT).show()
        // }
        
        // QQ交流群
        findViewById<android.view.View>(R.id.layout_qq_group).setOnClickListener {
            // 复制QQ群号到剪贴板
            val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
            val clip = android.content.ClipData.newPlainText("QQ群号", "4817010")
            clipboard.setPrimaryClip(clip)
            Toast.makeText(this, "QQ群号已复制到剪贴板", Toast.LENGTH_SHORT).show()
        }
        
        // 在线客服
            // findViewById<android.view.View>(R.id.layout_customer_service).setOnClickListener {
            //     Toast.makeText(this, "客服功能开发中", Toast.LENGTH_SHORT).show()
            // }
        
        // 设置
        findViewById<android.view.View>(R.id.layout_settings).setOnClickListener {
            Toast.makeText(this, "设置功能开发中", Toast.LENGTH_SHORT).show()
        }
        
        // 退出登录
        findViewById<android.view.View>(R.id.btn_logout).setOnClickListener {
            // 清除用户ID
            val sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
            sharedPreferences.edit().remove("user_id").apply()
            
            // 显示退出提示
            Toast.makeText(this, "已退出登录", Toast.LENGTH_SHORT).show()
            
            // 跳转到登录界面
            val intent = Intent(this, LoginActivity::class.java)
            // 清除返回栈，防止用户按返回键回到个人页面
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(intent)
            finish()
        }
    }
    
    private var lastBackPressTime: Long = 0
    
    override fun onBackPressed() {
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastBackPressTime > 2000) { // 2秒内点击两次
            Toast.makeText(this, "再按一次退出应用", Toast.LENGTH_SHORT).show()
            lastBackPressTime = currentTime
        } else {
            super.onBackPressed()
        }
    }
    
    private fun setupBottomNavigation() {
        // 设置底部导航栏点击事件
        findViewById<android.view.View>(R.id.nav_home).setOnClickListener {
            // 跳转到设备列表页面
            val intent = Intent(this, DeviceListActivity::class.java)
            // 清除返回栈，防止用户按返回键回到个人页面
            intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            // 添加无动画标志
            intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
            startActivity(intent)
            // 关闭当前页面时也不要动画
            overridePendingTransition(0, 0)
            finish()
        }
        
        findViewById<android.view.View>(R.id.nav_discovery).setOnClickListener {
            // 跳转到购买页面，不销毁当前页面
            val intent = Intent(this, PurchaseActivity::class.java)
            startActivity(intent)
        }
        
        // 当前已在个人页面，不需要处理nav_profile的点击
    }
    
    /**
     * 获取用户ID
     */
    private fun getUserId(): Int {
        val sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        return sharedPreferences.getInt("user_id", 0)
    }
    
    /**
     * 获取用户手机号
     */
    private fun getUserPhone(): String {
        val sharedPreferences = getSharedPreferences("app_prefs", Context.MODE_PRIVATE)
        val userId = sharedPreferences.getInt("user_id", 0)
        
        // 如果没有用户ID，说明未登录
        if (userId == 0) {
            return "未登录"
        }
        
        // 获取保存的手机号
        val phone = sharedPreferences.getString("phone", null)
        
        // 如果有手机号就返回手机号，否则返回用户ID
        return if (!phone.isNullOrEmpty()) {
            phone
        } else {
            "用户$userId"
        }
    }
    
    private fun loadUserProfile() {
        // 从本地存储获取用户ID和手机号
        val userId = getUserId()
        val userPhone = getUserPhone()
        
        Log.d(TAG, "加载用户信息: userId=$userId, userPhone=$userPhone")
        
        // 更新UI显示
        if (userId == 0) {
            // 未登录状态
            tvUsername.text = "未登录"
            tvUserId.text = "UID: --"
        } else {
            // 已登录状态
            tvUsername.text = userPhone
            tvUserId.text = "UID: $userId"
        }
        tvBalance.text = "0.00元"
        
        // 实际应用中的API调用示例
        /*
        RetrofitManager.apiService.getUserProfile().enqueue(object : Callback<BaseResponse<UserProfile>> {
            override fun onResponse(call: Call<BaseResponse<UserProfile>>, response: Response<BaseResponse<UserProfile>>) {
                if (response.isSuccessful) {
                    val baseResponse = response.body()
                    if (baseResponse != null && baseResponse.code == 200) {
                        val profile = baseResponse.data
                        profile?.let {
                            tvUsername.text = "用户: ${it.phone}"
                            tvUserId.text = "UID: ${it.userId}"
                            tvBalance.text = "${it.balance}元"
                        }
                    } else {
                        Log.e(TAG, "获取用户信息失败: ${baseResponse?.msg}")
                        Toast.makeText(this@ProfileActivity, "获取用户信息失败", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Log.e(TAG, "获取用户信息失败: ${response.code()}")
                    Toast.makeText(this@ProfileActivity, "获取用户信息失败", Toast.LENGTH_SHORT).show()
                }
            }
            
            override fun onFailure(call: Call<BaseResponse<UserProfile>>, t: Throwable) {
                Log.e(TAG, "获取用户信息失败", t)
                Toast.makeText(this@ProfileActivity, "网络错误，请重试", Toast.LENGTH_SHORT).show()
            }
        })
        */
    }
} 