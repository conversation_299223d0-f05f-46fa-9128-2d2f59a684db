package com.example.shiqianyun.manager

import android.os.Handler
import android.os.Looper
import android.util.Log
import org.java_websocket.client.WebSocketClient
import org.java_websocket.handshake.ServerHandshake
import org.json.JSONObject
import java.net.URI
import java.nio.ByteBuffer
import java.util.concurrent.atomic.AtomicBoolean

class WebSocketManager(
    private val onConnected: () -> Unit,
    private val onDisconnected: (code: Int, reason: String?, remote: Boolean) -> Unit,
    private val onError: (ex: Exception?) -> Unit,
    private val onTextMessage: (message: String) -> Unit,
    private val onBinaryMessage: (data: ByteArray) -> Unit
) {
    companion object {
        private const val TAG = "WebSocketManager"
        private const val CONNECTION_TIMEOUT_MS = 10000
        private const val MAX_CONNECTION_ATTEMPTS = 2
        private const val MAX_RECONNECTION_ATTEMPTS = 3
        private const val LATENCY_MEASUREMENT_INTERVAL = 5000L
    }

    // WebSocket相关
    private var wsClient: WebSocketClient? = null
    private var audioWsClient: WebSocketClient? = null
    private var wsUrl: String = ""
    
    // 连接状态
    private val isConnected = AtomicBoolean(false)
    private val isReconnecting = AtomicBoolean(false)
    
    // 连接管理
    private var connectionAttempts = 0
    private var reconnectionAttempts = 0
    private var connectionTimeoutHandler: Handler? = null
    
    // 延迟测量
    private var latencyMeasurementHandler: Handler? = null
    private var lastLatencyRequestTime: Long = 0
    
    // 流量统计
    private var connectionStartTime: Long = 0
    private var totalReceivedBytes: Long = 0

    /**
     * 连接WebSocket
     */
    fun connect(url: String) {
        if (isConnected.get() || isReconnecting.get()) {
            return
        }

        try {
            isReconnecting.set(true)
            wsUrl = url
            
            connectionAttempts++
            Log.d(TAG, "尝试连接 #$connectionAttempts")

            wsClient?.close()
            wsClient = null
            audioWsClient?.close()
            audioWsClient = null

            val finalUrl = if (!url.startsWith("ws://") && !url.startsWith("wss://")) {
                "ws://$url"
            } else {
                url
            }

            // 设置连接超时计时器
            connectionTimeoutHandler = Handler(Looper.getMainLooper())
            connectionTimeoutHandler?.postDelayed({
                if (!isConnected.get()) {
                    Log.e(TAG, "WebSocket连接超时")
                    
                    wsClient?.close()
                    wsClient = null
                    audioWsClient?.close()
                    audioWsClient = null
                    isReconnecting.set(false)
                    
                    onError(Exception("连接超时"))
                }
            }, CONNECTION_TIMEOUT_MS.toLong())

            wsClient = createWebSocketClient(finalUrl, false)
            wsClient?.connect()
            
        } catch (e: Exception) {
            Log.e(TAG, "创建WebSocket连接失败: ${e.message}")
            isReconnecting.set(false)
            
            connectionTimeoutHandler?.removeCallbacksAndMessages(null)
            connectionTimeoutHandler = null
            
            onError(e)
        }
    }

    /**
     * 重新连接
     */
    fun reconnect() {
        if (isReconnecting.get()) {
            return
        }

        reconnectionAttempts++
        
        if (reconnectionAttempts > MAX_RECONNECTION_ATTEMPTS) {
            Log.d(TAG, "超过最大重连次数($MAX_RECONNECTION_ATTEMPTS)")
            onError(Exception("重连次数超限"))
            return
        }
        
        Log.d(TAG, "尝试重新连接WebSocket (尝试 $reconnectionAttempts/$MAX_RECONNECTION_ATTEMPTS)")

        wsClient?.close()
        wsClient = null
        isConnected.set(false)

        Handler(Looper.getMainLooper()).postDelayed({
            connect(wsUrl)
        }, 1000)
    }

    /**
     * 断开连接
     */
    fun disconnect() {
        try {
            Log.d(TAG, "正在断开WebSocket连接")
            
            connectionTimeoutHandler?.removeCallbacksAndMessages(null)
            connectionTimeoutHandler = null
            
            stopLatencyMeasurement()
            
            if (wsClient != null) {
                wsClient?.close()
                wsClient = null
            }
            
            if (audioWsClient != null) {
                audioWsClient?.close()
                audioWsClient = null
            }
            
            isConnected.set(false)
            isReconnecting.set(false)
            
        } catch (e: Exception) {
            Log.e(TAG, "断开WebSocket连接失败: ${e.message}")
        }
    }

    /**
     * 发送消息
     */
    fun sendMessage(message: String): Boolean {
        return try {
            if (isConnected.get() && wsClient != null) {
                wsClient?.send(message)
                true
            } else {
                Log.w(TAG, "WebSocket未连接，无法发送消息")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "发送消息失败", e)
            false
        }
    }

    /**
     * 发送二进制数据
     */
    fun sendBinary(data: ByteArray): Boolean {
        return try {
            if (isConnected.get() && wsClient != null) {
                wsClient?.send(data)
                true
            } else {
                Log.w(TAG, "WebSocket未连接，无法发送二进制数据")
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "发送二进制数据失败", e)
            false
        }
    }

    /**
     * 测量延迟
     */
    fun measureLatency() {
        try {
            lastLatencyRequestTime = System.currentTimeMillis()
            
            val pingMessage = JSONObject().apply {
                put("msg_type", 1001)
                put("timestamp", lastLatencyRequestTime)
            }
            
            sendMessage(pingMessage.toString())
            Log.d(TAG, "发送延迟测量请求: timestamp=$lastLatencyRequestTime")
        } catch (e: Exception) {
            Log.e(TAG, "测量延迟失败", e)
        }
    }

    /**
     * 开始定期测量延迟
     */
    private fun startLatencyMeasurement() {
        stopLatencyMeasurement()
        
        latencyMeasurementHandler = Handler(Looper.getMainLooper())
        
        latencyMeasurementHandler?.post(object : Runnable {
            override fun run() {
                if (isConnected.get() && wsClient != null) {
                    measureLatency()
                }
                latencyMeasurementHandler?.postDelayed(this, LATENCY_MEASUREMENT_INTERVAL)
            }
        })
    }

    /**
     * 停止延迟测量
     */
    private fun stopLatencyMeasurement() {
        latencyMeasurementHandler?.removeCallbacksAndMessages(null)
        latencyMeasurementHandler = null
    }

    /**
     * 创建WebSocket客户端
     */
    private fun createWebSocketClient(url: String, isAudio: Boolean): WebSocketClient {
        return object : WebSocketClient(URI(url)) {
            override fun onOpen(handshakedata: ServerHandshake?) {
                if (!isAudio) {
                    connectionTimeoutHandler?.removeCallbacksAndMessages(null)
                    connectionTimeoutHandler = null
                    
                    isConnected.set(true)
                    isReconnecting.set(false)
                    connectionAttempts = 0
                    reconnectionAttempts = 0
                    
                    connectionStartTime = System.currentTimeMillis()
                    totalReceivedBytes = 0
                    
                    startLatencyMeasurement()
                    onConnected()
                }
                
                Log.d(TAG, "${if (isAudio) "音频" else "视频"}WebSocket连接已建立")
            }

            override fun onMessage(message: String?) {
                try {
                    if (message != null) {
                        if (!isAudio) {
                            totalReceivedBytes += message.length
                        }
                        onTextMessage(message)
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "处理WebSocket文本消息失败", e)
                }
            }

            override fun onMessage(bytes: ByteBuffer?) {
                if (bytes == null) return
                
                try {
                    val data = ByteArray(bytes.remaining())
                    bytes.get(data)
                    
                    if (!isAudio) {
                        totalReceivedBytes += data.size
                    }
                    
                    onBinaryMessage(data)
                } catch (e: Exception) {
                    Log.e(TAG, "处理二进制消息失败", e)
                }
            }

            override fun onClose(code: Int, reason: String?, remote: Boolean) {
                if (!isAudio) {
                    connectionTimeoutHandler?.removeCallbacksAndMessages(null)
                    connectionTimeoutHandler = null
                    
                    isConnected.set(false)
                    stopLatencyMeasurement()
                    
                    onDisconnected(code, reason, remote)
                } else {
                    Log.d(TAG, "音频WebSocket连接已关闭: code=$code, reason=$reason, remote=$remote")
                }
            }

            override fun onError(ex: Exception?) {
                if (!isAudio) {
                    connectionTimeoutHandler?.removeCallbacksAndMessages(null)
                    connectionTimeoutHandler = null
                    
                    Log.e(TAG, "视频WebSocket连接错误", ex)
                    onError(ex)
                } else {
                    Log.e(TAG, "音频WebSocket连接错误", ex)
                }
            }
        }.apply {
            setConnectionLostTimeout(5000)
        }
    }

    /**
     * 处理延迟测量响应
     */
    fun handleLatencyResponse(serverTimestamp: Long): Int? {
        return try {
            if (lastLatencyRequestTime > 0) {
                val roundTripLatency = System.currentTimeMillis() - lastLatencyRequestTime
                val serverTimeMs = serverTimestamp * 1000
                val clientToServerLatency = serverTimeMs - lastLatencyRequestTime
                val serverToClientLatency = roundTripLatency - clientToServerLatency
                
                Log.d(TAG, "延迟测量: 往返=${roundTripLatency}ms, 客户端到服务器=${clientToServerLatency}ms, 服务器到客户端=${serverToClientLatency}ms")
                
                roundTripLatency.toInt()
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "处理延迟测量响应失败", e)
            null
        }
    }

    /**
     * 获取连接状态
     */
    fun isConnected(): Boolean = isConnected.get()

    /**
     * 获取流量统计
     */
    fun getTrafficStats(): Triple<Long, Long, Long> {
        val currentTime = System.currentTimeMillis()
        val totalTimeSeconds = if (connectionStartTime > 0) {
            (currentTime - connectionStartTime) / 1000
        } else {
            0L
        }
        val totalTrafficKB = totalReceivedBytes / 1024
        
        return Triple(totalTimeSeconds, totalTrafficKB, totalReceivedBytes)
    }

    /**
     * 重置连接尝试计数
     */
    fun resetConnectionAttempts() {
        connectionAttempts = 0
        reconnectionAttempts = 0
    }
}
