{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/idedManger/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/idedManger/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/idedManger/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/idedManger/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-0050dc4b29beb645acea.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-e3ca7e0560aa1e11cc32.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-d3703c6fe0d72e04120c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-e3ca7e0560aa1e11cc32.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-d3703c6fe0d72e04120c.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-0050dc4b29beb645acea.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}